name: family_management
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ^3.5.4


dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  get: ^4.6.6
  get_storage: ^2.1.1
  dio: ^5.7.0
  get_it: ^8.0.3
  fpdart: ^1.1.1
  livekit_client: ^2.3.2
  livekit_components: ^1.1.0
  fluttertoast: ^8.2.8
  permission_handler: ^11.3.1
  flutter_native_splash: ^2.4.3
  flutter_svg: ^2.0.16
  google_fonts: ^6.2.1
  image_picker: ^1.1.2
  dart_pusher_channels: ^1.2.3
  flutter_keyboard_visibility: ^6.0.0
  cached_network_image: ^3.4.1
  flutter_local_notifications: ^18.0.1
  flutter_callkit_incoming: ^2.5.0
  audioplayers: ^6.1.2
  audio_session: ^0.1.25
  font_awesome_flutter: ^10.8.0
  table_calendar: ^3.1.3
  intl: ^0.19.0
  flutter_background_service: ^5.1.0
  multi_dropdown: ^3.0.1
  percent_indicator: ^4.2.4
  fl_chart: ^0.70.2
  firebase_core: ^3.13.0
  firebase_messaging: ^15.2.5
  audio_waveforms: ^1.3.0
  path_provider: ^2.1.5
  voice_message_player: ^1.1.2
  file_picker: ^10.0.0
  uuid: ^4.5.1
  open_file: ^3.5.10
  flutter_file_downloader: ^2.1.0
  flutter_contacts: ^1.1.9+2
  google_maps_flutter: ^2.12.1
  geolocator: ^14.0.0
  battery_plus: ^6.2.1
  flutter_launcher_icons: ^0.14.3
  carousel_slider: ^5.0.0
  url_launcher: ^6.3.1


dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
      - assets/images/
      - assets/svgs/
      - assets/svgs/drawer/
      - assets/mp3/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
