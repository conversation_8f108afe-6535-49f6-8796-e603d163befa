# Keep all Flutter engine classes
-keep class io.flutter.** { *; }

# Keep flutter_callkit_incoming classes
-keep class com.hiennv.flutter_callkit_incoming.** { *; }

# Keep WorkManager if using background services
-keep class androidx.work.** { *; }

# Keep all @Keep annotated classes
-keep @interface android.support.annotation.Keep
-keep class * { @Keep *; }

# Keep Parcelable classes
-keep class * implements android.os.Parcelable { *; }
-keep class * { *; }

-keep class com.google.android.play.core.splitcompat.SplitCompatApplication { *; }
-keep class com.google.android.play.core.splitinstall.SplitInstallException { *; }
-keep class com.google.android.play.core.splitinstall.SplitInstallManager { *; }
-keep class com.google.android.play.core.splitinstall.SplitInstallManagerFactory { *; }
-keep class com.google.android.play.core.splitinstall.SplitInstallRequest$Builder { *; }
-keep class com.google.android.play.core.splitinstall.SplitInstallRequest { *; }
-keep class com.google.android.play.core.splitinstall.SplitInstallSessionState { *; }
-keep class com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener { *; }
-keep class com.google.android.play.core.tasks.OnFailureListener { *; }
-keep class com.google.android.play.core.tasks.OnSuccessListener { *; }
-keep class com.google.android.play.core.tasks.Task { *; }
-keep class java.beans.ConstructorProperties { *; }
-keep class java.beans.Transient { *; }
-keep class javax.annotation.Nullable { *; }
-keep class javax.annotation.ParametersAreNonnullByDefault { *; }
-keep class org.conscrypt.Conscrypt { *; }
-keep class org.conscrypt.OpenSSLProvider { *; }
-keep class org.w3c.dom.bootstrap.DOMImplementationRegistry { *; }