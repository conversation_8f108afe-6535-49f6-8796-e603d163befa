import 'dart:developer';

import 'package:dio/dio.dart' as dio;
import 'package:family_management/config/urls.dart';
import 'package:family_management/data/datasources/local_datasources/auth_localdatasource.dart';
import 'package:family_management/data/datasources/local_datasources/base_localdatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/achievements_datasource.dart';
import 'package:family_management/data/datasources/remote_datasources/album_remotedatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/area_remotedatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/auth_remotedatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/call_remotedatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/chats_remotedatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/event_remotedatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/family_remotedatasource.dart';
import 'package:family_management/app_service.dart';
import 'package:family_management/data/datasources/remote_datasources/media_remotedatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/polls_remotedatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/shopping_list_remotedatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/task_remotedatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/banners_remotedatasource.dart';
import 'package:family_management/data/services/notifications_service.dart';
import 'package:family_management/domain/repositories/achievements_repository.dart';
import 'package:family_management/domain/repositories/album_repository.dart';
import 'package:family_management/domain/repositories/area_repository.dart';
import 'package:family_management/domain/repositories/auth_repository.dart';
import 'package:family_management/domain/repositories/banners_repository.dart';
import 'package:family_management/domain/repositories/base_repository.dart';
import 'package:family_management/domain/repositories/calls_repository.dart';
import 'package:family_management/domain/repositories/chat_repository.dart';
import 'package:family_management/domain/repositories/event_repository.dart';
import 'package:family_management/domain/repositories/family_repository.dart';
import 'package:family_management/domain/repositories/media_repository.dart';
import 'package:family_management/domain/repositories/poll_repository.dart';
import 'package:family_management/domain/repositories/task_repository.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_it/get_it.dart';
import 'package:get_storage/get_storage.dart';

import 'data/datasources/remote_datasources/expense_remotedatasource.dart';
import 'data/datasources/remote_datasources/notification_remotedatasource.dart';
import 'domain/repositories/expense_repository.dart';
import 'domain/repositories/notification_repository.dart';
import 'domain/repositories/shopping_list_repository.dart';

final gt = GetIt.instance;
final dioClient = dio.Dio();
final FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;

/// Custom Dio logging interceptor for debugging requests and responses
class DioLoggingInterceptor extends dio.Interceptor {
  @override
  void onRequest(
      dio.RequestOptions options, dio.RequestInterceptorHandler handler) {
    log('\n🚀 REQUEST [${options.method}] => ${options.uri}');
    log('Headers: ${options.headers}');
    if (options.data != null) {
      log('Data: ${options.data}');
    }
    if (options.queryParameters.isNotEmpty) {
      log('Query Parameters: ${options.queryParameters}');
    }
    log('─' * 50);

    super.onRequest(options, handler);
  }

  @override
  void onResponse(
      dio.Response response, dio.ResponseInterceptorHandler handler) {
    log('\n✅ RESPONSE [${response.statusCode}] => ${response.requestOptions.uri}');
    log('Data: ${response.data}');
    log('─' * 50);

    super.onResponse(response, handler);
  }

  @override
  void onError(dio.DioException err, dio.ErrorInterceptorHandler handler) {
    if (kDebugMode) {
      log('\n❌ ERROR [${err.response?.statusCode}] => ${err.requestOptions.uri}');
      log('Error Type: ${err.type}');
      log('Error Message: ${err.message}');
      if (err.response != null) {
        log('Response Data: ${err.response?.data}');
      }
      log('─' * 50);
    }
    super.onError(err, handler);
  }
}

Future<void> init() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  await GetStorage.init();
  NotificationsService.initNotifications();
  final options =
      dio.BaseOptions(baseUrl: apiUrl, headers: {'Accept': 'application/json'});
  dioClient.options = options;

  // Add logging interceptor in debug mode
  if (kDebugMode) {
    dioClient.interceptors.add(DioLoggingInterceptor());
  }

  final storage = GetStorage();

  // injecting datasources
  gt.registerFactory(() => BaseLocaldatasource(storage: storage));
  gt.registerFactory(() => BaseRemotedatasource(dio: dioClient));
  gt.registerFactory(() => CallRemotedatasource(dio: dioClient));
  gt.registerFactory(() => AuthRemotedatasource(dio: dioClient));
  gt.registerFactory(() => AuthLocaldatasource(storage: storage));
  gt.registerFactory(() => ChatsRemotedatasource(dio: dioClient));
  gt.registerFactory(() => FamilyRemotedatasource(dio: dioClient));
  gt.registerFactory(() => EventRemotedatasource(dio: dioClient));
  gt.registerFactory(() => TaskRemoteDataSource(dio: dioClient));
  gt.registerFactory(() => AchievementsDatasource(dio: dioClient));
  gt.registerFactory(() => ShoppingListRemoteDataSource(dio: dioClient));
  gt.registerFactory(() => BannersRemotedatasource(dio: dioClient));
  gt.registerFactory(() => PollsRemotedatasource(dio: dioClient));
  gt.registerFactory(() => AlbumRemotedatasource(dio: dioClient));
  gt.registerFactory(() => MediaRemotedatasource(dio: dioClient));
  gt.registerFactory(() => ExpenseRemoteDataSource(dio: dioClient));
  gt.registerFactory(() => NotificationRemoteDataSource(dio: dioClient));
  gt.registerFactory(() => AreaRemotedatasource(dio: dioClient));

  // injecting repositories
  gt.registerSingleton(
      BaseRepository(baseRemotedatasource: gt<BaseRemotedatasource>()));
  gt.registerSingleton(
      CallsRepository(remotedatasource: gt<CallRemotedatasource>()));
  gt.registerSingleton(AuthRepository(
      remotedatasource: gt<AuthRemotedatasource>(),
      localdatasource: gt<AuthLocaldatasource>(),
      baseRemotedatasource: gt<BaseRemotedatasource>()));
  gt.registerSingleton(ChatRepository(
      chatsRemoteDatasource: gt<ChatsRemotedatasource>(),
      baseRemotedatasource: gt<BaseRemotedatasource>()));
  gt.registerSingleton(
      FamilyRepository(remotedatasource: gt<FamilyRemotedatasource>()));
  gt.registerSingleton(
      EventRepository(remotedatasource: gt<EventRemotedatasource>()));
  gt.registerSingleton(TaskRepository(
      remoteDataSource: gt<TaskRemoteDataSource>(),
      baseRemotedatasource: gt<BaseRemotedatasource>()));
  gt.registerSingleton(AchievementsRepository(
      baseRemotedatasource: gt<BaseRemotedatasource>(),
      remoteDatasource: gt<AchievementsDatasource>()));
  gt.registerSingleton(ShoppingListRepository(
      remoteDataSource: gt<ShoppingListRemoteDataSource>(),
      baseRemotedatasource: gt<BaseRemotedatasource>()));
  gt.registerSingleton(PollRepository(
      baseRemotedatasource: gt<BaseRemotedatasource>(),
      remotedatasource: gt<PollsRemotedatasource>()));
  gt.registerSingleton(AlbumRepository(
      baseRemotedatasource: gt<BaseRemotedatasource>(),
      remotedatasource: gt<AlbumRemotedatasource>()));
  gt.registerSingleton(MediaRepository(
    baseRemotedatasource: gt<BaseRemotedatasource>(),
    remotedatasource: gt<MediaRemotedatasource>(),
  ));
  gt.registerSingleton(ExpenseRepository(
    baseRemotedatasource: gt<BaseRemotedatasource>(),
    remoteDataSource: gt<ExpenseRemoteDataSource>(),
  ));
  gt.registerSingleton(NotificationRepository(
    baseRemotedatasource: gt<BaseRemotedatasource>(),
    remoteDataSource: gt<NotificationRemoteDataSource>(),
  ));
  gt.registerSingleton(AreaRepository(
      baseRemotedatasource: gt<BaseRemotedatasource>(),
      remotedatasource: gt<AreaRemotedatasource>()));
  gt.registerSingleton(
      BannersRepository(remotedatasource: gt<BannersRemotedatasource>()));

  Get.put(AppService(), permanent: true);

  FirebaseMessaging.onBackgroundMessage(
      NotificationsService.onBackgroundMessage);

  FirebaseMessaging.onMessage.listen(NotificationsService.onForegroundMessage);
}
