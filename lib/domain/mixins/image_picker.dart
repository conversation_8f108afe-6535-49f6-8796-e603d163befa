import 'dart:developer';
import 'dart:io';

import 'package:family_management/utils/utils.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';

mixin ImagePickerMixin {
  Future<File?> pickFromCamera() async {
    try {
      final picker = ImagePicker();

      final pickedFile = await picker.pickImage(source: ImageSource.camera);
      if (pickedFile == null) return null;

      return File(pickedFile.path);
    } catch (e) {
      Utils.log('pick image from camera error: $e');
      return null;
    }
  }

  Future<File?> pickFromGallery() async {
    try {
      final picker = ImagePicker();

      final pickedFile = await picker.pickImage(source: ImageSource.gallery);
      if (pickedFile == null) return null;

      return File(pickedFile.path);
    } catch (e) {
      Utils.log('pick image from gallery error: $e');
      return null;
    }
  }

  Future<File?> pickFile() async {
    log('picking file');
    final result = await FilePicker.platform.pickFiles(allowMultiple: false);
    log('picked file ${result?.files.first.path}');
    if (result == null) return null;
    return File(result.files.first.path!);
  }
}
