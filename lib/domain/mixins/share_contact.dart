import 'dart:developer';

import 'package:family_management/config/app_routes.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:get/get.dart';

mixin ShareContactMixin {
  final Rx<Contact> _contact = Contact().obs;
  final RxBool _loadingContacts = false.obs;
  final RxList<Contact> contacts = <Contact>[].obs;

  bool get loadingContacts => _loadingContacts.value;
  Contact get contact => _contact.value;

  set contact(Contact value) => _contact.value = value;

  sendContact() async {
    final granted = await Utils.requestContactsPermission();
    log('contact permission $granted');
    if (granted == null) {
      log('contact permission not granted');
      return;
    }
    Get.toNamed(AppRoutes.selectContact);
    final result = await FlutterContacts.getContacts(withProperties: true);

    contacts.value = result;
    contacts.refresh();
  }
}
