import 'dart:developer';

import 'package:family_management/config/urls.dart';
import 'package:family_management/domain/controllers/call_controller.dart';
import 'package:family_management/domain/models/chat.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/calls_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:get/get.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:permission_handler/permission_handler.dart';

mixin CallMixin {
  final callRepository = gt<CallsRepository>();
  final Rx<User> _user = User().obs;
  final RxBool _micOn = false.obs;
  final RxBool _joined = false.obs;
  final RxBool _speakerOn = false.obs;
  final RxBool _leaving = false.obs;
  final RxBool _callOpen = false.obs;
  final RxBool _outgoingCall = false.obs;
  final RxString _callToken = ''.obs;
  final RxList<User> users = <User>[].obs;
  final RxList<User> joinedUsers = <User>[].obs;
  final RxBool _connecting = false.obs;

  bool get speakerOn => _speakerOn.value;
  bool get leaving => _leaving.value;
  bool get callOpen => _callOpen.value;
  bool get joined => _joined.value;
  String get callToken => _callToken.value;
  bool get outgoingCall => _outgoingCall.value;
  bool get micOn => _micOn.value;
  bool get connecting => _connecting.value;
  User get user => _user.value;

  set callOpen(bool value) => _callOpen.value = value;
  set outgoingCall(bool value) => _outgoingCall.value = value;
  set user(User value) => _user.value = value;

  final room = Room(
      roomOptions: const RoomOptions(
          defaultAudioCaptureOptions: AudioCaptureOptions(
    noiseSuppression: true,
  )));

  Future<void> call(Chat chat) async {
    _connecting.value = true;
    final token = await generateToken(chat);
    if (token == null) {
      Utils.showToast("Failed to generate token");
      _connecting.value = false;
      return;
    }
    await room.connect(livekitUrl, token);
    room.localParticipant?.setMicrophoneEnabled(true);
    room.setSpeakerOn(false);
    room.localParticipant?.setCameraEnabled(false);

    // Add room event listeners
    _setupRoomListeners();
    if (!outgoingCall) _callOpen.value = true;

    _micOn.value = true;
    _speakerOn.value = true;
    _connecting.value = false;
  }

  toggleSpeaker() {
    room.remoteParticipants.forEach((key, value) {
      for (final el in value.audioTrackPublications) {
        if (speakerOn) {
          el.subscribe();
        } else {
          el.unsubscribe();
        }
      }
    });
    _speakerOn.value = !speakerOn;
  }

  enableDisableMic() {
    room.localParticipant?.setMicrophoneEnabled(!_micOn.value);
    _micOn.value = !_micOn.value;
  }

  generateToken(Chat chat) async {
    bool granted = false;
    if (await Permission.microphone.status.isGranted &&
        await Permission.camera.status.isGranted) {
      granted = true;
    } else {
      granted = await Permission.microphone.request().isGranted;
      granted = await Permission.camera.request().isGranted;
    }
    if (!granted) {
      Utils.showToast('Please enable mic permission');
      return null;
    }
    String? token;
    final result =
        await callRepository.generateToken(roomName: chat.id.toString());
    result.fold((l) => Utils.showToast(l.message), (r) => token = r);
    return token;
  }

  Future<void> endCall() async {
    _leaving.value = true;
    await room.disconnect();
    _leaving.value = false;
    _callOpen.value = false;
    joinedUsers.clear();
    joinedUsers.refresh();
  }

  /// Setup room event listeners for participant join/leave events
  void _setupRoomListeners() {
    // Listen for participant connected events
    room.addListener(() {
      _onRoomUpdate();
    });
  }

  /// Handle room updates including participant changes
  void _onRoomUpdate() {
    // Update local participant state
    user.talking = room.localParticipant?.isSpeaking ?? false;
    user.mute = room.localParticipant?.isMuted ?? false;

    // Get current remote participant IDs
    final currentParticipantIds = room.remoteParticipants.keys.toSet();

    // Get previously joined user IDs
    final previousParticipantIds =
        joinedUsers.map((u) => u.sid).where((sid) => sid != null).toSet();

    // Check for participants who left
    for (final previousSid in previousParticipantIds) {
      if (!currentParticipantIds.contains(previousSid)) {
        _onParticipantLeft(previousSid!);
      }
    }

    // Update existing participants and add new ones
    room.remoteParticipants.forEach((key, participant) {
      final index =
          users.indexWhere((e) => e.id.toString() == participant.identity);
      if (index == -1) return;

      Utils.log('Updating participant: ${participant.identity}');
      users[index].mute = participant.isMuted;
      users[index].sid = participant.sid;
      users[index].cameraEnabled = participant.isCameraEnabled();
      users[index].talking = participant.isSpeaking;

      if (participant.videoTrackPublications.isNotEmpty) {
        users[index].videoRenderer =
            participant.videoTrackPublications.first.track as VideoTrack;
      }
    });

    joinedUsers.refresh();
    users.refresh();

    try {
      Get.find<CallController>().update();
    } catch (e) {
      log('controller not found ${e.toString()}');
    }
  }

  /// Handle when a participant leaves the call
  void _onParticipantLeft(String participantSid) {
    // Find the user by their session ID
    final leftUser =
        joinedUsers.firstWhereOrNull((user) => user.sid == participantSid);

    if (leftUser != null) {
      Utils.log('Participant left: ${leftUser.name} (ID: ${leftUser.id})');

      // Remove from joined users list
      joinedUsers.removeWhere((user) => user.sid == participantSid);

      // Clear their video renderer
      leftUser.videoRenderer = null;
      leftUser.sid = null;

      Utils.log(
          'Removed ${leftUser.name} from joined users list. Remaining: ${joinedUsers.length}');

      // You can add additional logic here, such as:
      // - Showing a notification that someone left
      // - Playing a sound
      // - Updating UI elements
      // - Checking if the call should end (if no participants left)

      joinedUsers.refresh();
      if (joinedUsers.length == 1) {
        try {
          final callController = Get.find<CallController>();
          callController.stop();
        } catch (e) {
          log('failed to end call');
          log(e.toString());
        }
      }
    } else {
      Utils.log(
          'Participant with SID $participantSid left, but not found in joined users');
    }
  }
}
