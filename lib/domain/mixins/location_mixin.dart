import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:family_management/config/urls.dart';
import 'package:family_management/data/services/location_service.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/calls_repository.dart';
import 'package:family_management/injections.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:livekit_client/livekit_client.dart';

mixin LocationMixin {
  final callRepository = gt<CallsRepository>();
  final RxList<User> users = <User>[].obs;
  final RxString _token = ''.obs;
  final RxList<Marker> markers = <Marker>[].obs;
  late StreamSubscription<Position>? _locationStreamSubscription;

  String get token => _token.value;

  final locationRoom = Room();

  connectLocationRoom() async {
    final token = await LocationService.generateLocationRoomToken();
    await locationRoom.connect(livekitUrl, token);
    locationRoom.localParticipant?.setMicrophoneEnabled(false);
    locationRoom.localParticipant?.setCameraEnabled(false);
    locationListener();
  }

  locationListener() async {
    locationRoom.createListener().on<DataReceivedEvent>((event) {
      log('location recieved');
      final data = event.data;
      final map = jsonDecode(utf8.decode(data));
      log(map.toString());
      final index = users.indexWhere((u) => u.id == map['user_id']);
      if (index > -1) {
        users[index].lat = map['lat'];
        users[index].lng = map['lng'];
        users.refresh();
        updateMarkers();
      }
    });
  }

  updateMarkers() async {
    markers.clear();
    for (final item in users) {
      markers.add(Marker(
          markerId: MarkerId(item.id.toString()),
          position: LatLng(item.lat ?? 0, item.lng ?? 0),
          infoWindow: InfoWindow(title: item.name)));
    }
    markers.refresh();
  }

  disposeListeners() async {
    await _locationStreamSubscription?.cancel();
  }
}
