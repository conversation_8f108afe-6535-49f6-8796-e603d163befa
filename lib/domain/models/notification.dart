class Notification {
  String? title;
  String? body;
  DateTime? createdAt;
  DateTime? sentAt;

  Notification({
    this.title,
    this.body,
    this.createdAt,
    this.sentAt,
  });

  Notification.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    body = json['body'];
    createdAt =
    json['created_at'] != null ? DateTime.parse(json['created_at']) : null;
    sentAt =
    json['sent_at'] != null ? DateTime.parse(json['sent_at']) : null;
  }
}
