class SubTask {
  int? id;
  String? content;
  String? status = 'todo';
  String? priority;
  int? sort;
  bool deleting = false;
  bool editing = false;
  SubTask({this.id, this.content, this.status, this.priority, this.sort});

  SubTask.fromJson(Map json) {
    id = json['id'];
    content = json['content'];
    status = json['status'] ?? 'todo';
    priority = json['priority'];
    sort = json['sort'];
  }
}
