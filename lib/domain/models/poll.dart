import 'package:family_management/domain/models/poll_item.dart';
import 'package:family_management/domain/models/poll_vote.dart';

class Poll {
  int? id;
  String? name;
  List<PollItem> items = [];
  List<PollVote> votes = [];
  int? userId;
  bool? isOpen;

  Poll({this.id, this.name});

  Poll.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    userId = json['user_id'];
    items = json['items'] == null
        ? []
        : <PollItem>[
            ...json['items']?.map((item) => PollItem.fromJson(item)).toList()
          ];
    votes = json['votes'] == null
        ? []
        : <PollVote>[
            ...json['votes']?.map((item) => PollVote.fromJson(item)).toList()
          ];
    isOpen = json['is_open'];
  }

  getTotalVotes() {
    return items.fold(0,
        (previousValue, element) => previousValue + (element.totalVotes ?? 0));
  }
}
