class ShoppingListItem {
  int? id;
  int? shoppingListId;
  String? name;
  int? quantity;
  String? unit;
  bool? isPurchased;
  DateTime? createdAt;
  DateTime? updatedAt;

  ShoppingListItem({
    this.id,
    this.shoppingListId,
    this.name,
    this.quantity,
  });

  ShoppingListItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    shoppingListId = json['shopping_list_id'];
    name = json['name'];
    quantity = json['quantity'];
    unit = json['unit'];
    isPurchased = json['is_purchased'];
    createdAt =
        json['created_at'] != null ? DateTime.parse(json['created_at']) : null;
    updatedAt =
        json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null;
  }
}
