import 'package:family_management/domain/models/budget_chart.dart';
import 'package:family_management/domain/models/expense.dart';

class Budget {
  List<Expense>? expenses;

  BudgetChart? chart;

  Budget({this.expenses, this.chart});

  Budget.fromJson(Map<String, dynamic> data) {
    expenses = data['latest'] != null
        ? List<Expense>.from(data['latest'].map((e) => Expense.fromJson(e)))
        : [];

    chart = data['chart'] != null ? BudgetChart.fromJson(data['chart']) : null;
  }
}
