import 'package:family_management/domain/enums/recurring_pattern.dart';
import 'package:family_management/domain/enums/task_status.dart';
import 'package:family_management/domain/enums/task_type.dart';
import 'package:family_management/domain/models/sub_task.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class Task {
  int? id;
  int? familyId;
  User? createdBy;
  String? title;
  String? description;
  String? status;
  String? priority;
  DateTime? dueDate;
  DateTime? createdAt;
  TaskType? type;
  RecurringPattern? recurringPattern;
  int? recurringInterval;
  List<SubTask>? subTasks;
  List<User>? assignees;
  TaskStatus? taskStatus;

  Task(
      {this.id,
      this.familyId,
      this.createdAt,
      this.createdBy,
      this.description,
      this.dueDate,
      this.priority,
      this.status,
      this.subTasks,
      this.taskStatus,
      this.type,
      this.recurringInterval,
      this.recurringPattern,
      this.assignees,
      this.title});

  Task.formJson(Map json) {
    id = json['id'];
    familyId = json['family_id'];
    title = json['title'];
    description = json['description'];
    createdBy =
        json['created_by'] != null ? User.fromJson(json['created_by']) : null;
    status = json['status'];
    priority = json['priority'];
    dueDate =
        json['due_date'] != null ? DateTime.parse(json['due_date']) : null;
    createdAt =
        json['created_at'] != null ? DateTime.parse(json['created_at']) : null;
    type = json['type'] != null
        ? json['type'] == 'recurring'
            ? TaskType.recurring
            : TaskType.oneTime
        : null;
    recurringPattern = _getRecurringPattern(json['recurring_pattern']);
    recurringInterval = json['recurring_interval'];
    subTasks = json['sub_tasks'] != null
        ? <SubTask>[
            ...json['sub_tasks']?.map((e) => SubTask.fromJson(e)).toList()
          ]
        : [];

    assignees = json['assignees'] != null
        ? <User>[...json['assignees'].map((e) => User.fromJson(e)).toList()]
        : [];
    taskStatus = _getStatus(json['status']);
  }
  _getStatus(String? value) {
    switch (value) {
      case 'todo':
        return TaskStatus.toDo;
      case 'in_progress':
        return TaskStatus.inProgress;
      case 'done':
        return TaskStatus.done;
      default:
        return null;
    }
  }

  _getRecurringPattern(String value) {
    switch (value) {
      case 'daily':
        return RecurringPattern.daily;
      case 'weekly':
        return RecurringPattern.weekly;
      case 'monthly':
        return RecurringPattern.monthly;
      case 'yearly':
        return RecurringPattern.yearly;
      default:
        return null;
    }
  }

  getRecurringTime() {
    switch (recurringPattern) {
      case RecurringPattern.daily:
        return 'every $recurringInterval ${'day/s'.tr}';
      case RecurringPattern.monthly:
        return 'every $recurringInterval ${'month/s'.tr}';
      case RecurringPattern.weekly:
        return 'every $recurringInterval ${'week/s'.tr}';
      case RecurringPattern.yearly:
        return 'every $recurringInterval ${'year/s'.tr}';
      default:
        return '';
    }
  }

  getBadgeColor() {
    switch (taskStatus) {
      case TaskStatus.toDo:
        return Colors.grey;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.done:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
