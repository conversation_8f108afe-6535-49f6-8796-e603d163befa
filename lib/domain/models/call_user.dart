import 'package:family_management/domain/models/user.dart';

class CallUser {
  int? id;
  DateTime? startedAt;
  DateTime? endedAt;
  User? user;

  CallUser({this.id, this.startedAt, this.endedAt, this.user});

  CallUser.fromJson(json) {
    id = json['id'];
    startedAt = json['started_at'] == null
        ? null
        : DateTime.tryParse(json['started_at']);
    endedAt =
        json['closed_at'] == null ? null : DateTime.tryParse(json['closed_at']);
    user = json['user'] != null ? User.fromJson(json['user']) : null;
  }
}
