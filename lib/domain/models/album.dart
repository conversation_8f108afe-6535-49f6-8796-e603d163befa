import 'dart:developer';

import 'package:family_management/domain/models/media.dart';
import 'package:family_management/domain/models/user.dart';

class Album {
  int? id;
  String? name;
  String? thumbnail;
  User? user;
  List<Media> media = [];

  Album({this.id, this.name, this.user});

  Album.fromJson(Map json) {
    log(json['gallery'].toString());
    id = json['id'];
    name = json['name'];
    thumbnail = json['thumbnail'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    media = json['gallery'] != null
        ? <Media>[...json['gallery'].map((e) => Media.fromJson(e)).toList()]
        : [];
  }
}
