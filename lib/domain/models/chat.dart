import 'package:family_management/data/services/local_storage.dart';
import 'package:family_management/domain/models/media.dart';
import 'package:family_management/domain/models/message.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:get/get.dart';

class Chat {
  int? id;
  String? name;
  Media? image;
  List<User> members = [];
  bool? isGroup;
  DateTime? createdAt;
  Message? lastMessage;
  bool? isArchived;

  Chat({this.id, this.name, this.isArchived});
  Chat.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'] != null ? Media.fromJson(json['image']) : null;
    members = <User>[...json['members']?.map((e) => User.fromJson(e)).toList()];
    isGroup = json['is_group'];
    createdAt = DateTime.tryParse(json['created_at']);
    lastMessage = json['last_message'] != null
        ? Message.fromJson(json['last_message'])
        : null;
  }

  getChatImage() {
    if (image != null) {
      return image?.originalUrl;
    }
    final user = LocalStorage.getUser();
    final member = members.firstWhereOrNull((i) => i.id != user?.id);
    if (member?.avatar != null) {
      return member!.avatar;
    }
    return null;
  }

  getChatName() {
    if (isGroup ?? false) return name ?? '';
    final user = LocalStorage.getUser();
    final member = members.firstWhereOrNull((i) => i.id != user?.id);
    if (member?.name != null) {
      return member!.name;
    }
    return '';
  }

  toMap() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'members': members.map((e) => e.toMap()).toList(),
      'is_group': isGroup,
      'created_at': createdAt.toString()
    };
  }
}
