import 'package:family_management/domain/models/call_user.dart';
import 'package:family_management/domain/models/chat.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:get/get_navigation/src/root/parse_route.dart';

class CallLog {
  int? id;
  DateTime? startedAt;
  DateTime? endedAt;
  DateTime? createdAt;
  String? duration;
  List<CallUser> users = [];
  User? user;
  Chat? chat;
  bool? isMissed;

  CallLog({this.id, this.startedAt, this.endedAt});

  CallLog.fromJson(json) {
    id = json['id'];
    startedAt =
        json['started_at'] != null ? DateTime.parse(json['started_at']) : null;
    endedAt =
        json['closed_at'] == null ? null : DateTime.parse(json['closed_at']);
    createdAt =
        json['created_at'] == null ? null : DateTime.parse(json['created_at']);
    duration = json['duration'];
    isMissed = json['is_missed'];
    users = json['call_users'] != null
        ? List<CallUser>.from(
            json['call_users'].map((x) => CallUser.fromJson(x)))
        : [];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    chat = json['chat'] != null ? Chat.fromJson(json['chat']) : null;
  }
  getTitle() {
    if (users.length > 2) {
      return chat != null
          ? chat?.name ?? ''
          : users.map((e) => e.user?.name ?? '').join(', ');
    }
    return users.firstWhereOrNull((r) => r.id != user?.id)?.user?.name ?? '';
  }

  getImage() {
    if (users.length > 2) {
      return chat?.image;
    }
    return users.firstWhereOrNull((r) => r.id != user?.id)?.user?.avatar;
  }
}
