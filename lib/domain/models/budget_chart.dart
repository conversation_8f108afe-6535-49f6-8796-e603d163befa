class BudgetChart {
  List<CategorySummary>? categories;
  TotalChart? total;

  BudgetChart({this.categories, this.total});

  BudgetChart.fromJson(Map<String, dynamic> json) {
    categories = json['categories'] != null
        ? List<CategorySummary>.from(
        json['categories'].map((e) => CategorySummary.fromJson(e)))
        : [];

    total = json['total'] != null ? TotalChart.fromJson(json['total']) : null;
  }
}

class CategorySummary {
  int? totalExpenses;
  int? expenseCategoryId;
  String? name;

  CategorySummary({this.totalExpenses, this.expenseCategoryId, this.name});

  CategorySummary.fromJson(Map<String, dynamic> json) {
    totalExpenses = json['total_expenses'];
    expenseCategoryId = json['expense_category_id'];
    name = json['name'];
  }
}

class TotalChart {
  int? total;

  TotalChart({this.total});

  TotalChart.fromJson(Map<String, dynamic> json) {
    total = json['total'];
  }
}
