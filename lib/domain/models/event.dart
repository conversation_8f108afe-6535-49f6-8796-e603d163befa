import 'package:family_management/domain/enums/event_type.dart';
import 'package:family_management/domain/models/occasion.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:get/get.dart';

class Event {
  int? id;
  String? name;
  String? description;
  DateTime? scheduledAt;
  DateTime? notifyAt;
  EventType? type;
  int? recurringInterval;
  String? recurringPattern;
  String? location;
  User? createdBy;
  Occasion? occasion;
  bool isPassed = false;
  Event(
      {this.id,
      this.name,
      this.description,
      this.location,
      this.recurringInterval,
      this.type,
      this.createdBy,
      this.notifyAt,
      this.recurringPattern,
      this.occasion,
      this.scheduledAt});
  Event.fromJson(Map json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    type = json['type'] == null
        ? null
        : json['type'] == 'one_time'
            ? EventType.oneTime
            : EventType.recurring;
    recurringPattern = json['recurring_pattern'];
    recurringInterval = json['recurring_interval'];
    scheduledAt = json['scheduled_at'] != null
        ? DateTime.parse(json['scheduled_at'])
        : null;
    createdBy = json['created_by'] != null && json['created_by'] is Map
        ? User.fromJson(json['created_by'])
        : null;
    occasion =
        json['occasion'] != null ? Occasion.fromJson(json['occasion']) : null;
    location = json['location'];
    notifyAt = json['notify_time'] != null
        ? DateTime.parse(json['notify_time'])
        : null;
    isPassed = _checkPassed();
  }
  _checkPassed() {
    if (scheduledAt != null && DateTime.now().isAfter(scheduledAt!)) {
      return true;
    }
    return false;
  }

  getNotifyTime() {
    if (scheduledAt == null || notifyAt == null) return '';
    final diff = scheduledAt!.difference(notifyAt!).inMinutes;

    if (diff < 60) {
      return diff.toString() + ' min'.tr;
    }
    if (diff < (60 * 24)) {
      return diff.toString() + ' h'.tr;
    }
    return diff.toString() + ' day'.tr;
  }
}
