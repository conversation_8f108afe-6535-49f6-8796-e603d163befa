import 'package:family_management/domain/enums/shopping_list_type.dart';
import 'package:family_management/domain/models/shopping_list_item.dart';
import 'package:family_management/domain/models/user.dart';

class ShoppingList {
  int? id;
  String? name;
  int? familyId;
  User? createdBy;
  DateTime? createdAt;
  DateTime? updatedAt;
  ShoppingListType? type;
  List<ShoppingListItem> items = [];

  ShoppingList({
    this.id,
    this.name,
    this.familyId,
    this.createdBy,
    this.createdAt,
    this.updatedAt,
  });

  ShoppingList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    familyId = json['family_id'];
    createdBy =
        json['created_by'] != null ? User.fromJson(json['created_by']) : null;
    createdAt =
        json['created_at'] != null ? DateTime.parse(json['created_at']) : null;
    updatedAt =
        json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null;
    items = json['items'] != null
        ? <ShoppingListItem>[
            ...json['items']?.map((e) => ShoppingListItem.fromJson(e)).toList()
          ]
        : [];
    type = _getType(json['type']);
  }
  _getType(value) {
    switch (value) {
      case 1:
        return ShoppingListType.shoppingList;
      case 2:
        return ShoppingListType.mealList;
    }
  }
}
