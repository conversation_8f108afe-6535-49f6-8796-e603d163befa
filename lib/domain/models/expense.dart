import 'package:family_management/domain/models/user.dart';

import '../enums/task_type.dart';
import 'expense_category.dart';

class Expense {
  int? id;
  int? expenseCategoryId;
  int? userId;
  int? amount;
  DateTime? date;
  String? description;
  DateTime? createdAt;
  DateTime? updatedAt;
  ExpenseCategory? expenseCategory;
  User? user;
  TaskType? type;
  bool? canEdit;
  bool? canDelete;
  bool? isOwnExpense;

  Expense({
    this.id,
    this.expenseCategoryId,
    this.userId,
    this.amount,
    this.date,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.expenseCategory,
    this.user,
    this.type,
    this.canEdit,
    this.canDelete,
    this.isOwnExpense,
  });

  Expense.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    expenseCategoryId = json['expense_category_id'];
    userId = json['user_id'];
    amount = json['amount'];
    date = json['date'] != null ? DateTime.parse(json['date']) : null;
    description = json['description'];
    createdAt =
        json['created_at'] != null ? DateTime.parse(json['created_at']) : null;
    updatedAt =
        json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null;
    expenseCategory = json['expense_category'] != null
        ? ExpenseCategory.fromJson(json['expense_category'])
        : null;
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    type = json['type'] == 'recurring' ? TaskType.recurring : TaskType.oneTime;
    canEdit = json['can_edit'];
    canDelete = json['can_delete'];
    isOwnExpense = json['is_own_expense'];
  }
}
