class Profile {
  final int id;
  final String name;
  final String email;
  final String? bio;
  final String timeZone;
  final String createdAt;
  final String updatedAt;

  Profile({
    required this.id,
    required this.name,
    required this.email,
    this.bio,
    required this.timeZone,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل البيانات من JSON إلى نموذج Profile
  factory Profile.fromJson(Map<String, dynamic> json) {
    return Profile(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      bio: json['bio'],
      timeZone: json['time_zone'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  // تحويل نموذج Profile إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'bio': bio,
      'time_zone': timeZone,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}
