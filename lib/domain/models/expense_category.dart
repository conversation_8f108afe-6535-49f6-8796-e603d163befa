class ExpenseCategory {
  int? id;
  int? familyId;
  String? name;
  bool isActive = false;
  DateTime? createdAt;
  DateTime? updatedAt;

  ExpenseCategory({
    this.id,
    this.familyId,
    this.name,
    this.isActive = false,
    this.createdAt,
    this.updatedAt,
  });

  ExpenseCategory.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    familyId = json['family_id'];
    name = json['name'];
    isActive = json['is_active'];
    createdAt =
    json['created_at'] != null ? DateTime.parse(json['created_at']) : null;
    updatedAt =
    json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null;
  }
}
