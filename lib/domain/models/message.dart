import 'package:family_management/data/services/local_storage.dart';
import 'package:family_management/domain/enums/file_message_status.dart';
import 'package:family_management/domain/enums/message_type.dart';
import 'package:family_management/domain/models/chat.dart';
import 'package:family_management/domain/models/media.dart';
import 'package:family_management/domain/models/shared_contact.dart';
import 'package:family_management/domain/models/user.dart';

class Message {
  int? id;
  String? message;
  User? sender;
  int? userId;
  DateTime? createdAt;
  Media? file;
  String? voice;
  int? total;
  int? received;
  int? chatId;
  FileMessageStatus? fileStatus;
  Media? image;
  MessageType? type;
  Chat? chat;
  String? uuid;
  List<User> readBy = [];
  double progress = 0;
  SharedContact? contact;
  bool showReadBy = false;
  Message(
      {this.id,
      this.total,
      this.received,
      this.voice,
      this.uuid,
      this.contact,
      this.createdAt,
      this.fileStatus,
      this.file,
      this.image,
      this.chat,
      this.chatId,
      this.type,
      this.message,
      this.sender,
      this.userId});

  Message.fromJson(Map data) {
    id = data['id'];
    message = data['content'];
    userId = data['user_id'];
    createdAt = DateTime.tryParse(data['created_at']);
    file = data['file'] != null ? Media.fromJson(data['file']) : null;
    voice = data['voice'];
    sender = data['sender'] != null ? User.fromJson(data['sender']) : null;
    type = setType(data['type']);
    image = data['image'] != null ? Media.fromJson(data['image']) : null;
    chatId = data['chat_id'];
    chat = data['chat'] != null ? Chat.fromJson(data['chat']) : null;
    readBy = data['read_by'] != null
        ? List<User>.from(data['read_by'].map((x) => User.fromJson(x)))
        : [];
    uuid = data['uuid'];
    contact = data['contact'] != null
        ? SharedContact.fromJson(data['contact'])
        : null;
    checkStatus();
  }
  checkStatus() async {
    final exists = await LocalStorage.fileExists(uuid!);
    fileStatus =
        exists ? FileMessageStatus.downloaded : FileMessageStatus.notExist;
  }

  setType(String? type) {
    switch (type) {
      case 'text':
        return MessageType.text;
      case 'image':
        return MessageType.image;
      case 'audio':
        return MessageType.audio;
      case 'video':
        return MessageType.video;
      case 'file':
        return MessageType.file;
      case 'contact':
        return MessageType.contact;
      default:
        return null;
    }
  }
}
