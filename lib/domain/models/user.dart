import 'package:family_management/domain/models/family.dart';
import 'package:livekit_client/livekit_client.dart';

class User {
  int? id;
  String? name;
  String? email;
  DateTime? createdAt;
  String? avatar;
  String? bio;
  bool talking = false;
  bool mute = false;
  bool speakerEnabled = true;
  bool cameraEnabled = false;
  String? sid;
  double? lat;
  double? lng;
  VideoTrack? videoRenderer;
  int? battery;
  List<Family> families = [];

  // Online status properties
  bool isOnline = false;
  DateTime? lastSeenAt;

  User({
    this.id,
    this.name,
    this.email,
    this.bio,
    this.createdAt,
    this.isOnline = false,
    this.lastSeenAt,
  });

  User.fromJson(Map json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    createdAt = DateTime.tryParse("${json['created_at']}");
    avatar = json['image'];
    bio = json['bio'];
    lat = json['lat'];
    lng = json['lng'];
    families = json['families'] != null
        ? <Family>[...json['families'].map((e) => Family.formJson(e)).toList()]
        : [];

    // Parse online status if available
    if (json['online_status'] != null) {
      isOnline = json['online_status']['is_online'] == true;
      if (json['online_status']['last_seen_at'] != null) {
        lastSeenAt =
            DateTime.tryParse("${json['online_status']['last_seen_at']}");
      }
    }
  }

  toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'createdAt': createdAt?.toString(),
      'image': avatar,
      'bio': bio,
      'families': families.map((family) => family.toMap()).toList(),
      'online_status': {
        'is_online': isOnline,
        'last_seen_at': lastSeenAt?.toIso8601String(),
      },
    };
  }

  // Format last seen time as a readable string
  String getLastSeenText() {
    if (isOnline) return 'Online';
    if (lastSeenAt == null) return 'Offline';

    final now = DateTime.now();
    final difference = now.difference(lastSeenAt!);

    if (difference.inSeconds < 60) {
      return 'Last seen just now';
    } else if (difference.inMinutes < 60) {
      return 'Last seen ${difference.inMinutes} min ago';
    } else if (difference.inHours < 24) {
      return 'Last seen ${difference.inHours} hours ago';
    } else if (difference.inDays < 7) {
      return 'Last seen ${difference.inDays} days ago';
    } else {
      return 'Last seen on ${lastSeenAt!.day}/${lastSeenAt!.month}/${lastSeenAt!.year}';
    }
  }
}
