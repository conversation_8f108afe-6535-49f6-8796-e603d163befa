import 'package:family_management/domain/controllers/achievement_board_controller.dart';
import 'package:family_management/domain/controllers/calendar_controller.dart';
import 'package:family_management/domain/controllers/chat_controller.dart';
import 'package:family_management/domain/controllers/home_controller.dart';
import 'package:family_management/domain/controllers/profile_controller.dart';
import 'package:family_management/domain/controllers/task_controller.dart';
import 'package:get/get.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.put(HomeController());
    Get.lazyPut(() => ChatController());
    Get.lazyPut(() => CalendarController());
    Get.lazyPut(() => ProfileController());
    Get.lazyPut(() => TaskController());
    Get.lazyPut(() => AchievementBoardController());
  }
}
