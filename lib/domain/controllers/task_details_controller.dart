import 'dart:developer';

import 'package:family_management/app_service.dart';
import 'package:family_management/domain/controllers/task_controller.dart';
import 'package:family_management/domain/models/sub_task.dart';
import 'package:family_management/domain/models/task.dart';
import 'package:family_management/domain/repositories/task_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:family_management/utils/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TaskDetailsController extends GetxController {
  final appService = Get.find<AppService>();

  final taskRepository = gt<TaskRepository>();
  final subTaskController = TextEditingController();
  late Task task;

  final RxList<SubTask> subTasks = <SubTask>[].obs;
  final RxBool _showSubtaskInput = false.obs;
  final RxBool _adding = false.obs;
  final RxString _subTaskError = ''.obs;

  bool get showSubtaskInput => _showSubtaskInput.value;

  bool get adding => _adding.value;

  String get subTaskError => _subTaskError.value;

  set showSubtaskInput(bool value) => _showSubtaskInput.value = value;

  @override
  void onInit() {
    super.onInit();
    task = Get.arguments;
    log(task.subTasks?.length.toString() ?? 'no subtasks');
    subTasks.value = task.subTasks ?? [];
    subTasks.refresh();
  }

  taskProgress() {
    double progress = 0;
    if (subTasks.isNotEmpty) {
      progress =
          (subTasks.where((element) => element.status == "done").length) /
              subTasks.length;
    }
    return progress;
  }

  addSubTask() async {
    _subTaskError.value = subTaskController.text.validateRequired() ?? '';
    if (subTaskError.isNotEmpty) {
      return;
    }
    _adding.value = true;
    final result = await taskRepository.addSubTask(
      taskId: task.id!,
      content: subTaskController.text,
      priority: "high",
    );
    result.fold(
      (l) {
        Utils.showToast(l.message);
      },
      (r) {
        Utils.showToast("Subtask added successfully".tr);
        log(r.status ?? '');

        subTasks.add(r);
        subTasks.refresh();

        subTaskController.clear();
        _showSubtaskInput.value = false;
      },
    );
    _adding.value = false;
  }

  deleteSubtask(int subtaskId) async {
    final index = subTasks.indexWhere((e) => e.id == subtaskId);
    if (index > -1) {
      subTasks[index].deleting = true;
      subTasks.refresh();
    }
    final result = await taskRepository.deleteSubTask(taskId: subtaskId);
    result.fold((l) {
      Utils.showToast(l.message);
      subTasks[index].deleting = false;
      subTasks.refresh();
    }, (r) {
      subTasks.removeAt(index);
      subTasks.refresh();
    });
  }

  updateStatus(int id, String? status) async {
    final result =
        await taskRepository.updateSubTask(taskId: id, status: status);
    result.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        final index = subTasks.indexWhere((e) => e.id == id);
        log('updating index $index');
        if (index > -1) {
          subTasks[index] = r;
          if (task.subTasks != null && index < task.subTasks!.length) {
            task.subTasks![index] = r;
          }
          subTasks.refresh();
          Get.find<TaskController>().getTasks();
        }
      },
    );
  }

  canEdit() {
    return task.createdBy?.id == appService.user.id;
  }
}
