import 'dart:developer';
import 'dart:io';

import 'package:family_management/app_service.dart';
import 'package:family_management/domain/mixins/image_picker.dart';
import 'package:family_management/domain/models/album.dart';
import 'package:family_management/domain/models/media.dart';
import 'package:family_management/domain/repositories/album_repository.dart';
import 'package:family_management/domain/repositories/media_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_file_downloader/flutter_file_downloader.dart';
import 'package:get/get.dart';
import 'package:open_file/open_file.dart';

class FilesController extends GetxController with ImagePickerMixin {
  final appService = Get.find<AppService>();
  final albumRepository = gt<AlbumRepository>();
  final mediaRepository = gt<MediaRepository>();

  final Rx<File> _file = File('').obs;
  final RxBool _loading = false.obs;
  final RxBool _uploading = false.obs;
  final RxList<Media> media = <Media>[].obs;
  final RxDouble _progress = 0.0.obs;

  bool get loading => _loading.value;
  bool get uploading => _uploading.value;
  File get file => _file.value;
  double get progress => _progress.value;

  late Album folder;

  @override
  void onInit() {
    super.onInit();
    folder = Get.arguments;
    fetchFilesWithLoading();
  }

  fetchFilesWithLoading() async {
    _loading.value = true;
    await getFiles();
    _loading.value = false;
  }

  pickFromFiles() async {
    final granted = await Utils.requestStoragePermissions();
    log(granted.toString());
    if (granted == null) {
      return;
    }
    final result = await pickFile();
    if (result == null) return;
    _file.value = result;
    showApproveDialog();
  }

  showApproveDialog() {
    return Get.dialog(AlertDialog(
        content: TextWidget(text: 'Are you sure you want to upload?'.tr),
        actions: [
          TextButton(
              onPressed: () => Get.back(),
              child: TextWidget(
                text: 'Cancel'.tr,
                color: Colors.red,
              )),
          TextButton(
              onPressed: () => upload(),
              child: TextWidget(
                text: 'Upload'.tr,
              )),
        ]));
  }

  upload() async {
    if (Get.isDialogOpen!) Get.back();
    _uploading.value = true;
    _progress.value = 0.0;
    final result = await mediaRepository.create(
      image: file,
      albumId: folder.id!,
      onSendProgress: (p0, p1) => _progress.value = p0 / p1,
    );
    result.fold((l) => Utils.showToast(l.message), (r) async {
      await getFiles();
    });
    _uploading.value = false;
  }

  getFiles() async {
    final result = await albumRepository.getAlbum(folder.id!);
    result.fold((l) => Utils.showToast(l.message), (r) {
      media.value = r.media;
      media.refresh();
    });
  }

  download(int index) async {
    media[index].downloading = true;
    media.refresh();
    _progress.value = 0.0;
    final result = await FileDownloader.downloadFile(
      url: media[index].originalUrl!,
      onProgress: (fileName, prgs) {
        final recieved = prgs / 100;
        if (recieved > 1) return;
        _progress.value = recieved;
        media.refresh();
      },
      onDownloadError: (errorMessage) {
        log(errorMessage);
        media[index].downloading = false;
        media.refresh();
      },
    );
    media[index].downloading = false;
    media.refresh();
    OpenFile.open(result?.path);
  }
}
