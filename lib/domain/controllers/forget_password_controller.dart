import 'package:family_management/config/app_routes.dart';
import 'package:family_management/app_service.dart';
import 'package:family_management/domain/repositories/auth_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:family_management/utils/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ForgetPasswordController extends GetxController {
  final authRepository = gt<AuthRepository>();
  final appService = Get.find<AppService>();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final firstController = TextEditingController();
  final seconController = TextEditingController();
  final thirdController = TextEditingController();
  final fourthController = TextEditingController();

  final RxBool _showPassword = false.obs;
  final RxBool _showConfirmPassword = false.obs;
  final RxBool _loading = false.obs;
  final RxBool _sendingCode = false.obs;
  final RxString _emailError = ''.obs;
  final RxString _codeError = ''.obs;
  final RxString _passwordError = ''.obs;
  final RxString _code = ''.obs;

  bool get loading => _loading.value;
  bool get sendingCode => _sendingCode.value;
  String get codeError => _codeError.value;
  String get passwordError => _passwordError.value;
  String get emailError => _emailError.value;
  bool get showPassword => _showPassword.value;
  bool get showConfirmPassword => _showConfirmPassword.value;

  set showPassword(bool value) => _showPassword.value = value;
  set showConfirmPassword(bool value) => _showConfirmPassword.value = value;

  requestCode() async {
    bool res = false;
    _emailError.value = emailController.text.validateEmail() ?? '';
    if (emailError.isNotEmpty) return;
    _sendingCode.value = true;
    final result = await authRepository.forgetPassword(emailController.text);
    result.fold((l) => _emailError.value = l.message, (r) {
      Utils.showToast('The code sent to your email'.tr);
      res = true;
    });
    _sendingCode.value = false;
    return res;
  }

  requestCodeAndRedirect() async {
    final res = await requestCode();
    if (res) {
      Get.toNamed(AppRoutes.verifyCode);
    }
  }

  checkCodeValidity() async {
    _codeError.value = firstController.text.validateRequired() ?? '';
    _codeError.value = seconController.text.validateRequired() ?? '';
    _codeError.value = thirdController.text.validateRequired() ?? '';
    _codeError.value = fourthController.text.validateRequired() ?? '';
    _code.value =
        '${firstController.text}${seconController.text}${thirdController.text}${fourthController.text}';
    Utils.log(_code.value);
    if (codeError.isNotEmpty) return;
    _loading.value = true;
    final result = await authRepository.checkCode(
        email: emailController.text, code: _code.value);
    result.fold((l) => _codeError.value = l.message,
        (r) => Get.toNamed(AppRoutes.forgetPassword));
    _loading.value = false;
  }

  resetPassword() async {
    _passwordError.value = passwordController.text.validateRequired() ?? '';
    if (passwordError.isNotEmpty) return;
    if (passwordController.text != confirmPasswordController.text) {
      _passwordError.value = 'Passwords do not match'.tr;
      return;
    }
    _loading.value = true;
    final result = await authRepository.resetPassword(
        email: emailController.text,
        code: _code.value,
        password: passwordController.text);
    result.fold((l) => Utils.showToast(l.message), (r) {
      Utils.showToast('Password has been reset successfuly'.tr);
      Get.offAllNamed(AppRoutes.resetSuccess);
    });
    _loading.value = false;
  }
}
