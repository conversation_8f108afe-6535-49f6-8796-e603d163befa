import 'package:family_management/app_service.dart';
import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/chat_controller.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/auth_repository.dart';
import 'package:family_management/domain/repositories/chat_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:get/get.dart';

class StartNewChatController extends GetxController {
  final appService = Get.find<AppService>();
  final chatRepository = gt<ChatRepository>();
  final authRepository = gt<AuthRepository>();

  final RxBool _loading = false.obs;
  final RxBool _creating = false.obs;
  final RxList<User> members = <User>[].obs;
  final RxString _searchQuery = ''.obs;

  bool get loading => _loading.value;
  bool get creating => _creating.value;
  String get searchQuery => _searchQuery.value;

  set searchQuery(String value) => _searchQuery.value = value;

  @override
  void onInit() {
    super.onInit();
    getMembers();
  }

  // Get filtered members based on search query
  List<User> get filteredMembers {
    if (searchQuery.isEmpty) {
      return members;
    }
    
    return members.where((member) {
      final name = member.name?.toLowerCase() ?? '';
      final email = member.email?.toLowerCase() ?? '';
      final query = searchQuery.toLowerCase();
      
      return name.contains(query) || email.contains(query);
    }).toList();
  }

  getMembers() async {
    _loading.value = true;
    final result = await chatRepository.getMembers();
    result.fold((l) => Utils.showToast(l.message), (r) {
      // Filter out the current user from the list
      final currentUserId = appService.user.id;
      members.value = r.where((user) => user.id != currentUserId).toList();
      members.refresh();
    });
    _loading.value = false;
    update();
  }

  // Start a private chat with a specific member
  startChatWithMember(User member) async {
    if (_creating.value) return;
    
    _creating.value = true;
    update();
    
    final authUser = authRepository.getAuthUser();
    final result = await chatRepository.createChat(
      members: [member.id!], 
      familyId: authUser!.families.first.id!
    );
    
    result.fold((l) {
      Utils.showToast(l.message);
    }, (chat) {
      // Update the chat list
      final chatController = Get.find<ChatController>();
      chatController.getChats();
      
      // Navigate to the private conversation
      Get.offNamed(AppRoutes.privateConversation, arguments: chat);
    });
    
    _creating.value = false;
    update();
  }

  // Clear search query
  clearSearch() {
    _searchQuery.value = '';
    update();
  }
}
