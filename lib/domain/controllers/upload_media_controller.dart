import 'dart:async';
import 'dart:io';

import 'package:family_management/domain/controllers/gallery_controller.dart';
import 'package:family_management/domain/mixins/image_picker.dart';
import 'package:family_management/domain/models/album.dart';
import 'package:family_management/domain/repositories/album_repository.dart';
import 'package:family_management/domain/repositories/media_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

class UploadMediaController extends GetxController with ImagePickerMixin {
  final albumRepository = gt<AlbumRepository>();
  final mediaRepository = gt<MediaRepository>();
  final searchController = TextEditingController();

  final RxList<Album> _albums = <Album>[].obs;
  final RxList<Album> _filteredAlbums = <Album>[].obs;
  final Rx<Album?> _selectedAlbum = Rx<Album?>(null);
  final RxBool _loadingAlbums = false.obs;
  final RxBool _uploading = false.obs;
  final RxString _searchQuery = ''.obs;

  Timer? _debounceTimer;

  // Getters
  List<Album> get albums => _albums;
  List<Album> get filteredAlbums => _filteredAlbums;
  Album? get selectedAlbum => _selectedAlbum.value;
  bool get loadingAlbums => _loadingAlbums.value;
  bool get uploading => _uploading.value;
  String get searchQuery => _searchQuery.value;

  @override
  void onInit() {
    super.onInit();
    loadAlbums();
  }

  @override
  void onClose() {
    _debounceTimer?.cancel();
    searchController.dispose();
    super.onClose();
  }

  /// Load albums from API
  Future<void> loadAlbums() async {
    _loadingAlbums.value = true;

    final result = await albumRepository.getAlbumns(type: 1);
    result.fold(
      (failure) {
        Utils.showToast(failure.message);
        _albums.clear();
        _filteredAlbums.clear();
      },
      (albumsList) {
        _albums.value = albumsList;
        _filteredAlbums.value = albumsList;
      },
    );

    _loadingAlbums.value = false;
  }

  /// Handle search input with debouncing
  void onSearchChanged(String query) {
    _searchQuery.value = query;

    // Cancel previous timer
    _debounceTimer?.cancel();

    // Set new timer for 500ms debounce
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch(query);
    });
  }

  /// Perform the actual search using API
  Future<void> _performSearch(String query) async {
    _loadingAlbums.value = true;

    final result = await albumRepository.getAlbumns(
      type: 1,
      search: query.isNotEmpty ? query : null,
    );

    result.fold(
      (failure) {
        Utils.showToast(failure.message);
        if (query.isEmpty) {
          _filteredAlbums.value = _albums;
        }
      },
      (albumsList) {
        _filteredAlbums.value = albumsList;
        if (query.isEmpty) {
          _albums.value = albumsList;
        }
      },
    );

    _loadingAlbums.value = false;
  }

  /// Clear search
  void clearSearch() {
    searchController.clear();
    _searchQuery.value = '';
    _filteredAlbums.value = _albums;
    _debounceTimer?.cancel();
  }

  /// Select an album
  void selectAlbum(Album album) {
    _selectedAlbum.value = album;
  }

  /// Show image picker options
  void showImagePicker() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            const SizedBox(height: 20),

            // Title
            Text(
              'Select Image Source'.tr,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 20),

            // Options
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Camera Option
                _buildPickerOption(
                  icon: CupertinoIcons.camera,
                  label: 'Camera'.tr,
                  onTap: _pickFromCamera,
                ),

                // Gallery Option
                _buildPickerOption(
                  icon: CupertinoIcons.photo,
                  label: 'Gallery'.tr,
                  onTap: _pickFromGallery,
                ),
              ],
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  /// Build picker option widget
  Widget _buildPickerOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: Colors.grey[700],
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Pick image from camera
  Future<void> _pickFromCamera() async {
    Get.back(); // Close bottom sheet

    // Check camera permission
    if (await Permission.camera.status.isDenied) {
      final result = await Permission.camera.request().isGranted;
      if (!result) {
        Utils.showToast('Camera permission is required'.tr);
        return;
      }
    }

    final file = await pickFromCamera();
    if (file != null) {
      await _uploadImage(file);
    }
  }

  /// Pick image from gallery
  Future<void> _pickFromGallery() async {
    Get.back(); // Close bottom sheet

    // Check storage permission
    final granted = await Utils.requestMediaPermissoins();
    if (granted == null) {
      Utils.showToast('Storage permission is required'.tr);
      return;
    }

    final file = await pickFromGallery();
    if (file != null) {
      await _uploadImage(file);
    }
  }

  /// Upload image to selected album
  Future<void> _uploadImage(File imageFile) async {
    if (_selectedAlbum.value == null) {
      Utils.showToast('Please select an album'.tr);
      return;
    }

    _uploading.value = true;

    final result = await mediaRepository.create(
      image: imageFile,
      albumId: _selectedAlbum.value!.id!,
      onSendProgress: (sent, total) {
        // You can add progress tracking here if needed
      },
    );

    result.fold(
      (failure) {
        Utils.showToast(failure.message);
      },
      (media) {
        Utils.showToast('Image uploaded successfully'.tr);

        // Refresh gallery if controller exists
        try {
          final galleryController = Get.find<GalleryController>();
          galleryController.getMedia();
        } catch (e) {
          // Gallery controller not found, that's okay
        }

        // Close dialog
        Get.back();
      },
    );

    _uploading.value = false;
  }
}
