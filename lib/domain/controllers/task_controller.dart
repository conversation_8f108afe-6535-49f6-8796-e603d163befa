import 'package:family_management/domain/models/task.dart';
import 'package:family_management/domain/repositories/task_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../app_service.dart';
import '../models/user.dart';

class TaskController extends GetxController {
  final appService = Get.find<AppService>();
  late TabController tabController;
  final TaskRepository taskRepository = gt<TaskRepository>();
  final ScrollController scrollController = ScrollController();

  final Rx<Task> _selectedTask = Task().obs;
  late User user;

  Task get selectedTask => _selectedTask.value;

  set selectedTask(Task value) => _selectedTask.value = value;

  final RxInt _activeIndex = 0.obs;
  final RxBool _loading = false.obs;
  final RxBool _isLoadingMore = false.obs;
  final RxList<Task> allTasks = <Task>[].obs;
  final RxInt _page = 1.obs;

  bool get loading => _loading.value;
  bool get isLoadingMore => _isLoadingMore.value;
  int get activeIndex => _activeIndex.value;

  // Computed list of tasks filtered by the current user (created by or assigned to)
  List<Task> get myTasks {
    return allTasks.where((task) {
      // Include tasks where the user is the creator
      final isCreator = task.createdBy?.id == user.id;

      // Include tasks where the user is assigned
      final isAssigned =
          task.assignees?.any((assignee) => assignee.id == user.id) ?? false;

      // Return true if either condition is met
      return isCreator || isAssigned;
    }).toList();
  }

  // Get the appropriate task list based on the tab
  List<Task> get tasks {
    return activeIndex == 0 ? allTasks : myTasks;
  }

  @override
  void onInit() {
    super.onInit();
    user = appService.user;
    tabController = TabController(length: 2, vsync: Navigator.of(Get.context!));
    tabController.addListener(() {
      _activeIndex.value = tabController.index;
      update(); // Update the UI when tab changes
    });
    scrollController.addListener(_onScroll);
    getTasks();
  }

  @override
  void onClose() {
    scrollController.removeListener(_onScroll);
    scrollController.dispose();
    tabController.dispose();
    super.onClose();
  }

  void _onScroll() {
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 200) {
      // Load more when user is 200 pixels from the bottom
      loadMoreTasks();
    }
  }

  getTasks() async {
    _loading.value = true;
    _page.value = 1; // Reset to first page

    final result = await taskRepository.getTasks(
        familyId: appService.user.families.first.id!, page: _page.value);

    result.fold((l) => Utils.showToast(l.message), (r) {
      allTasks.value = r;
      allTasks.refresh();
      update(); // Update the UI after fetching tasks
    });
    _loading.value = false;
  }

  loadMoreTasks() async {
    if (_isLoadingMore.value) return; // Prevent multiple simultaneous requests

    _isLoadingMore.value = true;
    _page.value += 1; // Increment page number

    final result = await taskRepository.getTasks(
        familyId: appService.user.families.first.id!, page: _page.value);

    result.fold((l) {
      Utils.showToast(l.message);
      _page.value -= 1; // Revert page increment on error
    }, (r) {
      if (r.isNotEmpty) {
        allTasks.addAll(r); // Add new tasks to existing list
        allTasks.refresh();
        update(); // Update the UI after fetching more tasks
      } else {
        _page.value -= 1; // Revert page increment if no more tasks
      }
    });
    _isLoadingMore.value = false;
  }
}
