import 'package:family_management/app_service.dart';
import 'package:family_management/domain/controllers/shopping_and_meal_controller.dart';
import 'package:family_management/domain/models/shopping_list.dart';
import 'package:family_management/domain/repositories/shopping_list_repository.dart';
import 'package:family_management/injections.dart';
import 'package:get/get.dart';

class MealDetailsController extends GetxController {
  final appService = Get.find<AppService>();
  final repository = gt<ShoppingListRepository>();
  final ShoppingAndMealController? mainController =
      Get.isRegistered<ShoppingAndMealController>()
          ? Get.find<ShoppingAndMealController>()
          : null;

  late ShoppingList shoppingList;
  bool _hasChanges = false;

  bool get hasChanges => _hasChanges;

  @override
  void onInit() {
    super.onInit();
    shoppingList = Get.arguments;
  }

  @override
  void onClose() {
    // Always update the main list when closing this controller
    // This ensures the main list is updated even if the user just viewed the list
    if (mainController != null) {
      mainController!.refreshShoppingListInLists(shoppingList);
    }
    super.onClose();
  }

  check(int itemId) {
    final index = shoppingList.items.indexWhere((e) => e.id == itemId);
    if (index > -1) {
      // Toggle the isPurchased value
      shoppingList.items[index].isPurchased =
          !(shoppingList.items[index].isPurchased ?? false);

      // Call the API to update the server
      repository.checkItem(shoppingList.items[index].id!);

      // Mark that changes were made
      _hasChanges = true;

      // Force UI update
      update();

      // Update the main list immediately if available
      if (mainController != null) {
        mainController!.refreshShoppingListInLists(shoppingList);
      }
    }
  }

  checked(int itemId) =>
      shoppingList.items.firstWhereOrNull((e) => e.id == itemId)?.isPurchased ??
      false;
}
