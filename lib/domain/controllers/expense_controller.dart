import 'dart:math';

import 'package:family_management/domain/models/budget_chart.dart';
import 'package:family_management/domain/models/expense.dart';
import 'package:family_management/domain/models/expense_category.dart';
import 'package:family_management/domain/repositories/expense_repository.dart';
import 'package:family_management/injections.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../app_service.dart';
import '../../utils/utils.dart';

class ExpenseController extends GetxController {
  final appService = Get.find<AppService>();
  final ExpenseRepository expenseRepository = gt<ExpenseRepository>();
  final descriptionController = TextEditingController();
  final RxString _dueDate = ''.obs;
  final RxBool _loading = false.obs;
  final RxInt _page = 1.obs;
  final RxList<ExpenseCategory> expenseCategories = <ExpenseCategory>[].obs;
  final RxList<Expense> expenses = <Expense>[].obs;
  final RxList<Expense> latestExpenses = <Expense>[].obs;
  final RxMap<String, Color> _categoryColors = <String, Color>{}.obs;
  final Rx<Expense> _selectedExpense = Expense().obs;
  final chartData = Rxn<BudgetChart>();

  Expense get selectedExpense => _selectedExpense.value;

  set selectedExpense(Expense value) => _selectedExpense.value = value;

  bool get loading => _loading.value;

  String get dueDate => _dueDate.value;

  set dueDate(String value) => _dueDate.value = value;

  @override
  void onInit() {
    super.onInit();
    homePage();
    getExpenseCategories();
    getExpenses();
  }

  homePage() async {
    final result = await expenseRepository.homePage(
      familyId: appService.user.families.first.id!,
      page: _page.value,
    );

    result.fold(
      (l) => Utils.showToast(l.message),
      (budget) {
        latestExpenses.value = budget.expenses ?? [];
        chartData.value = budget.chart;
        latestExpenses.refresh();
        chartData.refresh();
      },
    );
  }

  getExpenseCategories() async {
    final result = await expenseRepository.getExpenseCategories(
        familyId: appService.user.families.first.id!, page: _page.value);

    result.fold((l) => Utils.showToast(l.message), (r) {
      expenseCategories.value = r;
      expenseCategories.refresh();
    });
  }

  getExpenses() async {
    _loading.value = true;
    final result = await expenseRepository.getExpenses(
        familyId: appService.user.families.first.id!, page: _page.value);

    result.fold((l) => Utils.showToast(l.message), (r) {
      expenses.value = r;
      expenses.refresh();
    });
    _loading.value = false;
  }

  Map<String, double> groupExpensesByCategory() {
    final Map<String, double> expensesByCategory = {};

    for (var expense in expenses) {
      final String category = expense.expenseCategory!.name ?? 'Others';
      final double amount = (expense.amount ?? 0).toDouble();
      expensesByCategory[category] =
          (expensesByCategory[category] ?? 0) + amount;
    }

    return expensesByCategory;
  }

  getCategoryColor(String category) {
    if (!_categoryColors.containsKey(category)) {
      _categoryColors[category] = _generateRandomColor();
    }
    return _categoryColors[category]!;
  }

  _generateRandomColor() {
    final Random random = Random();
    return Color.fromRGBO(
      random.nextInt(256),
      random.nextInt(256),
      random.nextInt(256),
      1,
    );
  }

  getOldestAndNewestDate() {
    if (expenses.isEmpty) return 'No expenses';

    final oldestExpense = expenses.reduce((a, b) {
      return a.date!.isBefore(b.date!) ? a : b;
    });

    final newestExpense = expenses.reduce((a, b) {
      return a.date!.isAfter(b.date!) ? a : b;
    });

    return '${oldestExpense.date.toString().substring(0, 10)}    '
        '${newestExpense.date.toString().substring(0, 10)}';
  }
}
