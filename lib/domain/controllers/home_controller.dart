import 'package:family_management/app_service.dart';
import 'package:get/get.dart';
import 'package:livekit_client/livekit_client.dart';

class HomeController extends GetxController {
  final appService = Get.find<AppService>();

  final RxList<VideoTrackRenderer> videos = <VideoTrackRenderer>[].obs;
  final RxInt _activeIndex = 0.obs;

  int get activeIndex => _activeIndex.value;

  set activeIndex(int value) => _activeIndex.value = value;
}
