import 'package:family_management/config/app_routes.dart';
import 'package:family_management/app_service.dart';
import 'package:family_management/domain/repositories/family_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:family_management/utils/validation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

class RegisterFamilyController extends GetxController {
  final appService = Get.find<AppService>();
  final repository = gt<FamilyRepository>();

  final nameController = TextEditingController();
  final RxBool _canContinue = false.obs;
  final RxBool _loading = false.obs;

  bool get loading => _loading.value;
  bool get canContinue => _canContinue.value;

  register() async {
    if (!_canContinue.value) return;

    _loading.value = true;
    final result = await repository.register(nameController.text);
    result.fold((l) => Utils.showToast(l.message), (r) {
      Get.offAllNamed(AppRoutes.inviteEmail, arguments: {'familyId': r.id});
    });
    _loading.value = false;
  }

  check() {
    if (nameController.text.validateRequired() != null) {
      _canContinue.value = false;
    } else {
      _canContinue.value = true;
    }
  }
}
