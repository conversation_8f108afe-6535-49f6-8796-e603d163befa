import 'package:family_management/app_service.dart';
import 'package:family_management/domain/models/banner.dart';
import 'package:family_management/domain/models/event.dart';
import 'package:family_management/domain/models/task.dart';
import 'package:family_management/domain/repositories/achievements_repository.dart';
import 'package:family_management/domain/repositories/banners_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:get/get.dart';

class AchievementBoardController extends GetxController {
  final appService = Get.find<AppService>();
  final repository = gt<AchievementsRepository>();
  final bannersRepository = gt<BannersRepository>();

  final RxBool _loading = false.obs;
  final RxList<Task> tasks = <Task>[].obs;
  final RxList<Event> events = <Event>[].obs;
  final RxList<Event> occasions = <Event>[].obs;
  final RxList<Banner> banners = <Banner>[].obs;

  bool get loading => _loading.value;

  @override
  void onInit() {
    super.onInit();
    getAchievements();
  }

  getAchievements() async {
    _loading.value = true;

    // Fetch achievements
    final result = await repository.getAchievements();
    result.fold((l) => Utils.showToast(l.message), (r) {
      tasks.value = r['tasks'];
      events.value = r['events'];
      occasions.value = r['occasions'];
    });

    // Fetch banners
    await getBanners();

    _loading.value = false;
  }

  getBanners() async {
    final result = await bannersRepository.getBanners();
    result.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        banners.value = r;
        banners.refresh();
      },
    );
  }
}
