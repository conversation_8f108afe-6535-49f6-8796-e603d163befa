import 'dart:developer';
import 'dart:io';

import 'package:family_management/app_service.dart';
import 'package:family_management/data/services/sockets.dart';
import 'package:family_management/domain/mixins/image_picker.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/auth_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../config/app_routes.dart';

class ProfileController extends GetxController with ImagePickerMixin {
  final appService = Get.find<AppService>();
  final autRepository = gt<AuthRepository>();

  late User user;

  final RxList<User> members = RxList<User>([]);

  final Rx<File> _image = File('').obs;
  final RxString _imagePath = ''.obs;
  final RxDouble _uploadProgress = 0.0.obs;
  final RxBool _uploading = false.obs;

  String get imagePath => _imagePath.value;
  File? get image => _image.value;
  double get uploadProgress => _uploadProgress.value;
  bool get uploading => _uploading.value;

  @override
  void onInit() {
    super.onInit();
    user = appService.user;
    getMembers();

    // Listen to online status changes to refresh the UI
    ever(Sockets.onlineUsers, (_) {
      // Trigger UI refresh when online users change
      members.refresh();
    });
  }

  pickImageFromCamera() async {
    if (await Permission.camera.status.isDenied) {
      final result = await Permission.camera.request().isGranted;
      if (!result) return;
    }
    final file = await pickFromCamera();
    Get.back();
    log('picked file ${file?.path}');
    if (file == null) return;
    _image.value = file;
    _imagePath.value = file.path;
    uploadImage();
  }

  pickImageFromGallery() async {
    Utils.log('checking permission');
    final granted = await Utils.requestMediaPermissoins();
    if (granted == null) return;
    Utils.log('picking image');
    final file = await pickFromGallery();
    Get.back();
    log('picked file ${file?.path}');
    if (file == null) return;
    _image.value = file;

    Utils.log('sneding image');
    _imagePath.value = file.path;
    uploadImage();
  }

  uploadImage() async {
    log('uploading image');
    _uploading.value = true;
    _uploadProgress.value = 0;
    final result = await autRepository.uploadImage(_image.value,
        onSendProgress: (sent, total) {
      _uploadProgress.value = (sent / total);
      log('Upload progress: ${(uploadProgress * 100).toInt()}% ($sent/$total bytes)');
      update(); // Trigger UI update for GetBuilder widgets
    });
    result.fold((l) {
      Utils.showToast(l.message);
    }, (updatedUser) {
      // Update local user data
      user = updatedUser;

      // Update AppService user data so it's reflected across the app
      appService.user = updatedUser;

      // Show success message
      Utils.showToast('Profile image updated successfully'.tr);

      // Trigger UI refresh
      update();
    });

    _uploading.value = false;
  }

  getMembers() async {
    final result = await autRepository.getMembers();
    result.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        members.value = r;
        members.refresh();
      },
    );
  }

  pickMembers() {
    Get.toNamed(AppRoutes.inviteMembers);
  }

  // Test method to simulate upload progress
  testUploadProgress() async {
    _uploading.value = true;
    _uploadProgress.value = 0;

    for (int i = 0; i <= 100; i += 10) {
      await Future.delayed(const Duration(milliseconds: 200));
      _uploadProgress.value = i / 100.0;
      log('Test progress: ${(uploadProgress * 100).toInt()}%');
      update();
    }

    await Future.delayed(const Duration(milliseconds: 500));
    _uploading.value = false;
    Utils.showToast('Test upload completed!');
  }
}
