import 'package:family_management/app_service.dart';
import 'package:family_management/domain/models/album.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/album_repository.dart';
import 'package:family_management/domain/repositories/task_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:family_management/utils/validation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

class FolderController extends GetxController {
  final appService = Get.find<AppService>();
  final albumRepository = gt<AlbumRepository>();
  final taskRepository = gt<TaskRepository>();
  final nameController = TextEditingController();
  final multiSelectController = MultiSelectController<String>();

  final RxString _nameError = ''.obs;
  final RxString _assigneesError = ''.obs;
  final RxBool _creating = false.obs;
  final RxBool _loadingMembers = false.obs;

  final RxList<Album> folders = <Album>[].obs;
  final RxList<User> members = <User>[].obs;
  final RxList<User> selectedAssignees = <User>[].obs;
  final RxBool _loading = false.obs;

  String get nameError => _nameError.value;
  String get assigneesError => _assigneesError.value;
  bool get creating => _creating.value;
  bool get loading => _loading.value;
  bool get loadingMembers => _loadingMembers.value;
  @override
  void onInit() {
    super.onInit();
    getFolders();
    _fetchMembers();
  }

  /// Fetch family members for assignee selection
  _fetchMembers() async {
    _loadingMembers.value = true;
    final result = await taskRepository.getMembers();
    result.fold((l) => Utils.showToast(l.message), (r) {
      members.value = r;
      members.refresh();
    });
    _loadingMembers.value = false;
  }

  getFolders() async {
    _loading.value = true;
    final result = await albumRepository.getAlbumns(type: 2);
    result.fold((l) => Utils.showToast(l.message), (r) {
      folders.value = r;
      folders.refresh();
    });
    _loading.value = false;
  }

  create() async {
    // Clear previous errors
    _nameError.value = '';
    _assigneesError.value = '';

    // Validate name
    _nameError.value = nameController.text.validateRequired() ?? '';

    // Get selected assignees
    final assigneeIds = multiSelectController.selectedItems
        .map((item) => int.parse(item.value))
        .toList();

    // Validate assignees (required)
    if (assigneeIds.isEmpty) {
      _assigneesError.value = 'Please select at least one family member'.tr;
    }

    // Check if there are any validation errors
    if (_nameError.value.isNotEmpty || _assigneesError.value.isNotEmpty) {
      return;
    }

    _creating.value = true;
    final result = await albumRepository.create(
        familyId: appService.user.families.first.id!,
        name: nameController.text,
        type: 2,
        assignedUserIds: assigneeIds);
    result.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        Utils.showToast("Folder created successfully".tr);
        getFolders();
        if (Get.isDialogOpen!) {
          Get.back();
        }
      },
    );
    nameController.clear();
    multiSelectController.clearAll();
    _creating.value = false;
  }
}
