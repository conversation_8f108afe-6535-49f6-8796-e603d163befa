import 'dart:developer';

import 'package:family_management/app_service.dart';
import 'package:family_management/config/app_routes.dart';
import 'package:family_management/data/services/local_storage.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/injections.dart';
import 'package:get/get.dart';

class AuthCheckController extends GetxController {
  final appService = Get.find<AppService>();

  final RxBool _isChecking = true.obs;
  final RxBool _isAuthenticated = false.obs;

  bool get isChecking => _isChecking.value;
  bool get isAuthenticated => _isAuthenticated.value;

  @override
  void onInit() {
    super.onInit();
    checkAuthenticationStatus();
  }

  /// Check if user is already authenticated
  Future<void> checkAuthenticationStatus() async {
    try {
      log('Checking authentication status...');
      _isChecking.value = true;

      // Add a small delay to show the splash screen briefly
      await Future.delayed(const Duration(milliseconds: 1500));

      // Check if token exists in local storage
      final token = LocalStorage.getToken();
      final user = LocalStorage.getUser();

      log('Token exists: ${token != null && token.isNotEmpty}');
      log('User exists: ${user != null}');

      if (token != null && token.isNotEmpty && user != null) {
        // Token and user exist, user is authenticated
        log('User is authenticated, redirecting to home...');
        _isAuthenticated.value = true;
        dioClient.options.headers = {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token'
        };
        dioClient.options.queryParameters
            .addAll({'family_id': user.families.first.id});
        // Initialize app services for authenticated user
        await _initializeAuthenticatedUser(user);

        // Navigate to home
        Get.offAllNamed(AppRoutes.home);
      } else {
        // No token or user, redirect to login
        log('User is not authenticated, redirecting to login...');
        _isAuthenticated.value = false;

        // Clear any partial data
        await _clearAuthData();

        // Navigate to login
        Get.offAllNamed(AppRoutes.login);
      }
    } catch (e) {
      log('Error checking authentication: $e');
      // On error, assume not authenticated and go to login
      _isAuthenticated.value = false;
      await _clearAuthData();
      Get.offAllNamed(AppRoutes.login);
    } finally {
      _isChecking.value = false;
    }
  }

  /// Initialize services for authenticated user
  Future<void> _initializeAuthenticatedUser(User user) async {
    try {
      log('Initializing services for authenticated user: ${user.name}');

      // Bind private notifications and connect to real-time services
      await appService.bindPrivateNotifications();

      log('User services initialized successfully');
    } catch (e) {
      log('Error initializing user services: $e');
      // If initialization fails, treat as unauthenticated
      await _clearAuthData();
      throw e;
    }
  }

  /// Clear authentication data
  Future<void> _clearAuthData() async {
    try {
      // Clear token and user data
      await LocalStorage.clearToken();
      await LocalStorage.clearUser();
      dioClient.options.headers = {'Accept': 'application/json'};
      dioClient.options.queryParameters = {};
      log('Authentication data cleared');
    } catch (e) {
      log('Error clearing auth data: $e');
    }
  }

  /// Force re-check authentication (useful for logout)
  Future<void> recheckAuthentication() async {
    await checkAuthenticationStatus();
  }

  /// Logout user and redirect to login
  Future<void> logout() async {
    log('Logging out user...');
    _isAuthenticated.value = false;
    await _clearAuthData();
    Get.offAllNamed(AppRoutes.login);
  }
}
