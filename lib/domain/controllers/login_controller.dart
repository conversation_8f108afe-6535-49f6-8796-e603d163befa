import 'package:family_management/config/app_routes.dart';
import 'package:family_management/app_service.dart';
import 'package:family_management/domain/controllers/forget_password_controller.dart';
import 'package:family_management/domain/repositories/auth_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:family_management/utils/validation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

class LoginController extends GetxController {
  final appService = Get.find<AppService>();

  final authRepository = gt<AuthRepository>();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  final RxString _emailError = ''.obs;
  final RxString _passwordError = ''.obs;
  final RxBool _loading = false.obs;
  final RxBool _canLogin = false.obs;

  bool get loading => _loading.value;
  String get emailError => _emailError.value;
  String get passwordError => _passwordError.value;
  bool get canLogin => _canLogin.value;

  login() async {
    if (!_canLogin.value) return;
    _emailError.value = emailController.text.validateRequired() ?? '';
    _emailError.value = emailController.text.validateEmail() ?? '';
    _passwordError.value = passwordController.text.validateRequired() ?? '';
    if (emailError.isNotEmpty || passwordError.isNotEmpty) {
      return;
    }
    _loading.value = true;
    final result = await authRepository.login(
        email: emailController.text, password: passwordController.text);
    result.fold((l) => Utils.showToast(l.message), (r) {
      appService.bindPrivateNotifications();
      Get.offAllNamed(AppRoutes.home);
    });
    _loading.value = false;
  }

  checkCanLogin() {
    _canLogin.value =
        emailController.text.isNotEmpty && passwordController.text.isNotEmpty;
  }

  forgetPassword() {
    Get.put(ForgetPasswordController());
    Get.toNamed(AppRoutes.enterEmail);
  }
}
