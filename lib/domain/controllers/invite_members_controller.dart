import 'package:family_management/app_service.dart';
import 'package:family_management/domain/repositories/family_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class InviteMembersController extends GetxController {
  final appService = Get.find<AppService>();
  final familyRepository = gt<FamilyRepository>();

  final TextEditingController emailController = TextEditingController();
  final RxList<String> emails = <String>[].obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  bool get canInvite => emails.isNotEmpty && !isLoading.value;

  void addEmail() {
    final email = emailController.text.trim();
    if (email.isEmpty) return;

    if (!GetUtils.isEmail(email)) {
      errorMessage.value = 'Please enter a valid email address';
      return;
    }

    if (emails.contains(email)) {
      errorMessage.value = 'This email is already added';
      return;
    }

    emails.add(email);
    emailController.clear();
    errorMessage.value = '';
  }

  void removeEmail(String email) {
    emails.remove(email);
  }

  Future<void> inviteMembers() async {
    if (emails.isEmpty) {
      errorMessage.value = 'Please add at least one email address';
      return;
    }

    isLoading.value = true;

    final result = await familyRepository.sendInvites(
        emails: emails.toList(), familyId: appService.user.families.first.id!);

    isLoading.value = false;

    result.fold(
      (failure) {
        Utils.showToast(failure.message);
      },
      (success) {
        Utils.showToast('Invitations sent successfully'.tr);
        emails.clear();
        Get.back();
      },
    );
  }
}
