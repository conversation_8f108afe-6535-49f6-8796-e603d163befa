import 'dart:async';

import 'package:family_management/app_service.dart';
import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/models/call_log.dart';
import 'package:family_management/domain/models/chat.dart';
import 'package:family_management/domain/models/message.dart';
import 'package:family_management/domain/repositories/chat_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class ChatController extends GetxController {
  final appService = Get.find<AppService>();
  final chatRepository = gt<ChatRepository>();
  final Rx<Timer?> _timer = Timer(const Duration(seconds: 1), () {}).obs;
  final RxBool _loading = false.obs;
  final RxBool _archivedLoading = false.obs;
  final RxBool _loadingCallLog = false.obs;
  final RxList<Chat> chats = <Chat>[].obs;
  final RxList<Chat> archivedChats = <Chat>[].obs;
  final RxList<CallLog> callLog = <CallLog>[].obs;
  final RxInt _activeIndex = 0.obs;
  late TabController tabController;
  final RxString _searchQuery = ''.obs;

  // Chat filter state
  final RxString _chatFilter = 'all'.obs; // 'all', 'private', 'group'

  String get searchQuery => _searchQuery.value;
  Timer? get timer => _timer.value;
  set searchQuery(String value) => _searchQuery.value = value;

  bool get loading => _loading.value;

  bool get archivedLoading => _archivedLoading.value;

  int get activeIndex => _activeIndex.value;

  bool get loadingCallLog => _loadingCallLog.value;

  String get chatFilter => _chatFilter.value;

  set activeIndex(int index) => _activeIndex.value = index;
  set timer(Timer? value) => _timer.value = value;
  set chatFilter(String value) => _chatFilter.value = value;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 3, vsync: Navigator.of(Get.context!));
    tabController.addListener(() {
      _activeIndex.value = tabController.index;
    });
    getChats();
    getArchived();
    getCallLogs();
  }

  getCallLogs() async {
    _loadingCallLog.value = true;
    final result = await chatRepository.getCallLogs();
    result.fold((l) => Utils.showToast(l.message), (r) {
      callLog.value = r;
      callLog.refresh();
    });
    _loadingCallLog.value = false;
  }

  updateLastMessage(Message message, int chatId) {
    final index = chats.indexWhere((i) => i.id == chatId);
    if (index > -1) {
      chats[index].lastMessage = message;
    }
    chats.refresh();
  }

  getChats() async {
    _loading.value = true;
    final result = await chatRepository.getChats(
      isArchived: false,
      search: searchQuery.isEmpty ? null : searchQuery,
    );
    result.fold((l) => Utils.showToast(l.message), (r) {
      chats.value = r;
      chats.refresh();
    });
    _loading.value = false;
  }

  getArchived() async {
    Utils.log('getting archived');
    _archivedLoading.value = true;
    final result = await chatRepository.getChats(
      isArchived: true,
      search: searchQuery.isEmpty ? null : searchQuery,
    );
    result.fold((l) => Utils.showToast(l.message), (r) {
      archivedChats.value = r;
      archivedChats.refresh();
    });
    _archivedLoading.value = false;
  }

  deleteChat(int chatId) async {
    final result = await chatRepository.deleteChat(chatId);
    result.fold((l) => Utils.showToast(l.message), (r) {
      chats.removeWhere((i) => i.id == chatId);
      chats.refresh();
    });
  }

  archiveChat(int chatId) async {
    final result = await chatRepository.archiveChat(chatId);
    result.fold((l) => Utils.showToast(l.message), (r) {
      chats.removeWhere((i) => i.id == chatId);
      chats.refresh();
    });
  }

  pickMembers() {
    Get.toNamed(AppRoutes.chooseMembers);
  }

  startNewChat() {
    Get.toNamed(AppRoutes.startNewChat);
  }

  goToChat(Chat chat) {
    if (chat.isGroup ?? false) {
      Get.toNamed(AppRoutes.groupConversation, arguments: chat);
    } else {
      Get.toNamed(AppRoutes.privateConversation, arguments: chat);
    }
  }

  /// Returns filtered chats based on the current filter and search query
  List<Chat> getFilteredChats() {
    // First filter by chat type (all, private, group)
    List<Chat> typeFilteredChats;
    if (chatFilter == 'all') {
      typeFilteredChats = chats;
    } else if (chatFilter == 'private') {
      typeFilteredChats =
          chats.where((chat) => !(chat.isGroup ?? false)).toList();
    } else if (chatFilter == 'group') {
      typeFilteredChats = chats.where((chat) => chat.isGroup ?? false).toList();
    } else {
      typeFilteredChats = chats;
    }

    // If there's no search query, return the type-filtered chats
    if (searchQuery.isEmpty) {
      return typeFilteredChats;
    }

    // If there's a search query, filter the type-filtered chats by the search query
    return typeFilteredChats.where((chat) {
      // Search in chat name
      final chatName = chat.getChatName().toLowerCase();
      if (chatName.contains(searchQuery.toLowerCase())) {
        return true;
      }

      // Search in last message
      final lastMessage = chat.lastMessage?.message?.toLowerCase() ?? '';
      if (lastMessage.contains(searchQuery.toLowerCase())) {
        return true;
      }

      // Search in member names
      for (final member in chat.members) {
        final memberName = member.name?.toLowerCase() ?? '';
        if (memberName.contains(searchQuery.toLowerCase())) {
          return true;
        }
      }

      return false;
    }).toList();
  }

  /// Returns filtered archived chats based on the current filter and search query
  List<Chat> getFilteredArchivedChats() {
    // First filter by chat type (all, private, group)
    List<Chat> typeFilteredChats;
    if (chatFilter == 'all') {
      typeFilteredChats = archivedChats;
    } else if (chatFilter == 'private') {
      typeFilteredChats =
          archivedChats.where((chat) => !(chat.isGroup ?? false)).toList();
    } else if (chatFilter == 'group') {
      typeFilteredChats =
          archivedChats.where((chat) => chat.isGroup ?? false).toList();
    } else {
      typeFilteredChats = archivedChats;
    }

    // If there's no search query, return the type-filtered chats
    if (searchQuery.isEmpty) {
      return typeFilteredChats;
    }

    // If there's a search query, filter the type-filtered chats by the search query
    return typeFilteredChats.where((chat) {
      // Search in chat name
      final chatName = chat.getChatName().toLowerCase();
      if (chatName.contains(searchQuery.toLowerCase())) {
        return true;
      }

      // Search in last message
      final lastMessage = chat.lastMessage?.message?.toLowerCase() ?? '';
      if (lastMessage.contains(searchQuery.toLowerCase())) {
        return true;
      }

      // Search in member names
      for (final member in chat.members) {
        final memberName = member.name?.toLowerCase() ?? '';
        if (memberName.contains(searchQuery.toLowerCase())) {
          return true;
        }
      }

      return false;
    }).toList();
  }

  /// Sets the chat filter and refreshes the UI
  void setChatFilter(String filter) {
    chatFilter = filter;
    // If there's an active search query, we need to refresh the search results
    if (searchQuery.isNotEmpty) {
      // Cancel any existing timer to avoid multiple API calls
      if (timer?.isActive ?? false) {
        timer?.cancel();
      }
      // Refresh the chats with the current search query
      getChats();
      getArchived();
    } else {
      // Just refresh the UI if there's no search query
      chats.refresh();
      archivedChats.refresh();
    }
  }

  /// Format the last message date for display
  String formatMessageDate(DateTime? date) {
    if (date == null) return '';

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(date.year, date.month, date.day);

    if (messageDate == today) {
      // Today - show time
      return DateFormat('HH:mm').format(date);
    } else if (messageDate == yesterday) {
      // Yesterday
      return 'Yesterday'.tr;
    } else if (now.difference(date).inDays < 7) {
      // This week - show day name
      return DateFormat('EEEE').format(date);
    } else if (date.year == now.year) {
      // This year - show month and day
      return DateFormat('MMM d').format(date);
    } else {
      // Different year - show full date
      return DateFormat('MMM d, y').format(date);
    }
  }
}
