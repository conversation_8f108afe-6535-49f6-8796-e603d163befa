import 'package:family_management/domain/models/media.dart';
import 'package:family_management/domain/repositories/media_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../app_service.dart';

class GalleryController extends GetxController {
  final appService = Get.find<AppService>();
  final mediaRepository = gt<MediaRepository>();
  final ScrollController scrollController = ScrollController();

  late TabController tabController;
  late TabController innerTabController;
  final RxInt _activeIndex = 0.obs;
  final RxInt _innerActiveIndex = 0.obs;
  final RxList<Media> media = <Media>[].obs;
  final RxBool _loading = false.obs;
  final RxBool _isLoadingMore = false.obs;
  final RxBool _enableDelete = false.obs;
  final RxList<int> delelteIds = <int>[].obs;
  final RxInt _page = 1.obs;

  int get activeIndex => _activeIndex.value;
  int get innerActiveIndex => _innerActiveIndex.value;
  bool get enableDelete => _enableDelete.value;
  bool get loading => _loading.value;
  bool get isLoadingMore => _isLoadingMore.value;

  set enableDelete(bool value) => _enableDelete.value = value;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 2, vsync: Navigator.of(Get.context!));
    tabController.addListener(() {
      _activeIndex.value = tabController.index;
    });
    innerTabController =
        TabController(length: 2, vsync: Navigator.of(Get.context!));
    innerTabController.addListener(() {
      _innerActiveIndex.value = innerTabController.index;
    });
    scrollController.addListener(_onScroll);
    getMedia();
  }

  @override
  void onClose() {
    scrollController.removeListener(_onScroll);
    scrollController.dispose();
    tabController.dispose();
    innerTabController.dispose();
    super.onClose();
  }

  void _onScroll() {
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 200) {
      // Load more when user is 200 pixels from the bottom
      loadMoreMedia();
    }
  }

  getMedia() async {
    _loading.value = true;
    _page.value = 1; // Reset to first page

    final result = await mediaRepository.media(page: _page.value);
    result.fold((l) => Utils.showToast(l.message), (r) {
      media.value = r;
      media.refresh();
    });
    _loading.value = false;
  }

  loadMoreMedia() async {
    if (_isLoadingMore.value) return; // Prevent multiple simultaneous requests

    _isLoadingMore.value = true;
    _page.value += 1; // Increment page number

    final result = await mediaRepository.media(page: _page.value);
    result.fold((l) {
      Utils.showToast(l.message);
      _page.value -= 1; // Revert page increment on error
    }, (r) {
      if (r.isNotEmpty) {
        media.addAll(r); // Add new media to existing list
        media.refresh();
      } else {
        _page.value -= 1; // Revert page increment if no more media
      }
    });
    _isLoadingMore.value = false;
  }

  select(int id) {
    if (delelteIds.contains(id)) {
      delelteIds.removeWhere((i) => i == id);
    } else {
      delelteIds.add(id);
    }
    delelteIds.refresh();
    update();
  }
}
