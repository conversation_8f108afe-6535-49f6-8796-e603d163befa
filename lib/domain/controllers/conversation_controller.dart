import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:dart_pusher_channels/dart_pusher_channels.dart';
import 'package:family_management/app_service.dart';
import 'package:family_management/config/app_routes.dart';
import 'package:family_management/config/socket_config.dart';
import 'package:family_management/config/urls.dart';
import 'package:family_management/data/services/local_storage.dart';
import 'package:family_management/data/services/sockets.dart';
import 'package:family_management/domain/controllers/chat_controller.dart';
import 'package:family_management/domain/enums/file_message_status.dart';
import 'package:family_management/domain/mixins/image_picker.dart';
import 'package:family_management/domain/enums/message_type.dart';
import 'package:family_management/domain/mixins/share_contact.dart';
import 'package:family_management/domain/models/chat.dart';
import 'package:family_management/domain/models/media.dart';
import 'package:family_management/domain/models/message.dart';
import 'package:family_management/domain/models/shared_contact.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/auth_repository.dart';
import 'package:family_management/domain/repositories/chat_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:uuid/uuid.dart';

class ConversationController extends GetxController
    with ImagePickerMixin, ShareContactMixin {
  final appService = Get.find<AppService>();
  final chatRepository = gt<ChatRepository>();
  final authRepository = gt<AuthRepository>();
  final chatController = Get.find<ChatController>();
  final messageController = TextEditingController();
  final ScrollController scrollController = ScrollController();
  late PrivateChannel chatChannel;
  late StreamSubscription<ChannelReadEvent> channelSubscription;
  late StreamSubscription<bool> keyboardStream;
  late RecorderController recorderController;
  late Directory appDirecotry;
  final keyboardController = KeyboardVisibilityController();

  final RxBool _loading = false.obs;
  final RxList<Message> messages = <Message>[].obs;
  final Rx<Chat> _chat = Chat().obs;
  final Rx<User?> _user = User().obs;
  final Rx<User?> _authUser = User().obs;
  final Rx<File?> _image = File('').obs;
  final RxBool _hasImage = false.obs;
  final RxBool _recording = false.obs;
  final RxString _path = ''.obs;
  final Rx<MessageType> _type = MessageType.text.obs;
  final RxDouble _progress = 0.0.obs;
  final Rx<FileMessageStatus> _fileStatus = FileMessageStatus.uploading.obs;

  FileMessageStatus get fileStatus => _fileStatus.value;
  double get progress => _progress.value;
  bool get hasImage => _hasImage.value;
  bool get loading => _loading.value;
  Chat get chat => _chat.value;
  User? get user => _user.value;
  User? get authUser => _authUser.value;
  File? get image => _image.value;
  bool get recording => _recording.value;

  set recording(bool value) => _recording.value = value;
  set type(MessageType value) => _type.value = value;

  final arguments = Get.arguments;

  @override
  void onInit() {
    super.onInit();
    _initControllers();
    initChat();
    getMessages();
  }

  _initControllers() async {
    recorderController = RecorderController();
    appDirecotry = await getTemporaryDirectory();
    _path.value = '${appDirecotry.path}/recording.m4a';
  }

  Future<void> startOrStopRecording() async {
    log('recording $recording');
    try {
      if (recording) {
        _type.value = MessageType.audio;
        recorderController.reset();

        _path.value = await recorderController.stop(false) ?? '';
        _recording.value = false;
      } else {
        await recorderController.record(path: _path.value); // Path is optional
        _recording.value = true;
      }
    } catch (e) {
      log(e.toString());
      _recording.value = false;
    }
  }

  void refreshWave() {
    if (recording) recorderController.refresh();
  }

  call() async {
    Utils.log('starting a call');
    // await CallService.showOutgoingAudioCall(user!);
    Get.toNamed(AppRoutes.callScreen, arguments: {'user': user});
    // appService.call(user!, chat);
  }

  initChat() {
    _chat.value = arguments;
    Sockets.activeChat = arguments;
    appService.users.value = _chat.value.members;
    appService.users.refresh();
    LocalStorage.write('active_chat', jsonEncode(_chat.value.toMap()));
    _authUser.value = authRepository.getAuthUser();
    _user.value =
        chat.members.firstWhereOrNull((i) => i.id != _authUser.value?.id);
    if (_user.value == null) {
      Utils.log('there is no receiver');
      Get.back();
    }
    keyboardStream = keyboardController.onChange.listen((value) {
      if (value) {
        Utils.log('scrolling down');
        Timer(const Duration(milliseconds: 300), () => scroll(1));
      }
    });
  }

  getMessages() async {
    _loading.value = true;
    final result = await chatRepository.getMessages(chat.id);
    result.fold((l) => Utils.showToast(l.message), (r) {
      messages.value = r;
      messages.refresh();
    });
    _listener();
    _loading.value = false;
    scroll(1000);
  }

  _listener() async {
    log('subscribing to chat channel');
    final token = authRepository.getToken();
    chatChannel = socketClient.privateChannel('private-chat.${chat.id}',
        authorizationDelegate:
            EndpointAuthorizableChannelTokenAuthorizationDelegate
                .forPrivateChannel(
                    authorizationEndpoint:
                        Uri.parse('$baseUrl/broadcasting/auth'),
                    headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json'
            }));
    channelSubscription = chatChannel.bindToAll().listen(_messageEvent);

    chatChannel.subscribe();
    _sendMessageRead();
    Utils.log('subscribed to channel');
  }

  scroll(int duration) {
    WidgetsBinding.instance
        .addPostFrameCallback((_) => scrollController.animateTo(
              scrollController.position.maxScrollExtent,
              curve: Curves.easeOut,
              duration: const Duration(milliseconds: 200),
            ));
  }

  sendMessage() async {
    if (recording) await startOrStopRecording();
    final type = _type.value;
    log('Sending $type');
    if (type == MessageType.text && messageController.text.isEmpty) return;
    if (type == MessageType.file && _path.value.isEmpty) return;
    final uuid = const Uuid().v4();
    SharedContact? sharedContact;
    if (contact.displayName.isNotEmpty && type == MessageType.contact) {
      sharedContact = SharedContact(name: contact.displayName);
      sharedContact.phones = contact.phones.map((e) => e.number).toList();
    }
    contact = Contact();
    final message = Message(
        message: messageController.text,
        sender: authUser,
        type: type,
        file: Media(
          filePath: _path.value,
          fileName: _path.value.split('/').last,
        ),
        uuid: uuid,
        contact: sharedContact,
        image: hasImage ? Media(originalUrl: image!.path) : null,
        userId: authUser?.id);
    if (type == MessageType.audio) {
      message.voice = _path.value;
    }
    messages.insert(0, message);
    messages.refresh();
    update();
    type == MessageType.image
        ? Timer(const Duration(milliseconds: 100), () => scroll(50))
        : scroll(50);
    final content = messageController.text;
    final imageFile = image;
    final path = _path.value;
    log('type $type voice $path image ${imageFile?.path}');
    _image.value = File('');
    _hasImage.value = false;
    _type.value = MessageType.text;
    messageController.clear();
    LocalStorage.storeFile(uuid, path);
    final result = await chatRepository.sendMessage(
        chatId: chat.id!,
        message: content,
        type: type.toString(),
        voice: path,
        file: File(path),
        uuid: uuid,
        contact: sharedContact,
        onSendProgress: (p0, p1) {
          _progress.value = p1 / p0;
          if (_progress.value == 1) {
            _fileStatus.value = FileMessageStatus.uploaded;
          }
          update();
        },
        image: imageFile);
    result.fold((l) => Utils.showToast(l.message), (r) {
      // messages[0] = r;
      // messages.refresh();
    });
  }

  pickImageFromCamera() async {
    Get.back();
    if (await Permission.camera.status.isDenied) {
      final result = await Permission.camera.request().isGranted;
      if (!result) return;
    }
    _image.value = await pickFromCamera();
    if (image != null) {
      _type.value = MessageType.image;
      sendMessage();
    }
  }

  pickImageFromGallery() async {
    Utils.log('checking permission');
    Get.back();
    final granted = await Utils.requestMediaPermissoins();
    if (granted == null) return;
    Utils.log('picking image');
    _image.value = await pickFromGallery();
    if (image != null) {
      Utils.log('sneding image');
      _hasImage.value = true;
      _type.value = MessageType.image;
      sendMessage();
    }
  }

  sendFile() async {
    Get.back();
    final granted = await Utils.requestStoragePermissions();
    if (granted == null) {
      log('storage permission not granted');
      return;
    }

    _type.value = MessageType.file;
    final file = await pickFile();
    _path.value = file?.path ?? '';
    if (_path.value.isEmpty) {
      _type.value = MessageType.text;
    } else {
      sendMessage();
    }
  }

  _sendMessageRead() async => chatRepository.sendReadAll(chat.id!);

  _messageEvent(data) {
    final jsonMessage = jsonDecode(data.data);
    log('chat event $jsonMessage');
    if (jsonMessage['type'] == 'read') {
      _handleReadEvent(jsonMessage);
      return;
    }
    _handleMessageEvent(jsonMessage);
  }

  _handleMessageEvent(jsonMessage) {
    if (jsonMessage['message'] == null) return;
    final message = Message.fromJson(jsonMessage['message']);
    log('searching for this message');
    final index = messages.indexWhere((i) => i.uuid == message.uuid);
    log('search index $index for user ${message.userId} and local user ${authUser?.id}');
    if (index == -1) {
      if (message.userId == authUser?.id) {
        messages[0] = message;
      } else {
        messages.insert(0, message);
        _sendMessageRead();
      }
    } else {
      messages[index] = message;
    }
    chatController.updateLastMessage(message, chat.id!);
    messages.refresh();
    scroll(1000);
  }

  _handleReadEvent(event) {
    log('handling read event');
    final data = event['data'];
    if (data['messages'] == null || data['user'] == null) return;

    for (final item in data['messages']) {
      final index = messages.indexWhere((e) => e.id == item);
      log('found messages index read $index');
      if (index != -1) {
        messages[index].readBy.add(User.fromJson(data['user']));
        messages.refresh();
        scroll(50);
      }
    }
  }

  refreshStates() {
    messages.refresh();
    update();
  }

  @override
  void onClose() async {
    await LocalStorage.write('active_chat', null);
    scrollController.dispose();
    await channelSubscription.cancel();
    recorderController.dispose();
    await keyboardStream.cancel();
    chatChannel.unsubscribe();
    Sockets.activeChat = null;
    Utils.log('disposed');
    super.onClose();
  }
}
