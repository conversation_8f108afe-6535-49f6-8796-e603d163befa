import 'dart:io';

import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/repositories/auth_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:family_management/utils/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

class RegisterController extends GetxController {
  final authRepository = gt<AuthRepository>();
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final bioController = TextEditingController();
  final inviteCodeController = TextEditingController();

  final RxBool _hasImage = false.obs;
  final Rx<File?> _image = File('').obs;
  final RxBool _loading = false.obs;
  final RxString _emailError = ''.obs;
  final RxString _passwordError = ''.obs;
  final RxString _bioError = ''.obs;
  final RxString _inviteCodeError = ''.obs;
  final RxString _nameError = ''.obs;
  final RxInt _activeStep = 0.obs;
  final RxBool _joinByInvite = false.obs;
  final RxBool _showPassword = false.obs;

  bool get hasImage => _hasImage.value;
  bool get showPassword => _showPassword.value;
  bool get joinByInvite => _joinByInvite.value;
  int get activeStep => _activeStep.value;
  File? get image => _image.value;
  bool get loading => _loading.value;
  String get emailError => _emailError.value;
  String get passwordError => _passwordError.value;
  String get bioError => _bioError.value;
  String get inviteCodeError => _inviteCodeError.value;
  String get nameError => _nameError.value;

  set joinByInvite(bool value) => _joinByInvite.value = value;
  set showPassword(bool value) => _showPassword.value = value;
  set hasImage(bool value) => _hasImage.value = value;

  register() async {
    _emailError.value = emailController.text.validateRequired() ?? "";
    _emailError.value = emailController.text.validateEmail() ?? "";
    _passwordError.value = passwordController.text.validateRequired() ?? "";
    _nameError.value = nameController.text.validateRequired() ?? "";
    if (joinByInvite) {
      _inviteCodeError.value =
          inviteCodeController.text.validateRequired() ?? '';
    }
    if (emailError.isNotEmpty ||
        passwordError.isNotEmpty ||
        nameError.isNotEmpty) {
      return;
    }
    if (inviteCodeError.isNotEmpty) {
      return;
    }
    _loading.value = true;
    Utils.log('registering your account');
    final result = await authRepository.register(
      name: nameController.text,
      email: emailController.text,
      password: passwordController.text,
      bio: bioController.text,
      inviteCode:
          inviteCodeController.text.isEmpty ? null : inviteCodeController.text,
      image: hasImage ? image : null,
    );
    result.fold((l) => Utils.showToast(l.message), (r) {
      if (joinByInvite) {
        Get.offAllNamed(AppRoutes.home);
      } else {
        Get.offAllNamed(AppRoutes.registerFamily);
      }
    });
    _loading.value = false;
  }

  pickImage(ImageSource source) async {
    Get.back();
    bool granted = await Utils.requestMediaPermissoins();
    granted = await Utils.requestCameraPermissions();
    if (!granted) {
      Utils.showToast('Please enable storage and camera permissions'.tr);
      return;
    }
    final picker = ImagePicker();
    final file = await picker.pickImage(source: source);
    if (file != null) {
      _image.value = File(file.path);
      _hasImage.value = true;
    }
  }
}
