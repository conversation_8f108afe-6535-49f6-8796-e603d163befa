import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:dart_pusher_channels/dart_pusher_channels.dart';
import 'package:family_management/app_service.dart';
import 'package:family_management/config/socket_config.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/config/urls.dart';
import 'package:family_management/data/services/local_storage.dart';
import 'package:family_management/data/services/location_service.dart';
import 'package:family_management/data/services/sockets.dart';
import 'package:family_management/domain/models/area.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/area_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:family_management/utils/utils.dart';
import 'package:family_management/utils/validation.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MapController extends GetxController {
  final appService = Get.find<AppService>();
  final repository = gt<AreaRepository>();

  final Completer<GoogleMapController> completer =
      Completer<GoogleMapController>();
  late GoogleMapController mapController;
  final nameController = TextEditingController();
  final Rx<CameraPosition> _centerLocation =
      const CameraPosition(target: LatLng(33.88861060, 35.49547720), zoom: 8)
          .obs;
  late StreamSubscription<ChannelReadEvent> channelSubscription;
  late StreamSubscription<ChannelReadEvent> stopLocationSubscription;
  final RxList<LatLng> points = <LatLng>[].obs;
  final RxSet<Polygon> polygons = <Polygon>{}.obs;
  final RxList<Area> areas = <Area>[].obs;
  final RxList<User> users = <User>[].obs;
  final Rx<User> _selectedUser = User().obs;

  final RxBool _loading = false.obs;
  final RxBool _saving = false.obs;
  final RxBool _deleting = false.obs;
  final RxString _nameError = ''.obs;
  final RxBool _locationServiceRunning = false.obs;
  final RxBool _streamersModalShown = false.obs;

  User get selectedUser => _selectedUser.value;
  bool get loading => _loading.value;
  bool get locationServiceRunning => _locationServiceRunning.value;
  bool get saving => _saving.value;
  bool get deleting => _deleting.value;
  bool get streamersModalShown => _streamersModalShown.value;
  String get nameError => _nameError.value;
  CameraPosition get centerLocation => _centerLocation.value;

  set selectedUser(User value) => _selectedUser.value = value;

  void setStreamersModalShown(bool shown) {
    _streamersModalShown.value = shown;
  }

  void resetStreamersModal() {
    _streamersModalShown.value = false;
  }

  @override
  void onInit() {
    super.onInit();
    checkLocationService();
    getAreas();
    _connectLocationChannel();
  }

  complete(GoogleMapController ctr) {
    mapController = ctr;
    getMyLocation();
  }

  getMyLocation() async {
    var granted = await Utils.requestLocationPermission();
    if (!granted) {
      return;
    }

    final location = await Geolocator.getCurrentPosition();
    mapController.animateCamera(CameraUpdate.newCameraPosition(CameraPosition(
        target: LatLng(location.latitude, location.longitude), zoom: 12)));
  }

  shareMyLocation() {
    return Get.dialog(AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      content: SizedBox(
        child: TextWidget(
          text: 'Start sharing your location?'.tr,
          fontSize: 17,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        TextButton(
            onPressed: () => Get.back(),
            child: TextWidget(
              text: 'cancel'.tr,
              color: Colors.red,
            )),
        TextButton(
            onPressed: () {
              Get.back();
              LocationService.startStreamLocationService();
              _locationServiceRunning.value = true;
              update();
            },
            child: TextWidget(
              text: 'share'.tr,
            )),
      ],
    ));
  }

  stopSharingLocation() {
    LocationService.stopStreamLocationService();
    _locationServiceRunning.value = false;
    update();
  }

  addPoint(LatLng point) {
    log('adding point');
    points.add(point);
    points.refresh();
    final polygon =
        polygons.where((p) => p.polygonId.value == 'area').firstOrNull;

    if (polygon == null) {
      polygons.add(Polygon(
          polygonId: const PolygonId('area'),
          points: points,
          strokeWidth: 2,
          strokeColor: primarySwatch,
          fillColor: primarySwatch.withValues(alpha: 0.5)));
      polygons.refresh();
    }
    points.refresh();
    update();
  }

  clearPoints() {
    points.clear();
    points.refresh();
    polygons.clear();
    polygons.refresh();
    update();
  }

  closeAddArea() {
    points.clear();
    points.refresh();
    Get.back();
    return true;
  }

  checkLocationService() async {
    _locationServiceRunning.value = await LocationService.isServiceRunning();
    log('loation service running $locationServiceRunning');
  }

  _connectLocationChannel() async {
    final token = LocalStorage.getToken();
    final channel = socketClient.privateChannel(
        'private-location.${appService.user.families.first.id}',
        authorizationDelegate:
            EndpointAuthorizableChannelTokenAuthorizationDelegate
                .forPrivateChannel(
                    authorizationEndpoint:
                        Uri.parse('$baseUrl/broadcasting/auth'),
                    headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json'
            }));
    channelSubscription =
        channel.bind('App\\Events\\UpdateLocationEvent').listen((data) async {
      final jsonData = jsonDecode(data.data);
      log('location update received ${jsonData.toString()}');
      if (jsonData['user'] == null) return;
      final user = User.fromJson(jsonData['user']);
      if (jsonData['metadata'] != null &&
          jsonData['metadata']['battery'] != null) {
        user.battery = jsonData['metadata']['battery'];
      }

      // Set user as online since they're sharing location
      user.isOnline = true;
      user.lastSeenAt = DateTime.now();

      // Update the online status in Sockets service
      Sockets.updateUserOnlineStatus(user.id.toString(), true);

      final index = users.indexWhere((e) => e.id == user.id);
      log('updating user location ${user.name} with index $index');
      final wasEmpty = users.isEmpty;
      if (index > -1) {
        users[index] = user;
      } else {
        users.add(user);
      }
      users.refresh();
      update();

      // Show modal if users list was empty and now has users
      if (wasEmpty && users.isNotEmpty && !_streamersModalShown.value) {
        _streamersModalShown.value = true;
      }
    });
    stopLocationSubscription =
        channel.bind('App\\Events\\DisableLocationStreamEvent').listen((data) {
      final jsonData = jsonDecode(data.data);
      log('stop location event received ${jsonData.toString()}');
      if (jsonData['user'] == null) return;

      final user = User.fromJson(jsonData['user']);

      // Set user as offline since they stopped sharing location
      user.isOnline = false;
      user.lastSeenAt = DateTime.now();

      // Update the online status in Sockets service
      Sockets.updateUserOnlineStatus(user.id.toString(), false);

      users.removeWhere((e) => e.id == user.id);
      users.refresh();
      update();
    });
    channel.subscribe();
    log('location channel subscribed');
  }

  getAreas() async {
    _loading.value = true;
    final result = await repository.getAreas();
    result.fold((l) => Utils.showToast(l.message), (r) {
      areas.value = r;
      areas.refresh();
      for (final area in r) {
        polygons.add(Polygon(
            polygonId: PolygonId(area.id.toString()),
            points: area.points,
            strokeWidth: 2,
            strokeColor: primarySwatch,
            fillColor: primarySwatch.withValues(alpha: 0.5)));
      }
      polygons.refresh();
      update();
    });
    _loading.value = false;
  }

  showAreaNameDialog() {
    return Get.dialog(AlertDialog(
      content: Container(
        padding: const EdgeInsets.symmetric(vertical: 15),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextWidget(text: 'Enter area name'.tr),
            const SizedBox(height: 10),
            TextInput(
              controller: nameController,
              error: nameError,
              radius: BorderRadius.circular(10),
            )
          ],
        ),
      ),
      actions: [
        TextButton(
            onPressed: () => Get.back(),
            child: TextWidget(
              text: 'Cancel'.tr,
              color: Colors.red,
            )),
        TextButton(
            onPressed: () => saveArea(), child: TextWidget(text: 'Save'.tr))
      ],
    ));
  }

  saveArea() async {
    _nameError.value = nameController.text.validateRequired() ?? '';

    if (_nameError.value.isNotEmpty) {
      update();
      return;
    }
    if (Get.isDialogOpen!) Get.back();

    _saving.value = true;
    final result =
        await repository.create(name: nameController.text, points: points);
    nameController.clear();
    result.fold((l) => Utils.showToast(l.message), (r) {
      Utils.showToast('area added successfully'.tr);
      points.clear();
      areas.add(r);
      areas.refresh();
      polygons.add(
          Polygon(polygonId: PolygonId(r.id.toString()), points: r.points));
      polygons.refresh();
      update();

      // Trigger multiple refreshes to ensure UI updates everywhere
      Future.delayed(const Duration(milliseconds: 100), () {
        areas.refresh();
        update();
      });

      // Additional refresh after a longer delay to ensure all UI components update
      Future.delayed(const Duration(milliseconds: 500), () {
        areas.refresh();
        update();
      });
    });
    _saving.value = false;
  }

  deleteArea(int areaId) async {
    _deleting.value = true;
    final result = await repository.deleteArea(areaId);
    result.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        Utils.showToast('Area deleted successfully'.tr);
        // Remove from areas list
        areas.removeWhere((area) => area.id == areaId);
        areas.refresh();
        // Remove from polygons
        polygons.removeWhere(
            (polygon) => polygon.polygonId.value == areaId.toString());
        polygons.refresh();
        update();

        // Trigger multiple refreshes to ensure UI updates everywhere
        Future.delayed(const Duration(milliseconds: 100), () {
          areas.refresh();
          update();
        });

        // Additional refresh after a longer delay to ensure all UI components update
        Future.delayed(const Duration(milliseconds: 500), () {
          areas.refresh();
          update();
        });
      },
    );
    _deleting.value = false;
  }

  showDeleteAreaDialog(Area area) {
    return Get.dialog(AlertDialog(
      title: TextWidget(text: 'Delete Area'.tr),
      content: TextWidget(
        text:
            'Are you sure you want to delete "${area.name}"? This action cannot be undone.'
                .tr,
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: TextWidget(
            text: 'Cancel'.tr,
            color: Colors.grey,
          ),
        ),
        TextButton(
          onPressed: () {
            Get.back();
            deleteArea(area.id!);
          },
          child: TextWidget(
            text: 'Delete'.tr,
            color: Colors.red,
          ),
        ),
      ],
    ));
  }

  @override
  void onClose() {
    // appService.disposeListeners();
    channelSubscription.cancel();
    stopLocationSubscription.cancel();
    super.onClose();
  }
}
