import 'package:family_management/app_service.dart';
import 'package:family_management/domain/controllers/polls_controller.dart';
import 'package:family_management/domain/models/poll.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/poll_repository.dart';
import 'package:family_management/domain/repositories/task_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:family_management/utils/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

class AddPollController extends GetxController {
  final appService = Get.find<AppService>();
  final pollRepository = gt<PollRepository>();
  final taskRepository = gt<TaskRepository>();
  late Poll? poll;
  final nameController = TextEditingController();
  final multiSelectController = MultiSelectController<String>();

  final RxList<TextEditingController> items = <TextEditingController>[].obs;
  final RxList<User> members = <User>[].obs;
  final RxList<User> selectedAssignees = <User>[].obs;

  final RxBool _loading = false.obs;
  final RxBool _loadingMembers = false.obs;
  final RxString _nameError = ''.obs;
  final RxString _itemsError = ''.obs;
  final RxString _assigneesError = ''.obs;

  bool get loading => _loading.value;
  bool get loadingMembers => _loadingMembers.value;
  String get nameError => _nameError.value;
  String get itemsError => _itemsError.value;
  String get assigneesError => _assigneesError.value;

  @override
  void onInit() {
    super.onInit();
    poll = Get.arguments;
    _fetchMembers();
  }

  /// Fetch family members for assignee selection
  _fetchMembers() async {
    _loadingMembers.value = true;
    final result = await taskRepository.getMembers();
    result.fold((l) => Utils.showToast(l.message), (r) {
      members.value = r;
      members.refresh();
    });
    _loadingMembers.value = false;
  }

  addOption() {
    items.add(TextEditingController());
    items.refresh();
  }

  submit() async {
    // Clear previous errors
    _nameError.value = '';
    _itemsError.value = '';
    _assigneesError.value = '';

    // Validate name
    _nameError.value = nameController.text.validateRequired() ?? '';

    // Validate items
    if (items.isEmpty) {
      _itemsError.value = 'Please add at least one option'.tr;
    }

    // Validate assignees (required)
    final assigneeIds = multiSelectController.selectedItems
        .map((item) => int.parse(item.value))
        .toList();

    if (assigneeIds.isEmpty) {
      _assigneesError.value = 'Please select at least one family member'.tr;
    }

    // Check if there are any validation errors
    if (nameError.isNotEmpty ||
        itemsError.isNotEmpty ||
        assigneesError.isNotEmpty) {
      return;
    }

    final options = items.map((element) => element.text).toList();

    _loading.value = true;
    final result = await pollRepository.createOrUpdate(
        id: poll?.id,
        familyId: appService.user.families.first.id!,
        name: nameController.text,
        items: options,
        assignedUserIds: assigneeIds);

    result.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        Utils.showToast("Poll created successfully".tr);
        final pollsController = Get.find<PollsController>();
        pollsController.getPolls();
        Get.back();
      },
    );
    _loading.value = false;
  }
}
