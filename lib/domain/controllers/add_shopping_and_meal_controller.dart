import 'dart:async';
import 'dart:developer';

import 'package:family_management/domain/controllers/shopping_and_meal_controller.dart';
import 'package:family_management/domain/enums/shopping_list_type.dart';
import 'package:family_management/domain/models/suggested_item.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../app_service.dart';
import '../../injections.dart';
import '../../utils/utils.dart';
import '../repositories/shopping_list_repository.dart';

class AddShoppingAndMealController extends GetxController {
  final appService = Get.find<AppService>();
  final RxBool _loading = false.obs;
  final RxList<TextEditingController> itemsController =
      <TextEditingController>[].obs;
  final ShoppingListRepository shoppingListRepository =
      gt<ShoppingListRepository>();
  final nameController = TextEditingController();

  final RxString _type = ShoppingListType.shoppingList.value().toString().obs;
  final RxList<SuggestedItem> suggestions = <SuggestedItem>[].obs;
  final RxList<SuggestedItem> filteredSuggestions = <SuggestedItem>[].obs;
  final RxBool showSuggestions = false.obs;
  final RxInt currentFocusedIndex = (-1).obs;
  Timer? _debounceTimer;

  bool get loading => _loading.value;
  String get type => _type.value;

  set type(String value) => _type.value = value;

  @override
  void onInit() {
    super.onInit();
    _fetchSuggestions();
    // Add initial item
    if (itemsController.isEmpty) {
      addShoppingItem();
    }
  }

  Future<void> createShoppingList() async {
    _loading.value = true;
    final List<String> items = itemsController
        .map((controller) => controller.text.trim())
        .where((item) => item.isNotEmpty)
        .toList();

    final result = await shoppingListRepository.createShoppingList(
        familyId: appService.user.families.first.id!,
        name: nameController.text,
        items: items,
        type: type);

    result.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        Utils.showToast("Shopping List created successfully".tr);
        final shoppingAndMealController = Get.find<ShoppingAndMealController>();
        shoppingAndMealController.getShoppingLists();
        shoppingAndMealController.getMeals();
        Get.back();
      },
    );
    _loading.value = false;
  }

  void addShoppingItem() {
    itemsController.add(TextEditingController());
  }

  void removeShoppingItem(int index) {
    itemsController[index].dispose();
    itemsController.removeAt(index);
  }

  void clearInputs() {
    nameController.clear();
    for (var controller in itemsController) {
      controller.dispose();
    }
    itemsController.clear();
  }

  // Fetch item suggestions
  Future<void> _fetchSuggestions() async {
    final result = await shoppingListRepository.suggestItems();
    result.fold(
      (l) => log('Failed to fetch suggestions: ${l.message}'),
      (r) {
        suggestions.value = r;
        suggestions.refresh();
      },
    );
  }

  // Handle item text changes with debounce
  void onItemChanged(String value, int index) {
    currentFocusedIndex.value = index;
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (value.isNotEmpty && suggestions.isNotEmpty) {
        // Filter suggestions based on input and sort by usage frequency
        filteredSuggestions.value = suggestions
            .where((item) =>
                item.name != null &&
                item.name!.toLowerCase().contains(value.toLowerCase()))
            .toList()
          ..sort((a, b) => (b.timeUsed ?? 0).compareTo(a.timeUsed ?? 0));

        showSuggestions.value = filteredSuggestions.isNotEmpty;
      } else {
        filteredSuggestions.clear();
        showSuggestions.value = false;
      }
    });
  }

  // Select a suggestion
  void selectSuggestion(SuggestedItem suggestion, int index) {
    if (index < itemsController.length) {
      itemsController[index].text = suggestion.name ?? '';
    }
    showSuggestions.value = false;
    currentFocusedIndex.value = -1;
  }

  // Hide suggestions
  void hideSuggestions() {
    showSuggestions.value = false;
    currentFocusedIndex.value = -1;
  }

  // Check if suggestions should be shown for a specific index
  bool shouldShowSuggestionsForIndex(int index) {
    return showSuggestions.value && currentFocusedIndex.value == index;
  }

  @override
  void onClose() {
    _debounceTimer?.cancel();
    super.onClose();
  }
}
