import 'package:family_management/app_service.dart';
import 'package:family_management/domain/repositories/notification_repository.dart';
import 'package:get/get.dart';

import '../../injections.dart';
import '../../utils/utils.dart';
import '../models/notification.dart';

class NotificationsController extends GetxController {
  final appService = Get.find<AppService>();
  final NotificationRepository notificationRepository =
      gt<NotificationRepository>();
  final RxBool _loading = false.obs;
  final RxList<Notification> notifications = <Notification>[].obs;

  bool get loading => _loading.value;

  @override
  void onInit() {
    super.onInit();
    getNotifications();
  }

  getNotifications() async {
    _loading.value = true;
    final result = await notificationRepository.getNotifications();

    result.fold((l) => Utils.showToast(l.message), (r) {
      notifications.value = r;
      notifications.refresh();
    });
    _loading.value = false;
  }

  String formatTimeAgo(Duration difference) {
    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }
}
