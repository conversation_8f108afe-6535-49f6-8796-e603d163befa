import 'package:family_management/app_service.dart';
import 'package:family_management/domain/controllers/profile_controller.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/auth_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

class EditeProFileController extends GetxController {
  final appService = Get.find<AppService>();
  final repository = gt<AuthRepository>();

  late User user;

  final nameController = TextEditingController();
  final bioController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  final RxString _passwordError = ''.obs;
  final RxBool _loading = false.obs;

  bool get loading => _loading.value;

  String get passwordError => _passwordError.value;

  @override
  void onInit() {
    super.onInit();
    user = appService.user;
  }

  submit() async {
    if (passwordController.text.isNotEmpty &&
        (passwordController.text != confirmPasswordController.text)) {
      _passwordError.value = 'Password does not match';
      return;
    }

    _loading.value = true;

    final result = await repository.updateProfile(
      name: nameController.text,
      bio: bioController.text,
      password: passwordController.text,
    );

    result.fold((l) => Utils.showToast(l.message), (r) {
      appService.user = r;

      final profileController = Get.find<ProfileController>();
      profileController.user = r;

      Get.back();
    });

    _loading.value = false;
  }
}
