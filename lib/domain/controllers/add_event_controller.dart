import 'dart:developer';

import 'package:family_management/app_service.dart';
import 'package:family_management/domain/controllers/calendar_controller.dart';
import 'package:family_management/domain/models/event.dart';
import 'package:family_management/domain/models/occasion.dart';
import 'package:family_management/domain/repositories/event_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

import 'all_events_controller.dart';
import 'event_details_controller.dart';

class AddEventController extends GetxController {
  final appService = Get.find<AppService>();
  final eventRepository = gt<EventRepository>();

  final nameController = TextEditingController();
  final descriptionController = TextEditingController();
  final locationController = TextEditingController();

  final RxString _date = ''.obs;
  final RxString _time = ''.obs;
  final RxString _eventType = 'one_time'.obs;
  final RxInt _recurringInterval = 1.obs;
  final RxString _recurringPattern = 'daily'.obs;
  final RxString _occasionId = ''.obs;
  final RxString _notifyTime = '15m'.obs;
  final RxBool _loading = false.obs;
  final RxList<Occasion> occasions = <Occasion>[].obs;
  late Event? event;

  bool get loading => _loading.value;

  String get date => _date.value;

  String get notifyTime => _notifyTime.value;

  String get time => _time.value;

  String get eventType => _eventType.value;

  String get occasionId => _occasionId.value;

  int get recurringInterval => _recurringInterval.value;

  String get recurringPattern => _recurringPattern.value;

  set eventType(String value) => _eventType.value = value;

  set recurringInterval(int value) => _recurringInterval.value = value;

  set recurringPattern(String value) => _recurringPattern.value = value;

  set notifyTime(String value) => _notifyTime.value = value;

  set occasionId(String value) => _occasionId.value = value;

  @override
  void onInit() {
    super.onInit();
    event = Get.arguments;
    _initEvent();
    getOccasions();
  }

  _initEvent() {
    if (event != null) {
      nameController.text = event?.name ?? '';
      descriptionController.text = event?.name ?? "";
      _eventType.value = event?.type?.getValue();
      _recurringInterval.value = event?.recurringInterval ?? 1;
      _recurringPattern.value = event?.recurringPattern?.toString() ?? 'daily';
      if (event?.scheduledAt != null) {
        _date.value = intl.DateFormat('y-MM-d').format(event!.scheduledAt!);
        _time.value = intl.DateFormat('HH:mm').format(event!.scheduledAt!);
      }
      _occasionId.value = event?.occasion?.id.toString() ?? '';
    }
  }

  selectDate(BuildContext context) async {
    DateTime selectedDate = DateTime.now();

    // Parse current date if exists
    if (_date.value.isNotEmpty) {
      try {
        selectedDate = DateTime.parse(_date.value);
      } catch (e) {
        selectedDate = DateTime.now();
      }
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 350,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 8),
              height: 4,
              width: 40,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header with Done button
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'Cancel'.tr,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                  ),
                  Text(
                    'Select Date'.tr,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _date.value = selectedDate.toString().split(' ')[0];
                    },
                    child: Text(
                      'Done'.tr,
                      style: TextStyle(
                        color: Get.theme.primaryColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const Divider(height: 1),

            // iOS-style date picker
            Expanded(
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.date,
                initialDateTime: selectedDate,
                minimumDate: DateTime.now().subtract(const Duration(days: 365)),
                maximumDate: DateTime.now().add(const Duration(days: 365 * 2)),
                onDateTimeChanged: (DateTime newDate) {
                  selectedDate = newDate;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  getOccasions() async {
    final res =
        await eventRepository.getOccasions(appService.user.families.first.id!);
    res.fold((l) => Utils.showToast(l.message), (r) {
      occasions.value = r;
      _occasionId.value = r.first.id.toString();
      occasions.refresh();
    });
  }

  selectTime(BuildContext context) async {
    DateTime selectedTime = DateTime.now();

    // Parse current time if exists
    if (_time.value.isNotEmpty) {
      try {
        final parts = _time.value.split(':');
        final hour = int.parse(parts[0]);
        final minute = int.parse(parts[1]);
        selectedTime = DateTime(
          DateTime.now().year,
          DateTime.now().month,
          DateTime.now().day,
          hour,
          minute,
        );
      } catch (e) {
        selectedTime = DateTime.now();
      }
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 350,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 8),
              height: 4,
              width: 40,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header with Done button
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'Cancel'.tr,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                  ),
                  Text(
                    'Select Time'.tr,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _time.value =
                          '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}';
                    },
                    child: Text(
                      'Done'.tr,
                      style: TextStyle(
                        color: Get.theme.primaryColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const Divider(height: 1),

            // iOS-style time picker
            Expanded(
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.time,
                initialDateTime: selectedTime,
                onDateTimeChanged: (DateTime newTime) {
                  selectedTime = newTime;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  storeOrUpdate() async {
    _loading.value = true;
    try {
      Utils.log(appService.user.families);
      final res = await eventRepository.storeOrUpdate(
          id: event?.id,
          name: nameController.text,
          location: locationController.text,
          description: descriptionController.text,
          scheduledAt: formatScheduledAt(),
          familyId: appService.user.families.first.id!,
          type: _eventType.value,
          notifyTime: _notifyTime.value,
          occasionId: occasionId,
          recurringInterval: _recurringInterval.value,
          recurringPattern: _recurringPattern.value);
      res.fold((l) {
        Utils.showToast(l.message);
      }, (r) {
        event != null
            ? Utils.showToast("Event updated successfully".tr)
            : Utils.showToast("Event created successfully".tr);
        try {
          try {
            final eventController = Get.find<CalendarController>();
            eventController.fetchEvents('day');
          } catch (e) {
            log('calendar controller not found...skipping');
          }
          try {
            final eventDetailsController = Get.find<EventDetailsController>();
            eventDetailsController.event = r;
          } catch (e) {
            log('event details controller not found...skipping');
          }
          try {
            final allEventsController = Get.find<AllEventsController>();
            allEventsController.getEvents();
          } catch (e) {
            log('all events controller not found..skpping');
          }
          Get.back();
        } catch (e) {
          log('error rereshing events ${e.toString()}');
        }
      });
    } catch (e) {
      Utils.log(e);
    }
    _loading.value = false;
  }

  formatScheduledAt() {
    if (date.isEmpty) return null;
    final datetime = DateTime.parse(date);
    final newDate =
        '${datetime.year}-${datetime.month < 10 ? '0${datetime.month}' : datetime.month}-${datetime.day < 10 ? '0${datetime.day}' : datetime.day}';
    final parts = time.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);
    final newTime =
        '${hour < 10 ? '0$hour' : hour}:${minute < 10 ? '0$minute' : minute}:00';
    return '$newDate $newTime';
  }
}
