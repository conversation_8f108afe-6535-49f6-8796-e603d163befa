import 'dart:async';
import 'dart:math';
import 'package:family_management/domain/enums/shopping_list_type.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../app_service.dart';
import '../../injections.dart';
import '../models/shopping_list.dart';
import '../repositories/shopping_list_repository.dart';

class ShoppingAndMealController extends GetxController {
  final appService = Get.find<AppService>();
  late TabController tabController;
  final ShoppingListRepository shoppingListRepository =
      gt<ShoppingListRepository>();
  final nameController = TextEditingController();
  final RxList<TextEditingController> itemsController =
      <TextEditingController>[].obs;
  final RxInt _activeIndex = 0.obs;
  final Rx<ShoppingList> _selectedShoppingList = ShoppingList().obs;
  final RxBool _loading = false.obs;
  final RxBool _loadingPlanned = false.obs;

  ShoppingList get selectedShoppingList => _selectedShoppingList.value;

  set selectedShoppingList(ShoppingList value) =>
      _selectedShoppingList.value = value;

  int get activeIndex => _activeIndex.value;
  final RxInt _page = 1.obs;

  final RxBool isParticipant = RxBool(Random().nextBool());
  RxList<ShoppingList> shoppingLists = <ShoppingList>[].obs;
  RxList<ShoppingList> plannedMeals = <ShoppingList>[].obs;

  bool get loading => _loading.value;
  bool get loadingPlanned => _loadingPlanned.value;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 2, vsync: Navigator.of(Get.context!));
    tabController.addListener(() {
      _activeIndex.value = tabController.index;
    });
    getShoppingLists();
    getMeals();
    Timer.periodic(Duration(seconds: Random().nextInt(5) + 3), (timer) {
      isParticipant.value = Random().nextBool();
    });
  }

  getMeals() async {
    _loadingPlanned.value = true;
    plannedMeals.value = await getList(type: ShoppingListType.mealList);
    _loadingPlanned.value = false;
  }

  getShoppingLists() async {
    _loading.value = true;
    shoppingLists.value = await getList(type: ShoppingListType.shoppingList);
    _loading.value = false;
  }

  getList({ShoppingListType? type}) async {
    final result = await shoppingListRepository.getShoppingLists(
        familyId: appService.user.families.first.id!,
        page: _page.value,
        type: type);
    List<ShoppingList> list = [];
    result.fold((l) => Utils.showToast(l.message), (r) {
      list = r;
    });

    return list;
  }

  bool checked(int itemId) {
    final index = selectedShoppingList.items.indexWhere((e) => e.id == itemId);
    if (index > -1) {
      return selectedShoppingList.items[index].isPurchased ?? false;
    }
    return false;
  }

  void check(int itemId) {
    final index = selectedShoppingList.items.indexWhere((e) => e.id == itemId);
    if (index > -1) {
      // Toggle the isPurchased value
      selectedShoppingList.items[index].isPurchased =
          !(selectedShoppingList.items[index].isPurchased ?? false);

      // Call the API to update the server
      shoppingListRepository.checkItem(selectedShoppingList.items[index].id!);

      // Update the UI
      _selectedShoppingList.refresh(); // Refresh the Rx value
      update(); // Update GetBuilder widgets
    }
  }

  // Method to refresh a specific shopping list in the main lists
  void refreshShoppingListInLists(ShoppingList updatedList) {
    // Update in shopping lists if it exists there
    final shoppingIndex =
        shoppingLists.indexWhere((list) => list.id == updatedList.id);
    if (shoppingIndex != -1) {
      shoppingLists[shoppingIndex] = updatedList;
      shoppingLists.refresh();
    }

    // Update in planned meals if it exists there
    final mealIndex =
        plannedMeals.indexWhere((list) => list.id == updatedList.id);
    if (mealIndex != -1) {
      plannedMeals[mealIndex] = updatedList;
      plannedMeals.refresh();
    }

    // Force UI update
    update();

    // If this is the currently selected shopping list, update it too
    if (selectedShoppingList.id == updatedList.id) {
      selectedShoppingList = updatedList;
      _selectedShoppingList.refresh();
    }
  }

  // Method to refresh all lists from the server
  Future<void> refreshAllLists() async {
    await getShoppingLists();
    await getMeals();
  }
}
