import 'dart:developer';

import 'package:family_management/app_service.dart';
import 'package:family_management/domain/models/poll.dart';
import 'package:family_management/domain/models/poll_vote.dart';
import 'package:family_management/domain/repositories/poll_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:get/get.dart';

class PollsController extends GetxController {
  final appService = Get.find<AppService>();
  final repository = gt<PollRepository>();

  final RxBool _loading = false.obs;
  final RxBool _deleting = false.obs;

  final RxList<Poll> polls = <Poll>[].obs;

  bool get loading => _loading.value;
  bool get deleting => _deleting.value;

  @override
  void onInit() {
    super.onInit();
    getPolls();
  }

  getPolls() async {
    _loading.value = true;
    final result = await repository.getPolls();
    result.fold((l) => Utils.showToast(l.message), (r) {
      polls.value = r;
      polls.refresh();
    });
    _loading.value = false;
  }

  double getItemPercent(int pollId, int itemId) {
    final poll = polls.firstWhere((e) => e.id == pollId);
    final item = poll.items.firstWhere((e) => e.id == itemId);
    int totalVotes = 0;
    for (final item in poll.items) {
      totalVotes += item.totalVotes ?? 0;
    }
    if (totalVotes == 0) return 0;
    return (item.totalVotes ?? 0) / totalVotes;
  }

  vote(int pollId, int itemId) async {
    final pollIndex = polls.indexWhere((e) => e.id == pollId);
    final itemIndex = polls[pollIndex].items.indexWhere((e) => e.id == itemId);

    final prevIndex = polls[pollIndex]
        .votes
        .indexWhere((e) => e.userId == appService.user.id);

    log('vote $prevIndex');
    int votes = polls[pollIndex].items[itemIndex].totalVotes ?? 0;
    votes += 1;
    polls[pollIndex].items[itemIndex].totalVotes = votes;

    if (prevIndex > -1) {
      log('user does have prevoius vote');
      final index = polls[pollIndex]
          .items
          .indexWhere((e) => e.id == polls[pollIndex].votes[prevIndex].itemId);
      if (index > -1) {
        polls[pollIndex].items[index].totalVotes = votes - 1;
      }
      polls[pollIndex].votes.removeAt(prevIndex);
    }
    polls[pollIndex].votes.add(PollVote(
          userId: appService.user.id,
          pollId: polls[pollIndex].id,
          itemId: polls[pollIndex].items[itemIndex].id,
        ));
    polls.refresh();
    final result = await repository.vote(pollId: pollId, itemId: itemId);
    result.fold((l) => Utils.log(l.message), (r) {});
  }

  voted(int? pollId, int? itemId) {
    final poll = polls.firstWhere((e) => e.id == pollId);
    return poll.votes.firstWhereOrNull(
            (e) => e.userId == appService.user.id && e.itemId == itemId) !=
        null;
  }

  deletePoll(id) async {
    _deleting.value = true;
    final index = polls.indexWhere((e) => e.id == id);
    if (index > -1) {
      polls.removeAt(index);
      polls.refresh();
    }
    final result = await repository.deletePoll(id);
    result.fold((l) => Utils.showToast(l.message), (r) => {});
    _deleting.value = false;
    Get.back();
  }
}
