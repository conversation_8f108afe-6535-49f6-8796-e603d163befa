import 'dart:async';
import 'package:family_management/app_service.dart';
import 'package:family_management/domain/models/task.dart';
import 'package:family_management/domain/repositories/task_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:get/get.dart';

class AllTasksController extends GetxController {
  final appService = Get.find<AppService>();
  final respository = gt<TaskRepository>();

  late String? status;
  final RxBool _loading = false.obs;
  final RxList<Task> tasks = <Task>[].obs;
  final RxString _searchQuery = ''.obs;
  Timer? _debounceTimer;

  bool get loading => _loading.value;
  String get searchQuery => _searchQuery.value;
  set searchQuery(String value) => _searchQuery.value = value;

  @override
  void onInit() {
    super.onInit();
    status = Get.arguments?['status'];
    initTasks();
  }

  @override
  void onClose() {
    _debounceTimer?.cancel();
    super.onClose();
  }

  initTasks() async {
    _loading.value = true;
    await fetchTasks();
    _loading.value = false;
  }

  fetchTasks({String? search}) async {
    final user = appService.user;
    final result = await respository.getTasks(
        familyId: user.families.first.id!, status: status, search: search);
    result.fold((l) => Utils.showToast(l.message), (r) {
      tasks.value = r;
      tasks.refresh();
    });
  }

  void onSearchChanged(String query) {
    searchQuery = query;

    // Cancel previous timer if it exists
    _debounceTimer?.cancel();

    // If search query is empty, immediately fetch tasks
    if (query.isEmpty) {
      fetchTasks();
      return;
    }

    // Set up debounce timer for 500ms
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      fetchTasks(search: query);
    });
  }
}
