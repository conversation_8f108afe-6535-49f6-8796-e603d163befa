import 'package:family_management/app_service.dart';
import 'package:family_management/domain/controllers/calendar_controller.dart';
import 'package:family_management/domain/models/event.dart';
import 'package:family_management/domain/repositories/event_repository.dart';
import 'package:family_management/injections.dart';
import 'package:get/get.dart';

import '../../utils/utils.dart';

class EventDetailsController extends GetxController {
  final appService = Get.find<AppService>();
  final eventRepository = gt<EventRepository>();

  late Event event;

  final RxBool _deleting = false.obs;

  bool get deleting => _deleting.value;

  @override
  void onInit() {
    super.onInit();
    event = Get.arguments;
  }

  deleteEvent() async {
    _deleting.value = true;
    final res = await eventRepository.deleteEvent(event.id!);
    res.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        Utils.showToast("Event deleted successfully".tr);
        final calendarController = Get.find<CalendarController>();
        calendarController.fetchEvents('day');

        Get.back();

        Get.back();
      },
    );
    _deleting.value = false;
  }
}
