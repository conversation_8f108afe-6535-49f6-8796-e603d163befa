import 'package:family_management/config/app_routes.dart';
import 'package:family_management/app_service.dart';
import 'package:family_management/domain/repositories/family_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:family_management/utils/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class InviteEmailsController extends GetxController {
  final appService = Get.find<AppService>();
  final repository = gt<FamilyRepository>();
  final arguments = Get.arguments;

  final RxList<TextEditingController> emails = <TextEditingController>[].obs;
  final RxBool _loading = false.obs;
  final RxInt _familyId = 0.obs;

  bool get loading => _loading.value;

  @override
  void onInit() {
    super.onInit();
    addEmail();
    if (arguments['familyId'] == null) {
      Get.offAllNamed(AppRoutes.home);
    } else {
      _familyId.value = arguments['familyId'];
    }
  }

  addEmail() {
    emails.add(TextEditingController());
    emails.refresh();
  }

  sendInvites() async {
    _loading.value = true;
    Utils.log(_familyId.value);
    List<String> ems = [];
    for (final item in emails) {
      if (item.text.isNotEmpty && item.text.validateEmail() == null) {
        ems.add(item.text);
      }
    }
    final result =
        await repository.sendInvites(emails: ems, familyId: _familyId.value);
    result.fold((l) => Utils.showToast(l.message),
        (r) => Get.offAllNamed(AppRoutes.home));
    _loading.value = false;
  }
}
