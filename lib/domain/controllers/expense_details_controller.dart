import 'package:family_management/domain/models/expense.dart';
import 'package:family_management/domain/repositories/expense_repository.dart';
import 'package:family_management/injections.dart';
import 'package:get/get.dart';

import '../../app_service.dart';
import '../../utils/utils.dart';
import 'expense_controller.dart';

class ExpenseDetailsController extends GetxController {
  final appService = Get.find<AppService>();
  final ExpenseRepository expenseRepository = gt<ExpenseRepository>();
  final RxBool _loading = false.obs;
  final RxList<Expense> expenses = <Expense>[].obs;

  late Expense expense;

  bool get loading => _loading.value;

  @override
  void onInit() {
    super.onInit();
    expense = Get.arguments;
  }

  deleteExpense(int expenseId) async {
    _loading.value = true;
    final result = await expenseRepository.deleteExpense(expenseId: expenseId);
    result.fold((l) => Utils.showToast(l.message), (r) {
      expenses.removeWhere((i) => i.id == expenseId);
      expenses.refresh();
    });
    _loading.value = false;
    Get.find<ExpenseController>().getExpenses();
    Get.find<ExpenseController>().homePage();
    Get.find<ExpenseController>().getExpenseCategories();
    Get.back();
  }
}
