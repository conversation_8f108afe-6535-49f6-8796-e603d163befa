import 'package:family_management/domain/enums/task_type.dart';
import 'package:family_management/utils/validation.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

import '../../app_service.dart';
import '../../injections.dart';
import '../../utils/utils.dart';
import '../models/expense.dart';
import '../models/expense_category.dart';
import '../repositories/expense_repository.dart';
import 'expense_controller.dart';

class AddExpenseController extends GetxController {
  final appService = Get.find<AppService>();
  final ExpenseRepository expenseRepository = gt<ExpenseRepository>();

  final descriptionController = TextEditingController();
  final amountController = TextEditingController();
  final RxString _amountError = ''.obs;
  final RxString _dateError = ''.obs;
  final RxString _descriptionError = ''.obs;
  final RxBool _selectedExpenseType = false.obs;

  late Expense? expense;
  final RxString _date = ''.obs;
  final RxBool _loading = false.obs;
  final Rx<ExpenseCategory?> _selectedCategory = Rx<ExpenseCategory?>(null);

  final RxList<ExpenseCategory> expenseCategories = <ExpenseCategory>[].obs;

  String get amountError => _amountError.value;

  String get dateError => _dateError.value;

  String get descriptionError => _descriptionError.value;

  String get date => _date.value;

  set date(String value) => _date.value = value;

  bool get loading => _loading.value;

  bool get selectedExpenseType => _selectedExpenseType.value;

  ExpenseCategory? get selectedCategory => _selectedCategory.value;

  set selectedCategory(ExpenseCategory? value) =>
      _selectedCategory.value = value;

  set selectedExpenseType(bool value) => _selectedExpenseType.value = value;

  @override
  void onInit() {
    super.onInit();
    getExpenseCategories();
    expense = Get.arguments;
    _initExpense();
  }

  _initExpense() {
    if (expense != null) {
      descriptionController.text = expense!.description!;
      amountController.text = expense!.amount.toString();
      selectedCategory = expense!.expenseCategory;

      if (expense?.date != null) {
        date = intl.DateFormat('y-M-d').format(expense!.date!);
      }
      expense?.type == TaskType.recurring ? selectedExpenseType = true : false;
    }
  }

  validate() {
    _amountError.value = amountController.text.validateNumbers() ?? '';
    _dateError.value = date.validateRequired() ?? '';
    _descriptionError.value =
        descriptionController.text.validateRequired() ?? '';
    if (amountError.isNotEmpty ||
        dateError.isNotEmpty ||
        descriptionError.isNotEmpty) {
      return;
    }
    storeOrUpdateExpense();
  }

  getExpenseCategories() async {
    final result = await expenseRepository.getExpenseCategories(
      familyId: appService.user.families.first.id!,
      page: 1,
    );

    result.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        expenseCategories.value = r;
      },
    );
  }

  storeOrUpdateExpense() async {
    _loading.value = true;

    final result = await expenseRepository.storeOrUpdateExpense(
      id: expense?.id,
      familyId: appService.user.families.first.id!,
      expenseCategoryId: selectedCategory!.id ?? 0,
      description: descriptionController.text,
      date: date,
      amount: int.parse(amountController.text),
      type: selectedExpenseType ? 'recurring' : 'one_time',
    );

    result.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        Utils.showToast(expense != null
            ? "Expense updated successfully".tr
            : "Expense added successfully".tr);
        Get.find<ExpenseController>().getExpenses();
        Get.find<ExpenseController>().homePage();
        Get.find<ExpenseController>().getExpenseCategories();
        Get.back();
      },
    );

    _loading.value = false;
    clearInputs();
  }

  clearInputs() {
    descriptionController.clear();
    amountController.clear();
    date = '';
    selectedCategory = null;
  }
}
