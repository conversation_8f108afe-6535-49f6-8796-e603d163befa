import 'dart:developer';

import 'package:family_management/app_service.dart';
import 'package:family_management/domain/models/event.dart';
import 'package:family_management/domain/repositories/event_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CalendarController extends GetxController
    with GetTickerProviderStateMixin {
  final appService = Get.find<AppService>();
  final eventRepository = gt<EventRepository>();

  final RxList<Event> events = <Event>[].obs;
  final RxList<Event> monthEvents = <Event>[].obs;
  final RxBool _loading = false.obs;
  final RxBool _isScrolled = false.obs;
  final Rx<DateTime> _selectedDay = DateTime.now().obs;

  late AnimationController animationController;
  late Animation<double> fadeAnimation;

  bool get isScrolled => _isScrolled.value;
  DateTime get selectedDay => _selectedDay.value;
  bool get loading => _loading.value;

  set selectedDay(DateTime value) => _selectedDay.value = value;
  @override
  void onInit() {
    super.onInit();
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // Fade Animation (0.0 to 1.0)
    fadeAnimation =
        Tween<double>(begin: 1.0, end: 0.0).animate(animationController);

    // Initialize selectedDay to today (normalized to remove time component)
    final today = DateTime.now();
    _selectedDay.value = DateTime(today.year, today.month, today.day);

    // Load events for today and the current month
    fetchEvents('day');
    fetchEvents('month');
  }

  selectDay(DateTime day) {
    // Normalize the selected day to remove time component
    _selectedDay.value = DateTime(day.year, day.month, day.day);
    fetchEvents('day');
  }

  /// Refresh events for the current selected day and month
  refreshEvents() {
    fetchEvents('day');
    fetchEvents('month');
  }

  deleteEvent(id) async {
    final index = events.indexWhere((e) => e.id == id);
    if (index > -1) {
      events.removeAt(index);
      events.refresh();
    }
    final result = await eventRepository.deleteEvent(id);
    result.fold((l) => Utils.showToast(l.message), (r) => {});
  }

  fetchEvents(String groupBy) async {
    _loading.value = true;
    try {
      log('getting events $selectedDay');
      final result = await eventRepository.getEvents(
          date: selectedDay.toString(), groupBy: groupBy);
      result.fold((l) => Utils.showToast(l.message), (r) {
        switch (groupBy) {
          case 'day':
            events.value = r;
            events.refresh();
            break;
          case 'month':
            monthEvents.value = r;
            monthEvents.refresh();
            break;
        }
      });
    } catch (e) {
      log('error fetching events');
      log(e.toString());
    }
    _loading.value = false;
  }

  updateFade(double scrollOffset) {
    if (scrollOffset > 50) {
      animationController.forward(); // Fade out
      _isScrolled.value = true; // Change content
    } else {
      animationController.reverse(); // Fade in
      _isScrolled.value = false; // Reset content
    }
  }

  List<String> getEventsForDay(DateTime day) {
    // Filter monthEvents to get events for the specific day
    return monthEvents
        .where((event) {
          if (event.scheduledAt == null) return false;
          final eventDate = event.scheduledAt!;
          return eventDate.year == day.year &&
              eventDate.month == day.month &&
              eventDate.day == day.day;
        })
        .map((event) => event.name ?? '')
        .toList();
  }

  forwared() {
    _selectedDay.value = DateTime(selectedDay.year, selectedDay.month + 1);
    fetchEvents('month');
  }

  backward() {
    _selectedDay.value = DateTime(selectedDay.year, selectedDay.month - 1);
    fetchEvents('month');
  }

  @override
  void onClose() {
    animationController.dispose();
    super.onClose();
  }
}
