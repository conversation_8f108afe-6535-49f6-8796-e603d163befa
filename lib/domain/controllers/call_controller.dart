import 'dart:developer';

import 'package:audioplayers/audioplayers.dart';
import 'package:family_management/app_service.dart';
import 'package:family_management/data/services/sockets.dart';
import 'package:family_management/domain/controllers/chat_controller.dart';
import 'package:family_management/domain/models/chat.dart';
import 'package:family_management/domain/repositories/calls_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:get/get.dart';
import 'package:livekit_client/livekit_client.dart';

class CallController extends GetxController {
  final appService = Get.find<AppService>();
  final callRepository = gt<CallsRepository>();
  late Chat chat;

  final RxBool _ringing = false.obs;
  final RxBool _isIncomingCall = false.obs;
  final RxBool _micEnabled = true.obs;
  final RxBool _speakerEnabled = true.obs;
  final RxBool _cameraEnabled = false.obs;
  final AudioPlayer _audioPlayer = AudioPlayer();

  bool get ringing => _ringing.value;
  bool get isIncomingCall => _isIncomingCall.value;
  bool get micEnabled => _micEnabled.value;
  bool get cameraEnabled => _cameraEnabled.value;
  bool get speakerEnabled => _speakerEnabled.value;

  @override
  void onInit() {
    super.onInit();
    chat = Sockets.activeChat!;
    _isIncomingCall.value = Get.arguments['isIncomingCall'] ?? false;

    initializeAudioSession();
  }

  Future<void> initializeAudioSession() async {
    _ringing.value = true;

    if (!isIncomingCall) {
      final result = await callRepository.call(chat.id!);
      result.fold((l) {
        Utils.showToast(l.message);
        Get.back();
      }, (r) async {
        await _audioPlayer
            .setReleaseMode(ReleaseMode.loop); // Loop the ringtone
        await _audioPlayer.setVolume(1.0); // Ensure loud sound
        await _audioPlayer.play(AssetSource('mp3/4_vip.mp3'));
      });
    }
    appService.joinedUsers.add(appService.user);
    update();

    await appService.call(chat);
  }

  stop() async {
    try {
      await appService.endCall();
      await callRepository.endCall(appService.activeChat.id!);
      await _audioPlayer.stop();
      await _audioPlayer.dispose();
      try {
        final chatController = Get.find<ChatController>();
        chatController.getCallLogs();
      } catch (e) {
        log(e.toString());
      }
      Get.back();
    } catch (e) {
      log(e.toString());
      Get.back();
    }
  }

  toggleMic() async {
    log('toggling mic');
    appService.user.mute = !appService.user.mute;
    _micEnabled.value = !_micEnabled.value;
    update();
    await appService.room.localParticipant
        ?.setMicrophoneEnabled(_micEnabled.value);
  }

  toggleCamera() async {
    log('toggle camera');
    appService.room.localParticipant
        ?.setCameraEnabled(!appService.user.cameraEnabled);
    _cameraEnabled.value = !_cameraEnabled.value;
    appService.user.cameraEnabled = !appService.user.cameraEnabled;
    update();
  }

  toggleSpeaker() async {
    log('toggle speaker');
    appService.user.speakerEnabled = !appService.user.speakerEnabled;
    _speakerEnabled.value = !_speakerEnabled.value;
    update();
    await appService.room.setSpeakerOn(_speakerEnabled.value);
  }

  localVideoSource() {
    return appService.room.localParticipant?.videoTrackPublications.first.track
        as VideoTrack;
  }
}
