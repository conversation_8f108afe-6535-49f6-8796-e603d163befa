import 'dart:async';
import 'dart:developer';

import 'package:family_management/app_service.dart';
import 'package:family_management/domain/controllers/task_controller.dart';
import 'package:family_management/domain/controllers/task_details_controller.dart';
import 'package:family_management/domain/models/ferquent_task.dart';
import 'package:family_management/domain/models/task.dart' as t;
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/task_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:family_management/utils/validation.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
import 'package:multi_dropdown/multi_dropdown.dart';
import 'package:intl/intl.dart' as intl;

import 'all_tasks_controller.dart';

class AddTaskController extends GetxController {
  final appService = Get.find<AppService>();
  final TaskRepository taskRepository = gt<TaskRepository>();
  final titleController = TextEditingController();
  final descriptionController = TextEditingController();
  final responsiblePersonController = TextEditingController();
  final repeatTimeController = TextEditingController();
  final suggestedContentController = TextEditingController();
  final recurringIntervalController = TextEditingController();
  final multiSelectController = MultiSelectController<String>();

  late User user;
  late t.Task? task;

  final RxString _dueDate = ''.obs;
  final RxString selectedPriority = 'low'.obs;
  final RxInt selectedTaskType = 1.obs;
  final RxString selectedRepetition = 'Daily'.obs;
  final RxList<User> selectedAssignees = <User>[].obs;
  final Rx<t.Task> _selectedTask = t.Task().obs;
  final RxString _titleError = ''.obs;
  final RxBool _loading = false.obs;
  final RxBool _adding = false.obs;
  final RxList<User> members = <User>[].obs;
  final RxList<FerquentTask> suggestions = <FerquentTask>[].obs;
  final RxList<FerquentTask> filteredSuggestions = <FerquentTask>[].obs;
  final RxBool showSuggestions = false.obs;
  Timer? _debounceTimer;

  t.Task get selectedTask => _selectedTask.value;
  String get dueDate => _dueDate.value;
  String get titleError => _titleError.value;
  bool get loading => _loading.value;
  bool get adding => _adding.value;

  set dueDate(String value) => _dueDate.value = value;

  @override
  void onInit() {
    super.onInit();
    user = appService.user;
    task = Get.arguments;
    _fetchMembers();
    _initTask();
    _fetchSuggestions();
  }

  _initTask() {
    if (task != null) {
      titleController.text = task!.title!;
      descriptionController.text = task!.description!;
      selectedTaskType.value = task!.type!.getValue();
      selectedPriority.value = task!.priority!;
      selectedRepetition.value = task!.recurringPattern.toString();
      selectedAssignees.value = task!.assignees!;

      if (task?.dueDate != null) {
        dueDate = intl.DateFormat('y-M-d').format(task!.dueDate!);
      }
    }
  }

  _fetchMembers() async {
    _loading.value = true;
    final result = await taskRepository.getMembers();
    result.fold((l) => Utils.showToast(l.message), (r) {
      members.value = r;
      members.refresh();
    });
    _loading.value = false;
    if (task?.assignees != null) {
      final ids = task!.assignees!.map((e) => e.id.toString()).toList();
      Future.delayed(
          const Duration(milliseconds: 100),
          () =>
              multiSelectController.selectWhere((e) => ids.contains(e.value)));
    }
  }

  validate() {
    _titleError.value = titleController.text.validateRequired() ?? '';
    if (titleError.isNotEmpty) return;
    final assignees = multiSelectController.selectedItems
        .map((item) => int.parse(item.value))
        .toList();
    selectedAssignees.value =
        members.filter((user) => assignees.contains(user.id!)).toList();
    storeOrUpdate();
  }

  Future<void> storeOrUpdate() async {
    final user = appService.user;

    _adding.value = true;
    final assignees = multiSelectController.selectedItems
        .map((item) => int.parse(item.value))
        .toList();
    final result = await taskRepository.storeOrUpdate(
        id: task?.id,
        familyId: user.families.first.id!,
        title: titleController.text,
        description: descriptionController.text,
        priority: selectedPriority.value,
        dueDate: dueDate.isNotEmpty ? dueDate : null,
        assignees: assignees,
        type: selectedTaskType.value,
        recurringPattern: selectedTaskType.value == 2
            ? selectedRepetition.value.toLowerCase()
            : null,
        recurringInterval: recurringIntervalController.text);

    result.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        Utils.showToast(task != null
            ? "Task updated successfully".tr
            : "Task created successfully".tr);
        try {
          final taskController = Get.find<TaskController>();
          taskController.getTasks();
        } catch (e) {
          log('task controller not found...skipping');
        }
        try {
          final taskDetailsController = Get.find<TaskDetailsController>();
          taskDetailsController.task = r;
        } catch (e) {
          log('task details controller not found...skipping');
        }
        try {
          final allTasksController = Get.find<AllTasksController>();
          allTasksController.fetchTasks();
        } catch (e) {
          log('all tasks controller not found...skipping');
        }
        Get.back();
      },
    );
    _adding.value = false;
    clearInputs();
  }

  void clearInputs() {
    titleController.clear();
    descriptionController.clear();
    responsiblePersonController.clear();
    repeatTimeController.clear();
    suggestedContentController.clear();
    dueDate = '';
    selectedPriority.value = '';
    selectedTaskType.value = 1;
    selectedRepetition.value = '';
    recurringIntervalController.clear();
    selectedAssignees.clear();
  }

  // Fetch frequent task suggestions
  Future<void> _fetchSuggestions() async {
    final result = await taskRepository.suggestFerquent();
    result.fold(
      (l) => log('Failed to fetch suggestions: ${l.message}'),
      (r) {
        suggestions.value = r;
        suggestions.refresh();
      },
    );
  }

  // Handle title text changes with debounce
  void onTitleChanged(String value) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (value.isNotEmpty && suggestions.isNotEmpty) {
        // Filter suggestions based on input
        filteredSuggestions.value = suggestions
            .where((task) =>
                task.title != null &&
                task.title!.toLowerCase().contains(value.toLowerCase()))
            .toList();

        showSuggestions.value = filteredSuggestions.isNotEmpty;
      } else {
        filteredSuggestions.clear();
        showSuggestions.value = false;
      }
    });
  }

  // Select a suggestion
  void selectSuggestion(FerquentTask suggestion) {
    titleController.text = suggestion.title ?? '';
    showSuggestions.value = false;
  }

  // Hide suggestions
  void hideSuggestions() {
    showSuggestions.value = false;
  }

  @override
  void onClose() {
    _debounceTimer?.cancel();
    super.onClose();
  }
}
