import 'package:family_management/app_service.dart';
import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/chat_controller.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/auth_repository.dart';
import 'package:family_management/domain/repositories/chat_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:get/get.dart';

class ChooseMembersController extends GetxController {
  final appService = Get.find<AppService>();
  final chatRepository = gt<ChatRepository>();
  final authRepository = gt<AuthRepository>();

  final RxBool _loading = false.obs;
  final RxBool _creating = false.obs;
  final RxList<User> members = <User>[].obs;
  final RxList<User> selectedMembers = <User>[].obs;

  bool get loading => _loading.value;
  bool get creating => _creating.value;

  @override
  void onInit() {
    super.onInit();
    getMembers();
  }

  createChat() async {
    if (_creating.value) return;
    _creating.value = true;
    update();
    final authUser = authRepository.getAuthUser();
    final ids = [];
    for (final item in selectedMembers) {
      ids.add(item.id);
    }
    Utils.log(authUser?.families.first.id);
    final result = await chatRepository.createChat(
        members: ids, familyId: authUser!.families.first.id!);
    result.fold((l) => Utils.showToast(l.message), (r) {
      final chatController = Get.find<ChatController>();
      chatController.getChats();
      Get.offNamed(AppRoutes.privateConversation, arguments: r);
    });
    _creating.value = false;
    update();
  }

  getMembers() async {
    _loading.value = true;
    final result = await chatRepository.getMembers();
    result.fold((l) => Utils.showToast(l.message), (r) {
      members.value = r;
      members.refresh();
    });
    _loading.value = false;
    update();
  }

  bool isSelected(id) {
    return selectedMembers.firstWhereOrNull((i) => i.id == id) != null;
  }

  selectMember(User user) {
    final index = selectedMembers.indexWhere((i) => i.id == user.id);
    if (index == -1) {
      selectedMembers.add(user);
    } else {
      selectedMembers.removeAt(index);
    }
    selectedMembers.refresh();
    update();
  }
}
