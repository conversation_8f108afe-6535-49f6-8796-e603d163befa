import 'package:family_management/domain/models/notification.dart';
import 'package:family_management/domain/repositories/base_repository.dart';
import 'package:family_management/utils/failure.dart';
import 'package:family_management/utils/utils.dart';
import 'package:fpdart/fpdart.dart';

import '../../data/datasources/remote_datasources/notification_remotedatasource.dart';

class NotificationRepository extends BaseRepository {
  final NotificationRemoteDataSource remoteDataSource;

  NotificationRepository(
      {required this.remoteDataSource, required super.baseRemotedatasource});

  Future<Either<Failure, List<Notification>>> getNotifications() async {
    try {
      final response = await remoteDataSource.notifications();
      List<Notification> notifications = [];
      for (final item in response.data['data']) {
        notifications.add(Notification.fromJson(item));
      }
      return Right(notifications);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }
}
