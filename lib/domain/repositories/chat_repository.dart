import 'dart:developer';
import 'dart:io';

import 'package:family_management/data/datasources/remote_datasources/chats_remotedatasource.dart';
import 'package:family_management/domain/models/call_log.dart';
import 'package:family_management/domain/models/chat.dart';
import 'package:family_management/domain/models/message.dart';
import 'package:family_management/domain/models/shared_contact.dart';
import 'package:family_management/domain/repositories/base_repository.dart';
import 'package:family_management/utils/failure.dart';
import 'package:family_management/utils/utils.dart';
import 'package:fpdart/fpdart.dart';

class ChatRepository extends BaseRepository {
  final ChatsRemotedatasource chatsRemoteDatasource;
  ChatRepository(
      {required this.chatsRemoteDatasource,
      required super.baseRemotedatasource});

  Future<Either<Failure, List<Chat>>> getChats(
      {required bool isArchived,
      String? search}) async {
    try {
      final response =
          await chatsRemoteDatasource.getChats(archived: isArchived, search: search);
      List<Chat> chats = [];
      for (final item in response.data['data']) {
        chats.add(Chat.fromJson(item));
      }
      return Right(chats);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, Chat>> createChat(
      {required List members, required int familyId}) async {
    try {
      final response = await chatsRemoteDatasource.createChat(
          members: members, familyId: familyId);
      Chat chat = Chat.fromJson(response.data['data']);
      return Right(chat);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, List<Message>>> getMessages(int? chatId) async {
    try {
      final response = await chatsRemoteDatasource.getMessages(chatId);
      List<Message> messages = [];
      for (final item in response.data['data']) {
        messages.add(Message.fromJson(item));
      }
      return Right(messages);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, Message>> sendMessage(
      {required int chatId,
      required String message,
      required String type,
      Function(int, int)? onSendProgress,
      File? file,
      String? uuid,
      File? image,
      SharedContact? contact,
      String? voice}) async {
    try {
      log('sending phones ${contact?.phones.map((e) => e)}');
      final response = await chatsRemoteDatasource.sendMessage(
          chatId: chatId,
          message: message,
          type: type,
          file: file,
          voice: voice,
          uuid: uuid,
          image: image,
          contact: contact,
          onSendProgress: onSendProgress);
      Message res = Message.fromJson(response.data['data']);
      return Right(res);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> deleteChat(int chatId) async {
    try {
      await chatsRemoteDatasource.deleteChat(chatId);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> archiveChat(int chatId) async {
    try {
      await chatsRemoteDatasource.archiveChat(chatId);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> sendReadAll(int chatId) async {
    try {
      await chatsRemoteDatasource.readAll(chatId);
      return const Right(true);
    } catch (e) {
      log('sending read all error');
      log(e.toString());
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, List<CallLog>>> getCallLogs() async {
    try {
      final response = await chatsRemoteDatasource.callLog();
      List<CallLog> callLogs = List<CallLog>.from(
          response.data['data']?.map((e) => CallLog.fromJson(e)) ?? []);
      return Right(callLogs);
    } catch (e) {
      log(e.toString());
      return Left(Failure.handleException(e));
    }
  }
}
