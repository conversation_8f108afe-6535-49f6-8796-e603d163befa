import 'dart:developer';

import 'package:family_management/data/datasources/remote_datasources/album_remotedatasource.dart';
import 'package:family_management/domain/models/album.dart';
import 'package:family_management/domain/repositories/base_repository.dart';
import 'package:family_management/utils/failure.dart';
import 'package:fpdart/fpdart.dart';

class AlbumRepository extends BaseRepository {
  final AlbumRemotedatasource remotedatasource;
  AlbumRepository(
      {required super.baseRemotedatasource, required this.remotedatasource});

  Future<Either<Failure, Album>> create(
      {required int familyId,
      required String name,
      required int type,
      List<int>? assignedUserIds}) async {
    try {
      final response = await remotedatasource.create(
          familyId: familyId,
          name: name,
          type: type,
          assignedUserIds: assignedUserIds);
      final album = Album.fromJson(response.data['data']);
      return Right(album);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, List<Album>>> getAlbumns(
      {int? type, String? search}) async {
    try {
      final response =
          await remotedatasource.getAlbums(type: type, search: search);
      List<Album> albums = <Album>[
        ...response.data['data']?.map((item) => Album.fromJson(item)).toList()
      ];
      return Right(albums);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, Album>> getAlbum(int id) async {
    try {
      final response = await remotedatasource.getAlbum(id);
      return Right(Album.fromJson(response.data['data']));
    } catch (e) {
      log(e.toString());
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> deleteMedia(List<int> ids) async {
    try {
      await remotedatasource.delteMedia(ids);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }
}
