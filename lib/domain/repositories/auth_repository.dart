import 'dart:developer';
import 'dart:io';

import 'package:family_management/data/datasources/local_datasources/auth_localdatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/auth_remotedatasource.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/base_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/failure.dart';
import 'package:family_management/utils/utils.dart';
import 'package:fpdart/fpdart.dart';

class AuthRepository extends BaseRepository {
  final AuthRemotedatasource remotedatasource;
  final AuthLocaldatasource localdatasource;

  AuthRepository(
      {required this.remotedatasource,
      required this.localdatasource,
      required super.baseRemotedatasource});

  Future<Either<Failure, User>> login(
      {required String email, required String password}) async {
    try {
      final fcmToken = await firebaseMessaging.getToken();
      final response = await remotedatasource.login(
          email: email, password: password, fcmToken: fcmToken);
      final user = User.fromJson(response.data['data']['user']);

      final token = response.data['data']['token'];
      await localdatasource.saveToken(token);
      await localdatasource.saveUser(user);
      dioClient.options.headers = {
        'Accept': 'application/json',
        'Authorization': 'Bearer $token'
      };
      dioClient.options.queryParameters = {'family_id': user.families.first.id};
      return Right(user);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, User>> register(
      {required String name,
      required String email,
      required String password,
      String? bio,
      String? inviteCode,
      File? image}) async {
    try {
      final fcmToken = await firebaseMessaging.getToken();
      final response = await remotedatasource.register(
          name: name,
          email: email,
          password: password,
          bio: bio,
          fcmToken: fcmToken,
          inviteCode: inviteCode,
          image: image);
      final user = User.fromJson(response.data['data']['user']);
      final token = response.data['data']['token'];
      await localdatasource.saveToken(token);
      await localdatasource.saveUser(user);
      dioClient.options.headers = {
        'Accept': 'application/json',
        'Authorization': 'Bearer $token'
      };
      if (user.families.isNotEmpty) {
        dioClient.options.queryParameters = {
          'family_id': user.families.first.id
        };
      }
      return Right(user);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> forgetPassword(String email) async {
    try {
      await remotedatasource.forgetPassword(email);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> checkCode(
      {required String email, required String code}) async {
    try {
      await remotedatasource.checkCode(email: email, code: code);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> resetPassword({
    required String email,
    required String code,
    required String password,
  }) async {
    try {
      await remotedatasource.resetPassword(
          email: email, code: code, password: password);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, User>> uploadImage(File image,
      {Function(int, int)? onSendProgress}) async {
    try {
      final response = await remotedatasource.uploadImage(image,
          onSendProgress: onSendProgress);
      final user = User.fromJson(response.data['data']);
      localdatasource.saveUser(user);
      return Right(user);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, User>> updateProfile(
      {String? name, String? bio, String? password}) async {
    try {
      final response = await remotedatasource.updateProfile(
          name: name, bio: bio, password: password);
      final user = User.fromJson(response.data['data']);
      localdatasource.saveUser(user);
      return Right(user);
    } catch (e) {
      log(e.toString());
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, User>> profile() async {
    try {
      final response = await remotedatasource.profile();
      final user = User.fromJson(response.data['data']);
      return Right(user);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }

  User? getAuthUser() => localdatasource.getUser();

  String? getToken() => localdatasource.getToken();
}
