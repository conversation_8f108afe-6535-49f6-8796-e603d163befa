import 'dart:developer';

import 'package:family_management/data/datasources/remote_datasources/call_remotedatasource.dart';
import 'package:family_management/utils/failure.dart';
import 'package:fpdart/fpdart.dart';

class CallsRepository {
  final CallRemotedatasource remotedatasource;
  CallsRepository({required this.remotedatasource});

  Future<Either<Failure, String?>> generateToken(
      {required String roomName}) async {
    try {
      final response = await remotedatasource.generateToken(roomName: roomName);
      return Right(response.data['data']['token']);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> call(int chatId) async {
    try {
      await remotedatasource.call(chatId);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> acceptCall(int chatId) async {
    try {
      await remotedatasource.acceptCall(chatId);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> endCall(int chatId) async {
    try {
      log('ending call');
      await remotedatasource.endCall(chatId);
      log('call ended');
      return const Right(true);
    } catch (e) {
      log('ending call error ${e.toString()}');
      return Left(Failure.handleException(e));
    }
  }
}
