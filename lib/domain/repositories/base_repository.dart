import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/utils/failure.dart';
import 'package:fpdart/fpdart.dart';

class BaseRepository {
  final BaseRemotedatasource baseRemotedatasource;

  BaseRepository({required this.baseRemotedatasource});

  Future<Either<Failure, List<User>>> getMembers({String? search}) async {
    try {
      final response = await baseRemotedatasource.getMembers(search: search);
      List<User> members = [];
      for (final item in response.data['data']) {
        members.add(User.fromJson(item));
      }
      return Right(members);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }
}
