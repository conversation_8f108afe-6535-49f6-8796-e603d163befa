import 'dart:developer';

import 'package:family_management/data/datasources/remote_datasources/polls_remotedatasource.dart';
import 'package:family_management/domain/models/poll.dart';
import 'package:family_management/domain/repositories/base_repository.dart';
import 'package:family_management/utils/failure.dart';
import 'package:fpdart/fpdart.dart';

class PollRepository extends BaseRepository {
  final PollsRemotedatasource remotedatasource;
  PollRepository(
      {required super.baseRemotedatasource, required this.remotedatasource});

  Future<Either<Failure, List<Poll>>> getPolls() async {
    try {
      final response = await remotedatasource.getPolls();
      List<Poll> polls = <Poll>[
        ...response.data['data']?.map((item) => Poll.fromJson(item)).toList()
      ];
      return Right(polls);
    } catch (e) {
      log(e.toString());
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, Poll>> createOrUpdate({
    int? id,
    required String name,
    required List<String> items,
    required int familyId,
    List<int>? assignedUserIds,
  }) async {
    try {
      final response = await remotedatasource.createOrUpdate(
          familyId: familyId,
          name: name,
          items: items,
          assignedUserIds: assignedUserIds);
      final poll = Poll.fromJson(response.data['data']);
      return Right(poll);
    } catch (e) {
      log(e.toString());
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> vote(
      {required int pollId, required int itemId}) async {
    try {
      remotedatasource.vote(pollId: pollId, itemId: itemId);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> deletePoll(int id) async {
    try {
      await remotedatasource.deletePoll(id);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }
}
