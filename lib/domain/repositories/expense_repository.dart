import 'package:family_management/domain/models/budget.dart';
import 'package:family_management/domain/models/expense.dart' as model;
import 'package:family_management/domain/models/expense_category.dart' as model;
import 'package:family_management/domain/repositories/base_repository.dart';
import 'package:family_management/utils/failure.dart';
import 'package:family_management/utils/utils.dart';
import 'package:fpdart/fpdart.dart';

import '../../data/datasources/remote_datasources/expense_remotedatasource.dart';

class ExpenseRepository extends BaseRepository {
  final ExpenseRemoteDataSource remoteDataSource;

  ExpenseRepository(
      {required this.remoteDataSource, required super.baseRemotedatasource});

  Future<Either<Failure, Budget>> homePage({
    required int familyId,
    int page = 1,
  }) async {
    try {
      final response = await remoteDataSource.homePage(
        familyId: familyId,
        page: page,
      );

      final data = response.data['data'];
      final budget = Budget.fromJson(data);

      return Right(budget);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, List<model.ExpenseCategory>>> getExpenseCategories(
      {required int familyId, int page = 1}) async {
    try {
      final response = await remoteDataSource.expenseCategories(
          familyId: familyId, page: page);
      List<model.ExpenseCategory> categories = [];
      for (final item in response.data['data']) {
        categories.add(model.ExpenseCategory.fromJson(item));
      }
      return Right(categories);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, List<model.Expense>>> getExpenses(
      {required int familyId, int page = 1}) async {
    try {
      final response =
          await remoteDataSource.expenses(familyId: familyId, page: page);
      List<model.Expense> expenses = [];
      for (final item in response.data['data']) {
        expenses.add(model.Expense.fromJson(item));
      }
      return Right(expenses);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> deleteExpense({required int expenseId}) async {
    try {
      await remoteDataSource.deleteExpense(expenseId);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> storeOrUpdateExpense({
    int? id,
    required int familyId,
    required int expenseCategoryId,
    required String description,
    required String date,
    required int amount,
    String? type,
  }) async {
    try {
      await remoteDataSource.storeOrUpdateExpense(
        id: id,
        familyId: familyId,
        expenseCategoryId: expenseCategoryId,
        description: description,
        date: date,
        amount: amount,
        type: type,
      );
      return const Right(true);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }
}
