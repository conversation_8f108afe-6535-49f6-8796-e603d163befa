import 'package:family_management/data/datasources/remote_datasources/task_remotedatasource.dart';
import 'package:family_management/domain/models/ferquent_task.dart';
import 'package:family_management/domain/models/leaderboard.dart';
import 'package:family_management/domain/models/sub_task.dart';
import 'package:family_management/domain/models/task.dart' as model;
import 'package:family_management/domain/repositories/base_repository.dart';
import 'package:family_management/utils/failure.dart';
import 'package:family_management/utils/utils.dart';
import 'package:fpdart/fpdart.dart';

class TaskRepository extends BaseRepository {
  final TaskRemoteDataSource remoteDataSource;

  TaskRepository(
      {required this.remoteDataSource, required super.baseRemotedatasource});

  Future<Either<Failure, List<model.Task>>> getTasks(
      {required int familyId,
      int page = 1,
      String? status,
      String? search}) async {
    try {
      final response = await remoteDataSource.tasks(
          familyId: familyId, page: page, search: search);
      List<model.Task> tasks = [];
      for (final item in response.data['data']) {
        tasks.add(model.Task.formJson(item));
      }
      return Right(tasks);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, model.Task>> storeOrUpdate({
    int? id,
    required int familyId,
    required String title,
    String? description,
    required String priority,
    String? dueDate,
    required List<int> assignees,
    required int type,
    String? recurringPattern,
    String? recurringInterval,
  }) async {
    try {
      final response = await remoteDataSource.storeOrUpdate(
        id: id,
        familyId: familyId,
        title: title,
        description: description,
        priority: priority,
        dueDate: dueDate,
        assignees: assignees,
        type: type,
        recurringPattern: recurringPattern,
        recurringInterval: recurringInterval,
      );
      final task = model.Task.formJson(response.data['data']);
      return Right(task);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, model.Task>> updateTask({
    required int id,
    String? title,
    String? description,
    String? priority,
    String? dueDate,
    String? status,
  }) async {
    try {
      final response = await remoteDataSource.updateTask(
        id: id,
        title: title,
        description: description,
        priority: priority,
        dueDate: dueDate,
        status: status,
      );

      final task = model.Task.formJson(response.data['data']);
      return Right(task);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, SubTask>> addSubTask({
    required int taskId,
    required String content,
    required String priority,
  }) async {
    try {
      final response = await remoteDataSource.addSubtask(
          taskId: taskId, content: content, priority: priority);

      final subTask = SubTask.fromJson(response.data['data']);
      return Right(subTask);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> deleteSubTask({required int taskId}) async {
    try {
      await remoteDataSource.deleteSubtask(taskId);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, SubTask>> updateSubTask({
    required int taskId,
    String? content,
    String? status,
  }) async {
    try {
      final response = await remoteDataSource.editSubtask(taskId,
          content: content, status: status);
      final task = SubTask.fromJson(response.data['data']);
      return Right(task);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, List<FerquentTask>>> suggestFerquent() async {
    try {
      final response = await remoteDataSource.suggestFerquent();
      List<FerquentTask> tasks = <FerquentTask>[
        ...response.data['data']?.map((e) => FerquentTask.fromJson(e)).toList()
      ];

      return Right(tasks);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, List<LeaderBoard>>> leaderboard() async {
    try {
      final response = await remoteDataSource.leaderboard();
      return Right(<LeaderBoard>[
        ...response.data['data']?.map((e) => LeaderBoard.fromJson(e)).toList()
      ]);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, List<model.Task>>> overdue() async {
    try {
      final response = await remoteDataSource.overdue();
      return Right(<model.Task>[
        ...response.data['data']?.map((e) => model.Task.formJson(e)).toList()
      ]);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }
}
