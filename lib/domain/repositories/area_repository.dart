import 'package:family_management/data/datasources/remote_datasources/area_remotedatasource.dart';
import 'package:family_management/domain/models/area.dart';
import 'package:family_management/domain/repositories/base_repository.dart';
import 'package:family_management/utils/failure.dart';
import 'package:fpdart/fpdart.dart';

class AreaRepository extends BaseRepository {
  final AreaRemotedatasource remotedatasource;
  AreaRepository(
      {required super.baseRemotedatasource, required this.remotedatasource});

  Future<Either<Failure, List<Area>>> getAreas() async {
    try {
      final response = await remotedatasource.getAreas();
      final List<Area> areas = <Area>[
        ...response.data['data'].map((e) => Area.fromJson(e)).toList()
      ];
      return Right(areas);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, Area>> create(
      {required String name, required List points}) async {
    try {
      final response =
          await remotedatasource.create(name: name, points: points);
      final area = Area.fromJson(response.data['data']);
      return Right(area);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> deleteArea(int id) async {
    try {
      await remotedatasource.deleteArea(id);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> enterArea({required int areaId}) async {
    try {
      await remotedatasource.enterArea(areaId: areaId);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }
}
