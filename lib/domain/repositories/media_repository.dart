import 'dart:developer';

import 'package:family_management/data/datasources/remote_datasources/media_remotedatasource.dart';
import 'package:family_management/domain/models/media.dart';
import 'package:family_management/domain/repositories/base_repository.dart';
import 'package:family_management/utils/failure.dart';
import 'package:fpdart/fpdart.dart';
import 'dart:io';

class MediaRepository extends BaseRepository {
  final MediaRemotedatasource remotedatasource;
  MediaRepository(
      {required super.baseRemotedatasource, required this.remotedatasource});

  Future<Either<Failure, List<Media>>> media(
      {int? albumId, String? search, int page = 1}) async {
    try {
      final response = await remotedatasource.media(
          albumId: albumId, search: search, page: page);
      final List<Media> medias = <Media>[
        ...response.data['data'].map((e) => Media.fromJson(e)).toList()
      ];
      return Right(medias);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> create(
      {required File image,
      required int albumId,
      Function(int, int)? onSendProgress}) async {
    try {
      await remotedatasource.create(
          albumId: albumId, image: image, onSendProgress: onSendProgress);
      return const Right(true);
    } catch (e) {
      log(e.toString());
      return Left(Failure.handleException(e));
    }
  }
}
