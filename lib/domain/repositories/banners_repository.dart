import 'package:family_management/data/datasources/remote_datasources/banners_remotedatasource.dart';
import 'package:family_management/domain/models/banner.dart';
import 'package:family_management/utils/failure.dart';
import 'package:fpdart/fpdart.dart';

class BannersRepository {
  final BannersRemotedatasource remotedatasource;

  BannersRepository({required this.remotedatasource});

  Future<Either<Failure, List<Banner>>> getBanners() async {
    try {
      final response = await remotedatasource.getBanners();
      List<Banner> list = <Banner>[
        ...response.data['data'].map((e) => Banner.fromJson(e)).toList()
      ];
      return Right(list);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }
}
