import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

const primarySwatch = MaterialColor(0xff134982, {
  50: Color(0xff134982),
  100: Color(0xff134982),
  200: Color(0xff134982),
  300: Color(0xff134982),
  400: Color(0xff134982),
  500: Color(0xff134982),
  600: Color(0xff134982),
  700: Color(0xff134982),
  800: Color(0xff134982),
  900: Color(0xff134982),
});
const pink = Color(0xffEC639E);
const grey = Color(0xffB9C9DA);
const yellow = Color(0xffECB365);
const lightPink = Color(0x66EC639E);
const lightPink2 = Color(0x1AEC639E);
const green = Color(0xFF259245);
const lightGreen = Color(0xFF7CA5A5);
const lightBlue = Color(0x80134982);
final lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: const ColorScheme.light().copyWith(primary: primarySwatch),
    textTheme: GoogleFonts.poppinsTextTheme(),
    primaryColor: primarySwatch,
    iconTheme: const IconThemeData(color: primarySwatch),
    appBarTheme: const AppBarTheme(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle()),
    primarySwatch: primarySwatch);
final darkTheme = ThemeData(
    brightness: Brightness.dark,
    useMaterial3: true,
    iconTheme: const IconThemeData(color: primarySwatch),
    textTheme: GoogleFonts.poppinsTextTheme(),
    primaryColor: primarySwatch,
    primarySwatch: primarySwatch);
