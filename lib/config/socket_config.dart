import 'package:dart_pusher_channels/dart_pusher_channels.dart';
import 'package:family_management/config/urls.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

PusherChannelsOptions socketOptions = const PusherChannelsOptions.fromHost(
  scheme: 'ws',
  host: socketUrl,
  key: reverbKey,
  // port: socketPort,
);
PusherChannelsClient socketClient = PusherChannelsClient.websocket(
    options: socketOptions,
    connectionErrorHandler: (e, trace, refresh) => Utils.log(e));

PublicChannel publicNotification =
    socketClient.publicChannel('public_notification');

final notificationPlugin = FlutterLocalNotificationsPlugin();
