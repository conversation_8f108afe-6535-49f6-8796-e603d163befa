import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/bindings/achievement_board_binding.dart';
import 'package:family_management/domain/bindings/auth_check_binding.dart';
import 'package:family_management/domain/bindings/add_event_binding.dart';
import 'package:family_management/domain/bindings/add_poll_binding.dart';
import 'package:family_management/domain/bindings/add_shopping_and_meal_binding.dart';
import 'package:family_management/domain/bindings/add_task_binding.dart';
import 'package:family_management/domain/bindings/all_events_binding.dart';
import 'package:family_management/domain/bindings/all_tasks_binding.dart';
import 'package:family_management/domain/bindings/call_binding.dart';
import 'package:family_management/domain/bindings/chat_binding.dart';
import 'package:family_management/domain/bindings/choose_members_binding.dart';
import 'package:family_management/domain/bindings/conversation_binding.dart';
import 'package:family_management/domain/bindings/editeprofile.dart';
import 'package:family_management/domain/bindings/event_description_binding.dart';
import 'package:family_management/domain/bindings/event_details_binding.dart';
import 'package:family_management/domain/bindings/expense_details_binding.dart';
import 'package:family_management/domain/bindings/files_binding.dart';
import 'package:family_management/domain/bindings/gallery_binding.dart';
import 'package:family_management/domain/bindings/home_binding.dart';
import 'package:family_management/domain/bindings/invite_email_binding.dart';
import 'package:family_management/domain/bindings/invite_members_binding.dart';
import 'package:family_management/domain/bindings/login_binding.dart';
import 'package:family_management/domain/bindings/map_binding.dart';
import 'package:family_management/domain/bindings/meal_details_binding.dart';
import 'package:family_management/domain/bindings/notifications_binding.dart';
import 'package:family_management/domain/bindings/polls_binding.dart';
import 'package:family_management/domain/bindings/register_binding.dart';
import 'package:family_management/domain/bindings/register_family_binding.dart';
import 'package:family_management/domain/bindings/shopping_and_meal_binding.dart';
import 'package:family_management/domain/bindings/task_binding.dart';
import 'package:family_management/domain/bindings/task_details_binding.dart';
import 'package:family_management/domain/bindings/view_contact_binding.dart';
import 'package:family_management/presentation/achievement_board/achievement_board_details_view.dart';
import 'package:family_management/presentation/achievement_board/achievement_board_view.dart';
import 'package:family_management/presentation/achievement_board/event_description_view.dart';
import 'package:family_management/presentation/auth/auth_check_view.dart';
import 'package:family_management/presentation/auth/forget_password/enter_email_view.dart';
import 'package:family_management/presentation/auth/forget_password/forget_password_view.dart';
import 'package:family_management/presentation/auth/forget_password/reset_success_view.dart';
import 'package:family_management/presentation/auth/forget_password/verify_code.dart';
import 'package:family_management/presentation/auth/invite_email_view.dart';
import 'package:family_management/presentation/auth/login_view.dart';
import 'package:family_management/presentation/auth/register_family_view.dart';
import 'package:family_management/presentation/auth/register_view.dart';
import 'package:family_management/presentation/budget_tracking/add_expense_view.dart';
import 'package:family_management/presentation/budget_tracking/budget_tracking_view.dart';
import 'package:family_management/presentation/budget_tracking/expense_details_view.dart';
import 'package:family_management/presentation/budget_tracking/total_expense_view.dart';
import 'package:family_management/presentation/calendar/add_event_view.dart';
import 'package:family_management/presentation/calendar/all_events_view.dart';
import 'package:family_management/presentation/calendar/event_details_view.dart';
import 'package:family_management/presentation/chat/call_view.dart';
import 'package:family_management/presentation/chat/choose_members_view.dart';
import 'package:family_management/presentation/chat/contact_details_view.dart';
import 'package:family_management/presentation/chat/group_chat_view.dart';
import 'package:family_management/presentation/chat/home_chat_view.dart';
import 'package:family_management/presentation/chat/private_conversation_view.dart';
import 'package:family_management/presentation/chat/select_contact_view.dart';
import 'package:family_management/presentation/gallery/album_media_view.dart';
import 'package:family_management/presentation/gallery/delete_similar_photos_view.dart';
import 'package:family_management/presentation/gallery/files_view.dart';
import 'package:family_management/presentation/gallery/gallery_view.dart';
import 'package:family_management/presentation/gallery/similar_photos_view.dart';
import 'package:family_management/presentation/home/<USER>';
import 'package:family_management/presentation/location_tracking/draw_area_view.dart';
import 'package:family_management/presentation/location_tracking/map_view.dart';
import 'package:family_management/presentation/notifications/notifications_view.dart';
import 'package:family_management/presentation/shopping_and_meal/add_poll_view.dart';
import 'package:family_management/presentation/shopping_and_meal/add_shopping_list_view.dart';
import 'package:family_management/presentation/shopping_and_meal/confirm_add_meal_view.dart';
import 'package:family_management/presentation/shopping_and_meal/done_meal_view.dart';
import 'package:family_management/presentation/shopping_and_meal/meal_details.dart';
import 'package:family_management/presentation/shopping_and_meal/meal_view.dart';
import 'package:family_management/presentation/shopping_and_meal/polls_view.dart';
import 'package:family_management/presentation/shopping_and_meal/restaurant_description_view.dart';
import 'package:family_management/presentation/shopping_and_meal/shopping_list_view.dart';
import 'package:family_management/presentation/task/add_task_view.dart';
import 'package:family_management/presentation/task/all_tasks.dart';
import 'package:family_management/presentation/task/confirm_add_task_view.dart';
import 'package:family_management/presentation/task/done_add_task_view.dart';
import 'package:family_management/presentation/task/home_task_view.dart';
import 'package:family_management/presentation/task/task_details_view.dart';
import 'package:get/get.dart';
import '../domain/bindings/achievement_board_details_binding.dart';
import '../domain/bindings/add_expense_binding.dart';
import '../domain/bindings/expense_binding.dart';
import '../domain/bindings/start_new_chat_binding.dart';
import '../presentation/chat/group_video_call_view.dart';
import '../presentation/chat/start_new_chat_view.dart';
import '../presentation/profile/edit_profile_view.dart';
import '../presentation/profile/invite_members_view.dart';

final getPages = <GetPage>[
  GetPage(
    name: AppRoutes.authCheck,
    page: () => const AuthCheckView(),
    binding: AuthCheckBinding(),
  ),
  GetPage(
    name: AppRoutes.home,
    page: () => const HomeView(),
    binding: HomeBinding(),
  ),
  GetPage(
      name: AppRoutes.login,
      page: () => const LoginView(),
      binding: LoginBinding()),
  GetPage(
      name: AppRoutes.chats,
      page: () => const HomeChatView(),
      binding: ChatBinding()),
  GetPage(
      name: AppRoutes.register,
      page: () => const RegisterView(),
      binding: RegisterBinding()),
  GetPage(name: AppRoutes.enterEmail, page: () => const EnterEmailView()),
  GetPage(name: AppRoutes.verifyCode, page: () => const VerifyCodeView()),
  GetPage(
      name: AppRoutes.forgetPassword, page: () => const ForgetPasswordView()),
  GetPage(name: AppRoutes.resetSuccess, page: () => const ResetSuccessView()),
  GetPage(
      name: AppRoutes.inviteEmail,
      page: () => const InviteEmailView(),
      binding: InviteEmailBinding()),
  GetPage(
      name: AppRoutes.registerFamily,
      page: () => const RegisterFamilyView(),
      binding: RegisterFamilyBinding()),
  GetPage(
      name: AppRoutes.privateConversation,
      page: () => const PrivateConversationView(),
      binding: ConversationBinding()),
  GetPage(
      name: AppRoutes.groupConversation,
      page: () => const GroupChatView(),
      binding: ConversationBinding()),
  GetPage(
      name: AppRoutes.chooseMembers,
      page: () => const ChooseMembersView(),
      binding: ChooseMembersBinding()),
  GetPage(
      name: AppRoutes.startNewChat,
      page: () => const StartNewChatView(),
      binding: StartNewChatBinding()),
  GetPage(
      name: AppRoutes.callScreen,
      page: () => const CallView(),
      binding: CallBinding()),
  GetPage(
      name: AppRoutes.notifications,
      page: () => const NotificationsView(),
      binding: NotificationsBinding()),
  GetPage(
      name: AppRoutes.addEvent,
      page: () => const AddEventView(),
      binding: AddEventBinding()),
  GetPage(
      name: AppRoutes.eventDetails,
      page: () => const EventDetailsView(),
      binding: EventDetailsBinding()),
  GetPage(
      name: AppRoutes.achievementBoard,
      page: () => const AchievementBoardView(),
      binding: AchievementBoardBinding()),
  GetPage(
    name: AppRoutes.achievementBoardDetails,
    page: () => const AchievementBoardDetailsView(),
    binding: AchievementBoardDetailsBinding(),
  ),
  GetPage(
      name: AppRoutes.taskDetails,
      page: () => const TaskDetailsView(),
      binding: TaskDetailsBinding()),
  GetPage(
    name: AppRoutes.eventDescription,
    page: () => const EventDescriptionView(),
    binding: EventDescriptionBinding(),
  ),
  GetPage(
    name: AppRoutes.tasks,
    page: () => const HomeTaskView(),
    binding: TaskBinding(),
  ),
  GetPage(
    name: AppRoutes.addTask,
    page: () => const AddTaskView(),
    binding: AddTaskBinding(),
  ),
  GetPage(
    name: AppRoutes.confirmedAddTask,
    page: () => const ConfirmAddTaskView(),
  ),
  GetPage(
    name: AppRoutes.doneAddTask,
    page: () => const DoneAddTaskView(),
  ),
  GetPage(
    name: AppRoutes.editProfile,
    page: () => const EditprofileView(),
    binding: EditProfileBinding(),
  ),
  GetPage(
      name: AppRoutes.allEvents,
      page: () => const AllEventsView(),
      binding: AllEventsBinding()),
  GetPage(
      name: AppRoutes.shoppingAndMeal,
      page: () => const ShoppingListView(),
      binding: ShoppingAndMealBinding()),
  GetPage(
    name: AppRoutes.meal,
    page: () => const MealView(),
    binding: ShoppingAndMealBinding(),
  ),
  GetPage(
    name: AppRoutes.doneMeal,
    page: () => const DoneMealView(),
    binding: ShoppingAndMealBinding(),
  ),
  GetPage(
    name: AppRoutes.addShoppingList,
    page: () => const AddShoppingListView(),
    binding: AddShoppingAndMealBinding(),
  ),
  GetPage(
    name: AppRoutes.confirmAddMeal,
    page: () => const ConfirmAddMealView(),
    binding: ShoppingAndMealBinding(),
  ),
  GetPage(
    name: AppRoutes.restaurantDescription,
    page: () => const RestaurantDescriptionView(),
    binding: ShoppingAndMealBinding(),
  ),
  GetPage(
    name: AppRoutes.gallery,
    page: () => const GalleryView(),
    binding: GalleryBinding(),
  ),
  GetPage(
    name: AppRoutes.similarPhoto,
    page: () => const SimilarPhotosView(),
    binding: GalleryBinding(),
  ),
  GetPage(
    name: AppRoutes.deleteSimilarPhoto,
    page: () => const DeleteSimilarPhotosView(),
    binding: GalleryBinding(),
  ),
  GetPage(
      name: AppRoutes.addPoll,
      page: () => const AddPollView(),
      binding: AddPollBinding()),
  GetPage(
      name: AppRoutes.mealDetails,
      page: () => const MealDetails(),
      binding: MealDetailsBinding()),
  GetPage(
    name: AppRoutes.albumGallery,
    page: () => const AlbumMediaView(),
  ),
  GetPage(
    name: AppRoutes.budgetTracking,
    page: () => const BudgetTrackingView(),
    binding: ExpenseBinding(),
  ),
  GetPage(
    name: AppRoutes.addExpense,
    page: () => const AddExpenseView(),
    binding: AddExpenseBinding(),
  ),
  GetPage(
    name: AppRoutes.totalExpense,
    page: () => const TotalExpenseView(),
  ),
  GetPage(
    name: AppRoutes.expenseDetails,
    page: () => const ExpenseDetailsView(),
    binding: ExpenseDetailsBinding(),
  ),
  GetPage(
    name: AppRoutes.groupVideoCall,
    page: () => const GroupVideoCallView(),
  ),
  GetPage(name: AppRoutes.selectContact, page: () => const SelectContactView()),
  GetPage(
      name: AppRoutes.viewContact,
      page: () => const ContactDetailsView(),
      binding: ViewContactBinding()),
  GetPage(
      name: AppRoutes.map, page: () => const MapView(), binding: MapBinding()),
  GetPage(
      name: AppRoutes.addArea,
      page: () => const DrawAreaView(),
      binding: MapBinding()),
  GetPage(
      name: AppRoutes.files,
      page: () => const FilesView(),
      binding: FilesBinding()),
  GetPage(
      name: AppRoutes.allTasks,
      page: () => const AllTasksView(),
      binding: AllTasksBinding()),
  GetPage(
      name: AppRoutes.polls,
      page: () => const PollsView(),
      binding: PollsBinding()),
  GetPage(
      name: AppRoutes.inviteMembers,
      page: () => const InviteMembersView(),
      binding: InviteMembersBinding())
];
