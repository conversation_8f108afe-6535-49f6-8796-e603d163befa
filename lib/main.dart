import 'package:family_management/config/app_routes.dart';
import 'package:family_management/config/pages.dart';
import 'package:family_management/domain/bindings/auth_check_binding.dart';
import 'package:family_management/app_service.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

void main() async {
  await init();
  // startBackgroundService();
  final appService = Get.find<AppService>();

  runApp(GetMaterialApp(
    debugShowCheckedModeBanner: false,
    initialBinding: AuthCheckBinding(),
    defaultTransition: Transition.cupertino, // Change this!
    // transitionDuration: const Duration(milliseconds: 500),
    initialRoute: AppRoutes.authCheck,
    theme: appService.getTheme(),
    locale: appService.locale,
    getPages: getPages,
    translations: LocalizationService(),
  ));
}
