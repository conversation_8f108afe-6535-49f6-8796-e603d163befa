import 'package:family_management/domain/models/user.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter_callkit_incoming/entities/entities.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

class CallService {
  static Future<void> requestPermissions() async {
    await Permission.phone.request();
    // await FlutterCallkitIncoming.requestFullIntentPermission();
  }

  static Future<void> showOutgoingAudioCall(User receiver) async {
    Utils.log('starting call with ${receiver.name}');
    final activeCalls = await FlutterCallkitIncoming.activeCalls();
    if (activeCalls?.length > 0) {
      Utils.log(activeCalls);
      await FlutterCallkitIncoming.endAllCalls();
      Utils.showToast('There is another active call'.tr);
      return;
    }
    CallKitParams params = CallKitParams(
        id: receiver.id.toString(),
        nameCaller: receiver.name,
        handle: receiver.name,
        type: 1,
        duration: 30000,
        avatar: receiver.avatar,
        appName: receiver.name,
        // extra: <String, dynamic>{'userId': receiver.id.toString()},
        ios: const IOSParams(handleType: 'generic'),
        android: const AndroidParams(
          isShowFullLockedScreen: true,
          isImportant: true,
        ));
    await FlutterCallkitIncoming.startCall(params)
        .catchError((e) => Utils.log(e));
    Utils.log('call started');
  }

  static Future<void> showIncomingAudioCall(User caller) async {
    await FlutterCallkitIncoming.showCallkitIncoming(CallKitParams(
        id: caller.id.toString(),
        nameCaller: caller.name,
        appName: 'Incoming call',
        avatar: caller.avatar,
        handle: caller.name,
        type: 0, // 0 = Audio, 1 = Video
        duration: 30000, // Call duration in ms
        textAccept: 'Accept'.tr,
        textDecline: 'Decline'.tr,
        missedCallNotification: NotificationParams(
          showNotification: true,
          subtitle: "Missed call".tr,
        ),
        android: AndroidParams(
            isShowLogo: false,
            backgroundColor: '#0955fa',
            backgroundUrl: caller.avatar,
            actionColor: '#4CAF50',
            isShowFullLockedScreen: true,
            isImportant: true,
            textColor: '#ffffff',
            incomingCallNotificationChannelName: "Incoming Call".tr,
            missedCallNotificationChannelName: "Missed Call".tr,
            isShowCallID: false)));
  }

  static Future<void> endCall(id) async {
    await FlutterCallkitIncoming.endCall(id);
  }

  static Future<void> endAllCalls() async {
    await FlutterCallkitIncoming.endAllCalls();
  }
}
