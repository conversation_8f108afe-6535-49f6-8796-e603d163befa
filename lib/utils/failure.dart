import 'package:family_management/utils/exceptions.dart';
import 'package:get/get.dart';

class Failure {
  final String message;
  Failure({required this.message});

  static handleException(e) {
    if (e is NetworkException) {
      return Failure(message: 'Network error'.tr);
    }
    if (e is ResponseException) {
      return Failure(message: e.message ?? 'server error');
    }
    return Failure(message: 'Unknown error'.tr);
  }
}
