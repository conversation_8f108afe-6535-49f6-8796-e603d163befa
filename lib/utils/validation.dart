import 'package:get/get.dart';

extension StringValidator on String {
  String? validateEmail() {
    final check = RegExp(
            r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
        .hasMatch(this);
    return check ? null : 'please enter a valid email address'.tr;
  }

  String? validatePhone() {
    final check =
        RegExp(r'^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$')
            .hasMatch(this);
    return check ? null : 'the phone number is not valid'.tr;
  }

  String? validateRequired() {
    return isEmpty ? 'this field is required'.tr : null;
  }

  String? validateNumbers() {
    final check = RegExp(r'^\d+$').hasMatch(this);
    return check ? null : 'only numbers are allowed'.tr;
  }
}

class Validator {
  validateEmail(String email) {
    if (email.isEmpty) return false;
    if (!email.contains('@')) return false;
    final parts = email.split('@');
    if (!parts[1].contains('.')) return false;
    return true;
  }
}
