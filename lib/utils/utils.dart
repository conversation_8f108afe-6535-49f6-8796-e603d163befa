import 'package:flutter/foundation.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:intl/intl.dart' as intl;

class Utils {
  static showToast(String message) {
    Fluttertoast.showToast(msg: message, gravity: ToastGravity.BOTTOM);
  }

  static log(message) {
    if (kDebugMode) {
      print('\x1B[31m$message\x1B[0m');
    }
  }

  static formatDate(DateTime date) {
    return intl.DateFormat('dd / MM / y').format(date);
  }

  static requestMediaPermissoins() async {
    if (await Permission.mediaLibrary.status.isDenied) {
      return await Permission.mediaLibrary.request().isGranted;
    }

    return await Permission.mediaLibrary.status.isGranted;
  }

  static requestStoragePermissions() async {
    // if (await Permission.storage.status.isDenied) {
    return await Permission.storage.request().isGranted;
    // }
    // return await Permission.storage.status.isGranted;
  }

  static requestContactsPermission() async {
    return await FlutterContacts.requestPermission();
  }

  static requestLocationPermission() async {
    return await Permission.location.request().isGranted;
  }

  static requestCameraPermissions() async {
    if (await Permission.camera.status.isDenied) {
      return await Permission.camera.request().isGranted;
    }

    return await Permission.camera.status.isGranted;
  }
}
