import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:ui';

import 'package:battery_plus/battery_plus.dart';
import 'package:dio/dio.dart';
import 'package:family_management/config/urls.dart';
import 'package:family_management/data/datasources/remote_datasources/area_remotedatasource.dart';
import 'package:family_management/data/datasources/remote_datasources/call_remotedatasource.dart';
import 'package:family_management/data/services/local_storage.dart';
import 'package:family_management/domain/models/area.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/area_repository.dart';
import 'package:family_management/domain/repositories/calls_repository.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:livekit_client/livekit_client.dart';

@pragma('vm:entry-point')
class LocationService {
  static Room locationRoom = Room();

  // Track which areas the user is currently inside to avoid duplicate API calls
  static Set<int> _currentlyInsideAreas = <int>{};

  // this function to create notification channel for foreground service
  static createNotificationChannel() async {
    AndroidNotificationChannel channel = const AndroidNotificationChannel(
      'location', // id
      'Location', // title
      description:
          'This channel is for location related notifications.', // description
      importance: Importance.low, // importance must be at low or higher level
    );
    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.requestNotificationsPermission();
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
    return channel;
  }

  static getStorageValue(String key) async {
    await GetStorage.init();
    final storage = GetStorage();
    return storage.read(key);
  }

  static getAuthUser() async {
    final userJson = await getStorageValue('user');
    final user = User.fromJson(jsonDecode(userJson ?? ''));
    return user;
  }

  static sendUpdateLocation(double lat, double lng) async {
    final user = await getAuthUser();
    final battery = Battery();
    await sendPostRequest('update-location', {
      'lat': lat,
      'lng': lng,
      'family_id': user.families.first.id,
      'metadata': {'battery': await battery.batteryLevel}
    });
  }

  static stopStreaming() async {
    final user = await getAuthUser();
    await sendPostRequest(
        'stop-location', {'family_id': user.families.first.id});
  }

  static sendPostRequest(String endpoint, data) async {
    final token = await getStorageValue('token');
    log('authentication token $token');
    final dio = Dio(BaseOptions(baseUrl: apiUrl, headers: {
      'Accept': 'application/json',
      'Authorization': 'Bearer $token'
    }));
    try {
      await dio.post(endpoint, data: data);
    } on DioException catch (e) {
      log('update location error ${e.response?.data['message']}');
    } catch (e) {
      log('update location error ${e.toString()}');
    }
  }

  static initAreaRepository() async {
    final token = await getStorageValue('token');
    final user = await getStorageValue('user');
    final userObj = user != null ? User.fromJson(jsonDecode(user)) : null;

    final dio = Dio(BaseOptions(baseUrl: apiUrl, headers: {
      'Accept': 'application/json',
      'Authorization': 'Bearer $token'
    }, queryParameters: {
      'family_id': userObj?.families.first.id
    }));

    final AreaRemotedatasource datasource = AreaRemotedatasource(dio: dio);
    final areaRepository = AreaRepository(
        baseRemotedatasource: datasource, remotedatasource: datasource);
    return areaRepository;
  }

  static Future<List<Area>> getAreas() async {
    try {
      final repository = await initAreaRepository();
      final result = await repository.getAreas();
      return result.fold((l) {
        log('Error getting areas: ${l.message}');
        return <Area>[];
      }, (r) => r);
    } catch (e) {
      log('Error getting areas: ${e.toString()}');
      return <Area>[];
    }
  }

  static sendEnterAreaRequest(int areaId) async {
    try {
      log('User entered area with ID: $areaId');
      final repository = await initAreaRepository();
      final result = await repository.enterArea(areaId: areaId);
      result.fold(
        (l) => log('Error sending enter area request: ${l.message}'),
        (r) => log('Successfully sent enter area request for area $areaId'),
      );
    } catch (e) {
      log('Error sending enter area request: ${e.toString()}');
    }
  }

  static checkAreaEntry(Position position) async {
    try {
      final areas = await getAreas();
      final currentLocation = LatLng(position.latitude, position.longitude);

      // Check which areas the user is currently inside
      Set<int> currentAreas = <int>{};

      for (final area in areas) {
        if (area.id != null && area.points.isNotEmpty) {
          if (isPointInPolygon(currentLocation, area.points)) {
            currentAreas.add(area.id!);

            // If user just entered this area (wasn't inside before)
            if (!_currentlyInsideAreas.contains(area.id!)) {
              await sendEnterAreaRequest(area.id!);
            }
          }
        }
      }

      // Update the currently inside areas set
      _currentlyInsideAreas = currentAreas;
    } catch (e) {
      log('Error checking area entry: ${e.toString()}');
    }
  }

  static startStreamLocationService() async {
    await createNotificationChannel();
    final service = FlutterBackgroundService();
    await service.configure(
      iosConfiguration: IosConfiguration(),
      androidConfiguration: AndroidConfiguration(
          onStart: LocationService.onServiceStart,
          isForegroundMode: true,
          notificationChannelId: 'location',
          initialNotificationTitle: 'Sharing your location'.tr,
          initialNotificationContent:
              'Share location service working in background'.tr,
          foregroundServiceNotificationId: 888,
          autoStart: false,
          autoStartOnBoot: false,
          foregroundServiceTypes: [AndroidForegroundType.location]),
    );
    return await service.startService();
  }

  static stopStreamLocationService() {
    final service = FlutterBackgroundService();
    stopStreaming();
    service.invoke('stopService');

    log('location service stopped');
  }

  static isServiceRunning() async {
    final service = FlutterBackgroundService();
    return await service.isRunning();
  }

  @pragma('vm:entry-point')
  static onServiceStart(ServiceInstance service) async {
    try {
      log('starting location service');
      WidgetsFlutterBinding.ensureInitialized();
      DartPluginRegistrant.ensureInitialized();
      log('creating stop  listener');
      service.on('stopService').listen((e) {
        log('stop service recieved');
        service.stopSelf();
      });
//show stop button in the notification to stop the service
      // if (service is AndroidServiceInstance) {
      //   log('creating notification');
      //   const AndroidNotificationDetails androidPlatformChannelSpecifics =
      //       AndroidNotificationDetails(
      //     'location',
      //     'Location',
      //     ongoing: true,
      //     actions: <AndroidNotificationAction>[
      //       AndroidNotificationAction(
      //         'STOP_ACTION',
      //         'Stop',
      //         showsUserInterface: true,
      //       ),
      //     ],
      //   );

      //   const NotificationDetails platformChannelSpecifics =
      //       NotificationDetails(android: androidPlatformChannelSpecifics);
      //   final FlutterLocalNotificationsPlugin flnp =
      //       FlutterLocalNotificationsPlugin();
      //   // Handle notification action
      //   log('create notification action');
      //   flnp.initialize(
      //     const InitializationSettings(),
      //     onDidReceiveNotificationResponse: (response) {
      //       if (response.actionId == 'STOP_ACTION') {
      //         log('stopping service');
      //         FlutterBackgroundService().invoke('stopService');
      //       }
      //     },
      //   );
      //   log('showing notification');
      //   await flnp.show(
      //     888,
      //     'Service Running'.tr,
      //     'Tap stop to stop the service'.tr,
      //     platformChannelSpecifics,
      //     payload: 'STOP_ACTION',
      //   );
      // }
      log('start streaming location');
      LocationService.streamLocation();
    } catch (e) {
      log(e.toString());
    }
  }

  static generateLocationRoomToken() async {
    var granted = await Utils.requestLocationPermission();
    if (!granted) {
      return;
    }
    String? token;

    final repository = initRepository();
    final result =
        await repository.generateToken(roomName: 'locationTracking'.toString());
    result.fold((l) => Utils.showToast(l.message), (r) => token = r);
    return token;
  }

  static initRepository() {
    final dio = Dio(BaseOptions(baseUrl: apiUrl, headers: {
      'Accept': 'application/json',
      'Authorization': 'Bearer ${LocalStorage.getToken()}'
    }, queryParameters: {
      'family_id': LocalStorage.getUser()?.families.first.id
    }));
    final CallRemotedatasource datasource = CallRemotedatasource(dio: dio);
    final callRepository = CallsRepository(remotedatasource: datasource);
    return callRepository;
  }

  static streamLocation({Function(Position position)? extraHandler}) async {
    log('started streaming location');
    // Timer.periodic(const Duration(seconds: 10), (_) async {
    //   log('location timer started');
    //   final position = await Geolocator.getCurrentPosition();
    //   shareLocation(position);
    // });
    Geolocator.getPositionStream(
            locationSettings: const LocationSettings(
                accuracy: LocationAccuracy.high, distanceFilter: 10))
        .listen((position) {
      log('location changed');
      shareLocation(position);
      if (extraHandler != null) {
        extraHandler(position);
      }
    });
  }

  static shareLocation(Position position) async {
    log('sharing my location {${position.latitude}, ${position.longitude}}');

    await sendUpdateLocation(position.latitude, position.longitude);

    // Check if user entered any area
    await checkAreaEntry(position);
  }

  static bool isPointInPolygon(LatLng point, List<LatLng> polygon) {
    int intersectCount = 0;

    for (int j = 0; j < polygon.length - 1; j++) {
      LatLng a = polygon[j];
      LatLng b = polygon[j + 1];

      // Check if the point is between the y-coordinates of the edge
      if (((a.latitude > point.latitude) != (b.latitude > point.latitude))) {
        // Compute the x-coordinate where the line intersects the horizontal line at point.latitude
        double xIntersect = (b.longitude - a.longitude) *
                (point.latitude - a.latitude) /
                (b.latitude - a.latitude) +
            a.longitude;

        if (point.longitude < xIntersect) {
          intersectCount++;
        }
      }
    }

    return (intersectCount % 2 == 1); // True if odd number of intersections
  }
}
