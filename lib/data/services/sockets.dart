import 'dart:convert';

import 'package:dart_pusher_channels/dart_pusher_channels.dart';
import 'package:family_management/config/socket_config.dart';
import 'package:family_management/config/urls.dart';
import 'package:family_management/data/services/local_storage.dart';
import 'package:family_management/domain/models/chat.dart';
import 'package:family_management/utils/utils.dart';
import 'package:get/get.dart';

class Sockets {
  static Chat? activeChat;

  // Map to track online users: userId -> isOnline
  static final RxMap<String, bool> onlineUsers = <String, bool>{}.obs;
  static connect() async {
    socketClient.onConnectionEstablished.listen((data) {
      Utils.log('connected');
    });
    await socketClient.connect();
  }

  static disconnect() async {
    socketClient.disconnect();
    socketClient.dispose();
  }

  static familyNotificationsChannel() async {
    final user = LocalStorage.getUser();
    if (user == null) return;
    final token = LocalStorage.getToken();
    final notifications = socketClient.privateChannel(
        'private-family.${user.families.first.id}',
        authorizationDelegate:
            EndpointAuthorizableChannelTokenAuthorizationDelegate
                .forPrivateChannel(
                    authorizationEndpoint:
                        Uri.parse('$baseUrl/broadcasting/auth'),
                    headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json'
            }));

    notifications.bindToAll().listen(handleFamilyNotification);
  }

  static handleFamilyNotification(ChannelReadEvent data) async {
    Utils.log('family notification ${data.name}');
    Utils.log(data.data);
    if (data.data['type'] == null) return;
  }

  /// Subscribe to the presence channel to detect when users connect or disconnect
  static subscribeToPresenceChannel() async {
    final user = LocalStorage.getUser();
    if (user == null) return;

    final token = LocalStorage.getToken();
    if (token == null) return;

    final familyId = user.families.first.id;
    Utils.log('Subscribing to presence channel: presence-family.$familyId');

    // Create the presence channel
    final presenceChannel = socketClient.presenceChannel(
        'presence-family.$familyId',
        authorizationDelegate:
            EndpointAuthorizableChannelTokenAuthorizationDelegate
                .forPresenceChannel(
                    authorizationEndpoint:
                        Uri.parse('$baseUrl/broadcasting/auth'),
                    headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json'
            }));

    // Handle member added event (user connected) - Reverb event
    presenceChannel.bind('reverb:member_added').listen((event) {
      final data = jsonDecode(event.data);
      final userId = data['user_id'].toString();
      Utils.log('Member connected: $userId');
      Utils.log('Member info: ${data['user_info']}');

      // Update online status
      updateUserOnlineStatus(userId, true);
      Utils.log('Updated online users: ${onlineUsers.length} users online');
    });

    // Handle member removed event (user disconnected) - Reverb event
    presenceChannel.bind('reverb:member_removed').listen((event) {
      final data = jsonDecode(event.data);
      final userId = data['user_id'].toString();
      Utils.log('Member disconnected: $userId');

      // Update online status
      updateUserOnlineStatus(userId, false);
      Utils.log('Updated online users: ${onlineUsers.length} users online');
    });

    // Handle subscription succeeded event to get initial members - Reverb event
    presenceChannel.bind('reverb:subscription_succeeded').listen((event) {
      final data = jsonDecode(event.data);
      if (data['presence'] != null && data['presence']['members'] != null) {
        final members = data['presence']['members'] as Map<String, dynamic>;

        // Clear existing online users
        onlineUsers.clear();

        // Add all current members as online
        members.forEach((userId, userInfo) {
          updateUserOnlineStatus(userId, true);
        });

        Utils.log('Initial online users: ${onlineUsers.length} users online');
      }
    });

    // Subscribe to the channel
    presenceChannel.subscribe();
    Utils.log('Subscribed to presence channel');
  }

  /// Check if a user is online
  /// Returns true if the user is online, false otherwise
  static bool isUserOnline(int userId) {
    return onlineUsers[userId.toString()] == true;
  }

  /// Get a list of all online user IDs
  static List<String> getOnlineUserIds() {
    return onlineUsers.entries
        .where((entry) => entry.value == true)
        .map((entry) => entry.key)
        .toList();
  }

  /// Update a user's online status in the app
  /// This should be called whenever we receive presence channel updates
  static void updateUserOnlineStatus(String userId, bool isOnline) {
    // First update our local tracking map
    onlineUsers[userId] = isOnline;

    // Try to find the user in any controllers that might have this user
    try {
      // Get the user from local storage to update
      final currentUser = LocalStorage.getUser();
      if (currentUser != null && currentUser.id.toString() == userId) {
        currentUser.isOnline = isOnline;
        if (!isOnline) {
          currentUser.lastSeenAt = DateTime.now();
        }
        // Save the updated user
        LocalStorage.saveUser(currentUser);
      }
    } catch (e) {
      Utils.log('Error updating user online status: $e');
    }
  }
}
