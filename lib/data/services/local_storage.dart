import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:family_management/domain/models/user.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter_file_downloader/flutter_file_downloader.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:path_provider/path_provider.dart';

class LocalStorage {
  static GetStorage getStorage = GetStorage();
  static User? getUser() {
    final userData = getStorage.read('user');
    if (userData != null) {
      return User.fromJson(jsonDecode(userData));
    }
    return null;
  }

  static Future<void> saveUser(User user) async {
    await getStorage.write('user', jsonEncode(user.toMap()));
  }

  static Future<void> write(String key, String? value) async =>
      await getStorage.write(key, value);
  static String? read(String key) => getStorage.read(key);
  static String? getToken() => getStorage.read('token');

  static Future<void> saveToken(String token) async {
    await getStorage.write('token', token);
  }

  static Future<void> clearToken() async {
    await getStorage.remove('token');
  }

  static Future<void> clearUser() async {
    await getStorage.remove('user');
  }

  static storeFile(String uuid, String path) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File(path);
    final fileName = '${directory.path}/${path.split('/').last}';
    await file.copy(fileName);
    await getStorage.write(uuid, fileName);
  }

  static fileExists(String uuid) {
    final storage = getStorage.read(uuid);
    log('checking file $uuid $storage');

    if (storage == null) return false;
    final file = File(storage);
    return file.existsSync();
  }

  static getFilePath(String uuid) {
    return getStorage.read(uuid);
  }

  static downloadFile(String uuid, String url, String fileName,
      {Function(String? path, double progress)? onDownloadProgress}) async {
    try {
      FileDownloader.downloadFile(
          url: url,
          onDownloadCompleted: (path) => storeFile(uuid, path),
          onProgress: onDownloadProgress);
    } catch (e) {
      log(e.toString());
      Utils.showToast('Unknown error'.tr);
    }
  }
}
