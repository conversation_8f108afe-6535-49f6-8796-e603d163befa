import 'dart:convert';
import 'dart:math';

import 'package:family_management/config/constants.dart';
import 'package:family_management/config/socket_config.dart';
import 'package:family_management/data/services/local_storage.dart';
import 'package:family_management/domain/models/chat.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/utils/call_service.dart';
import 'package:family_management/utils/utils.dart';
import 'package:family_management/domain/models/message.dart' as msg;
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class NotificationsService {
  static sendEventNotification(
      {required String title, required String body}) async {
    try {
      final id = Random().nextInt(9999);
      notificationPlugin.show(
          id,
          title,
          body,
          const NotificationDetails(
            android: AndroidNotificationDetails(
                importance: Importance.max,
                priority: Priority.high,
                enableVibration: true,
                eventChannelId,
                eventChannelName),
          ));
    } catch (e, stackTrace) {
      Utils.log(e);
      Utils.log(stackTrace);
    }
  }

  static initNotifications() async {
    try {
      CallService.requestPermissions();
      notificationPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()!
          .requestNotificationsPermission();
    } catch (e) {
      Utils.log(e);
    }
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await notificationPlugin.initialize(initializationSettings);
  }

  static Future<void> onBackgroundMessage(RemoteMessage message) async {
    await Firebase.initializeApp();
    final data = message.data;
    if (data['type'] == 'call') {
      final caller = User.fromJson(jsonDecode(data['user']));
      CallService.showIncomingAudioCall(caller);
      return;
    }
    showNotification(
        message.notification?.title ?? '', message.notification?.body ?? '');
  }

  static Future<void> onForegroundMessage(RemoteMessage message) async {
    final data = message.data;
    Utils.log('notification recieved ${message.notification?.title}');
    if (data['type'] == 'call') {
      final caller = User.fromJson(jsonDecode(data['user']));
      CallService.showIncomingAudioCall(caller);
      return;
    }
    if (data['type'] == 'message') {
      final chatJson = LocalStorage.read('active_chat');
      if (chatJson != null) {
        final chat = Chat.fromJson(jsonDecode(chatJson));
        final msgData = jsonDecode(data['message']);
        Utils.log(msgData.toString());
        final message = msg.Message.fromJson(msgData);
        if (message.chatId == chat.id) {
          return;
        }
      }
    }
    showNotification(
        message.notification?.title ?? '', message.notification?.body ?? '');
  }

  static showNotification(String title, String body, {data}) {
    try {
      final id = Random().nextInt(9999);
      notificationPlugin.show(
          id,
          title,
          body,
          const NotificationDetails(
            android: AndroidNotificationDetails(
                showWhen: true,
                importance: Importance.max,
                priority: Priority.high,
                enableVibration: true,
                notificationsId,
                notificationsName),
          ));
    } catch (e, stackTrace) {
      Utils.log(e);
      Utils.log(stackTrace);
    }
  }

  static sendNewMessageNotification(data, Chat? activeChat, User? user) {
    final message = msg.Message.fromJson(data);
    final chat = message.chat;
    if (message.chatId != activeChat?.id && message.sender?.id != user?.id) {
      String? chatName = chat?.name;
      // if(!(chat?.isGroup??false)){
      //   chatName = chat.members?.first.name;
      // }
      try {
        final id = Random().nextInt(9999);
        notificationPlugin.show(
            id,
            (chat?.isGroup ?? false) ? "$chatName" : "${message.sender?.name}",
            message.message,
            const NotificationDetails(
              android: AndroidNotificationDetails(
                  importance: Importance.max,
                  priority: Priority.high,
                  enableVibration: true,
                  messageChannelId,
                  messageChannelName),
            ));
      } catch (e, stackTrace) {
        Utils.log(e);
        Utils.log(stackTrace);
      }
    }
  }
}
