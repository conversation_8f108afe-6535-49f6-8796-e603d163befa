import 'dart:convert';

import 'package:family_management/data/datasources/local_datasources/base_localdatasource.dart';
import 'package:family_management/domain/models/user.dart';

class AuthLocaldatasource extends BaseLocaldatasource {
  AuthLocaldatasource({required super.storage});

  saveToken(String token) async {
    await write('token', token);
  }

  saveUser(User user) async {
    await write('user', jsonEncode(user.toMap()));
  }

  getToken() {
    return read('token');
  }

  getUser() {
    final data = read('user');
    if (data == null) return data;
    return User.fromJson(jsonDecode(data));
  }
}
