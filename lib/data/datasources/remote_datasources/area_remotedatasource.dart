import 'package:dio/dio.dart';
import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';

class AreaRemotedatasource extends BaseRemotedatasource {
  AreaRemotedatasource({required super.dio});

  Future<Response> getAreas() async => await get(endpoint: 'areas');

  Future<Response> create({required String name, required List points}) async =>
      await post(endpoint: 'areas', data: {'name': name, 'points': points});

  Future<Response> deleteArea(int id) async =>
      await delete(endpoint: 'areas/$id');

  Future<Response> enterArea({required int areaId}) async =>
      await post(endpoint: 'enter-area', data: {'area_id': areaId});
}
