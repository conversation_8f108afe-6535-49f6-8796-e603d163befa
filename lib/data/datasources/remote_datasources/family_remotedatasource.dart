import 'package:dio/dio.dart';
import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';

class FamilyRemotedatasource extends BaseRemotedatasource {
  FamilyRemotedatasource({required super.dio});

  Future<Response> register(String name) async {
    return await post(endpoint: '/register-family', data: {'name': name});
  }

  Future<Response> sendInvites(
      {required List<String> emails, required int familyId}) async {
    return await post(
        endpoint: 'invite/multiple-emails',
        data: {'emails': emails, 'family_id': familyId});
  }
}
