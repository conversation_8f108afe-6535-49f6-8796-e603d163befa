import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';
import 'package:family_management/domain/enums/message_type.dart';
import 'package:family_management/domain/models/shared_contact.dart';

class ChatsRemotedatasource extends BaseRemotedatasource {
  ChatsRemotedatasource({required super.dio});

  Future<Response> getChats({required bool archived, String? search}) async {
    String url = 'chats?is_archived=${archived ? 1 : 0}';

    if (search != null && search.isNotEmpty) {
      url += '&search=$search';
    }

    return await get(endpoint: url);
  }

  Future<Response> getMessages(int? chatId) async {
    return await get(endpoint: 'messages?chat_id=$chatId');
  }

  Future<Response> createChat(
      {required List members, required int familyId}) async {
    return await post(
        endpoint: 'chats', data: {'members': members, 'family_id': familyId});
  }

  Future<Response> sendMessage(
      {required int chatId,
      required String message,
      required String type,
      Function(int, int)? onSendProgress,
      File? image,
      String? voice,
      String? uuid,
      SharedContact? contact,
      File? file}) async {
    Map<String, dynamic> data = {
      'chat_id': chatId,
      'content': message,
      'type': type,
      'uuid': uuid
    };
    if (type == MessageType.image.toString() &&
        image != null &&
        image.path.isNotEmpty) {
      data['image'] = await MultipartFile.fromFile(image.path);
    }
    if (type == MessageType.file.toString() &&
        file != null &&
        file.path.isNotEmpty) {
      data['file'] = await MultipartFile.fromFile(file.path);
    }
    if (type == MessageType.audio.toString() &&
        voice != null &&
        voice.isNotEmpty) {
      data['voice'] = await MultipartFile.fromFile(voice);
    }
    if (type == MessageType.contact.toString() && contact != null) {
      data.addAll({'contact[name]': contact.name});
      for (final item in contact.phones) {
        data.addAll({'contact.phones[]': item});
      }
    }
    log(data.toString());
    return await post(
        endpoint: 'messages/send',
        data: FormData.fromMap(data),
        onSendProgress: onSendProgress);
  }

  Future<Response> deleteChat(int chatId) async {
    return delete(endpoint: 'chats/$chatId');
  }

  Future<Response> archiveChat(int chatId) async =>
      post(endpoint: 'archive-chat/$chatId', data: {});
  Future<Response> readAll(int chatId) async =>
      await get(endpoint: 'read-all/$chatId');

  Future<Response> callLog() async => await get(endpoint: 'call-logs');
}
