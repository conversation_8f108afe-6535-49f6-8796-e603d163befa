import 'package:dio/dio.dart';
import 'dart:io';
import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';

class MediaRemotedatasource extends BaseRemotedatasource {
  MediaRemotedatasource({required super.dio});

  Future<Response> media({int? albumId, String? search, int page = 1}) async {
    String url = 'media?page=$page';
    if (albumId != null) {
      url += '&album_id=$albumId';
    }
    if (search != null && search.isNotEmpty) {
      url += '&search=$search';
    }
    return await get(endpoint: url);
  }

  Future<Response> create(
      {required int albumId,
      required File image,
      Function(int, int)? onSendProgress}) async {
    final data = {
      'album_id': albumId,
      'image': await MultipartFile.fromFile(image.path)
    };
    return await post(
        endpoint: 'media',
        data: FormData.fromMap(data),
        onSendProgress: onSendProgress);
  }
}
