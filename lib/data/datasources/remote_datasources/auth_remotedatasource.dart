import 'dart:io';

import 'package:dio/dio.dart';
import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';

class AuthRemotedatasource extends BaseRemotedatasource {
  AuthRemotedatasource({required super.dio});

  Future<Response> login(
      {required String email,
      required String password,
      String? fcmToken}) async {
    return await post(
        endpoint: 'login',
        data: {'email': email, 'password': password, 'fcm_token': fcmToken});
  }

  Future<Response> register(
      {required String name,
      required String email,
      required String password,
      String? fcmToken,
      String? bio,
      String? inviteCode,
      File? image}) async {
    Map<String, dynamic> data = {
      'email': email,
      'name': name,
      'password': password,
      'bio': bio,
      'invite_code': inviteCode
    };
    if (bio != null) {
      data.addAll({'bio': bio});
    }
    if (image != null) {
      data.addAll({'image': await MultipartFile.fromFile(image.path)});
    }
    if (fcmToken != null) {
      data.addAll({'fcm_token': fcmToken});
    }
    FormData formData = FormData.fromMap(data);
    return await post(endpoint: 'register', data: formData);
  }

  Future<Response> forgetPassword(String email) async {
    return await post(endpoint: 'forgot-password', data: {'email': email});
  }

  Future<Response> checkCode(
      {required String email, required String code}) async {
    return await post(
        endpoint: 'check-code', data: {'email': email, 'code': code});
  }

  Future<Response> resetPassword(
      {required String email,
      required String code,
      required String password}) async {
    return await post(
        endpoint: 'reset-password',
        data: {'email': email, 'code': code, 'password': password});
  }

  Future<Response> uploadImage(File image,
      {Function(int, int)? onSendProgress}) async {
    FormData formData =
        FormData.fromMap({'image': await MultipartFile.fromFile(image.path)});
    return await post(
        endpoint: 'profile/update-image',
        data: formData,
        onSendProgress: onSendProgress);
  }

  Future<Response> updateProfile(
      {String? name, String? bio, String? password}) async {
    final data = {};
    if (name != null && name.isNotEmpty) {
      data['name'] = name;
    }
    if (bio != null && bio.isNotEmpty) {
      data['bio'] = bio;
    }
    if (password != null && password.isNotEmpty) {
      data['password'] = password;
    }
    return await put(endpoint: 'profile', data: data);
  }

  Future<Response> profile() async {
    return await get(endpoint: 'profile');
  }
}
