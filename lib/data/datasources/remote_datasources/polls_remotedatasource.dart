import 'package:dio/dio.dart';
import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';

class PollsRemotedatasource extends BaseRemotedatasource {
  PollsRemotedatasource({required super.dio});

  Future<Response> createOrUpdate(
      {int? id,
      required int familyId,
      required String name,
      required List<String> items,
      List<int>? assignedUserIds}) async {
    String url = 'polls';
    if (id != null) {
      url = 'polls/$id';
    }

    Map<String, dynamic> data = {
      'family_id': familyId,
      'name': name,
      'items': items,
    };

    if (assignedUserIds != null && assignedUserIds.isNotEmpty) {
      data['assigned_user_ids'] = assignedUserIds;
    }

    return await post(endpoint: url, data: data);
  }

  Future<Response> update(int id,
      {required int familyId,
      required String name,
      required List<String> items,
      List<int>? assignedUserIds}) async {
    Map<String, dynamic> data = {
      'family_id': familyId,
      'name': name,
      'items': items,
    };

    if (assignedUserIds != null && assignedUserIds.isNotEmpty) {
      data['assigned_user_ids'] = assignedUserIds;
    }

    return await put(endpoint: 'polls/$id', data: data);
  }

  Future<Response> getPolls() async {
    return await get(endpoint: 'polls');
  }

  Future<Response> deletePoll(int id) async {
    return delete(endpoint: 'polls/$id');
  }

  Future<Response> vote({required int pollId, required int itemId}) async {
    return await post(
        endpoint: 'polls/vote', data: {'poll_id': pollId, 'item_id': itemId});
  }
}
