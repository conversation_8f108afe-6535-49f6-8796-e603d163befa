import 'package:dio/dio.dart';
import 'package:family_management/utils/exceptions.dart';
import 'package:family_management/utils/utils.dart';

class BaseRemotedatasource {
  final Dio dio;
  BaseRemotedatasource({required this.dio});

  Future<Response> post(
      {required String endpoint,
      required data,
      Function(int, int)? onSendProgress,
      Function(int, int)? onReceiveProgress}) async {
    try {
      final response = await dio.post(endpoint,
          data: data,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress);

      return response;
    } on DioException catch (e) {
      if (e.response == null) {
        Utils.log(e);
        throw NetworkException();
      } else {
        throw ResponseException(
            code: e.response?.statusCode, message: e.response?.data['message']);
      }
    } catch (e) {
      throw NetworkException();
    }
  }

  Future<Response> put(
      {required String endpoint,
      required data,
      Function(int, int)? onSendProgress,
      Function(int, int)? onReceiveProgress}) async {
    try {
      final response = await dio.put(endpoint,
          data: data,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress);

      return response;
    } on DioException catch (e) {
      if (e.response == null) {
        throw NetworkException();
      } else {
        throw ResponseException(
            code: e.response?.statusCode, message: e.response?.data['message']);
      }
    } catch (e) {
      throw NetworkException();
    }
  }

  Future<Response> get(
      {required String endpoint, Map<String, dynamic>? params}) async {
    try {
      final response = await dio.get(endpoint, queryParameters: params);

      return response;
    } on DioException catch (e) {
      if (e.response == null) {
        throw NetworkException();
      } else {
        throw ResponseException(
            code: e.response?.statusCode, message: e.response?.data['message']);
      }
    } catch (e) {
      throw NetworkException();
    }
  }

  Future<Response> delete({required String endpoint, Object? data}) async {
    try {
      final response = await dio.delete(endpoint, data: data);
      return response;
    } on DioException catch (e) {
      if (e.response == null) {
        throw NetworkException();
      } else {
        throw ResponseException(
            code: e.response?.statusCode, message: e.response?.data['message']);
      }
    } catch (e) {
      throw NetworkException();
    }
  }

  Future<Response> getMembers({String? search}) async {
    String url = 'members';
    if (search != null && search.isNotEmpty) {
      url += '?search=$search';
    }
    return get(endpoint: url);
  }
}
