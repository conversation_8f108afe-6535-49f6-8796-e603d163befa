import 'package:dio/dio.dart';
import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';

class ExpenseRemoteDataSource extends BaseRemotedatasource {
  ExpenseRemoteDataSource({required super.dio});

  Future<Response> homePage({required int familyId, int page = 1}) async {
    String url = 'budget?page=$page';
    return await get(endpoint: url);
  }

  Future<Response> expenseCategories(
      {required int familyId, int page = 1}) async {
    String url = 'expense-categories?family_id=$familyId&page=$page';
    return await get(endpoint: url);
  }

  Future<Response> expenses({required int familyId, int page = 1}) async {
    String url = 'expenses?family_id=$familyId&page=$page';
    return await get(endpoint: url);
  }

  Future<Response> deleteExpense(int expenseId) async {
    return await delete(endpoint: 'expenses/$expenseId');
  }

  Future<Response> storeOrUpdateExpense({
    int? id,
    required int familyId,
    required int expenseCategoryId,
    required String description,
    required String date,
    required int amount,
    String? type,
  }) async {
    Map<String, dynamic> data = {
      'family_id': familyId,
      'expense_category_id': expenseCategoryId,
      'description': description,
      'date': date,
      'amount': amount,
      if (type != null) 'type': type,
    };
    String url = 'expenses';
    if (id != null) {
      url = 'expenses/$id';
      return await put(endpoint: url, data: data);
    }
    return await post(endpoint: url, data: data);
  }
}
