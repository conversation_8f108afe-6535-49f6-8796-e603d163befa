import 'package:dio/dio.dart';
import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';

class AlbumRemotedatasource extends BaseRemotedatasource {
  AlbumRemotedatasource({required super.dio});

  Future<Response> getAlbums({int? type, String? search}) async {
    Map<String, dynamic>? params;

    if (type != null || search != null) {
      params = {};
      if (type != null) params['type'] = type;
      if (search != null && search.isNotEmpty) params['search'] = search;
    }

    return await get(endpoint: 'albums', params: params);
  }

  Future<Response> create(
      {required int familyId,
      required String name,
      required int type,
      List<int>? assignedUserIds}) async {
    Map<String, dynamic> data = {
      'name': name,
      'family_id': familyId,
      'type': type,
    };

    if (assignedUserIds != null && assignedUserIds.isNotEmpty) {
      data['assigned_user_ids'] = assignedUserIds;
    }

    return await post(endpoint: 'albums', data: data);
  }

  Future<Response> getAlbum(int id) async {
    return await get(endpoint: 'albums/$id');
  }

  Future<Response> delteMedia(List<int> ids) async =>
      await delete(endpoint: 'media', data: {'media': ids});
}
