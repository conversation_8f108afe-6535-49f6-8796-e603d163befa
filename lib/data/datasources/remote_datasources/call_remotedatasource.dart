import 'dart:math';

import 'package:dio/dio.dart';
import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';

class CallRemotedatasource extends BaseRemotedatasource {
  CallRemotedatasource({required super.dio});

  Future<Response> generateToken({required String roomName}) async {
    return await post(
        endpoint: 'calls/generate-token',
        data: {'room': roomName, 'name': Random().nextInt(9999999)});
  }

  Future<Response> call(int chatId) async {
    return post(endpoint: 'call/$chatId', data: {});
  }

  Future<Response> acceptCall(int chatId) async {
    return post(endpoint: 'call/accept/$chatId', data: {});
  }

  Future<Response> endCall(int chatId) async {
    return post(endpoint: 'call/end/$chatId', data: {});
  }
}
