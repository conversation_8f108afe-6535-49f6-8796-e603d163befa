// ignore_for_file: library_prefixes

import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:dart_pusher_channels/dart_pusher_channels.dart';
import 'package:family_management/config/app_routes.dart';
import 'package:family_management/config/socket_config.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/config/urls.dart';
import 'package:family_management/data/services/local_storage.dart';
import 'package:family_management/data/services/notifications_service.dart';
import 'package:family_management/data/services/sockets.dart';
import 'package:family_management/domain/controllers/call_controller.dart';
import 'package:family_management/domain/mixins/call_mixin.dart';
import 'package:family_management/domain/mixins/location_mixin.dart';
import 'package:family_management/domain/models/chat.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/utils/call_service.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter_callkit_incoming/entities/entities.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class AppService extends GetxService with CallMixin, LocationMixin {
  final RxString _theme = 'light'.obs;
  final Rx<Locale> _locale = const Locale('en', 'US').obs;
  final Rx<Chat> _activeChat = Chat().obs;
  final RxString _token = ''.obs;
  late StreamSubscription<ChannelReadEvent> privateMessages;
  late StreamSubscription<ChannelReadEvent> privateCalls;

  final RxString _statusMessage = ''.obs;

  final Rx<TextDirection> _direction = TextDirection.ltr.obs;

  String get statusMessage => _statusMessage.value;

  String get theme => _theme.value;
  Locale get locale => _locale.value;
  Chat get activeChat => _activeChat.value;
  TextDirection get direction => _direction.value;

  set activeChat(Chat value) => _activeChat.value = value;
  @override
  void onInit() {
    super.onInit();
    connect();
  }

  getTheme() {
    return theme == 'dark' ? darkTheme : lightTheme;
  }

  connect() async {
    NotificationsService.initNotifications();
    await Sockets.connect();
  }

  bindPrivateNotifications() async {
    Utils.log('subscribing to private channel');
    final authUser = LocalStorage.getUser();
    if (authUser == null) return;

    user = authUser;
    Utils.log(
        'authenticated user ${user.name} from family ${user.families.first.name}');
    final userToken = LocalStorage.getToken();
    if (userToken == null || userToken.isEmpty) {
      return;
    }
    _token.value = userToken;
    await Sockets.familyNotificationsChannel();

    // Subscribe to presence channel to detect user connections/disconnections
    await Sockets.subscribeToPresenceChannel();

    Utils.log('token ${_token.value}');
    _connectCallChannel();
  }

  _connectCallChannel() async {
    final calls = socketClient.privateChannel('private-calls.${user.id}',
        authorizationDelegate:
            EndpointAuthorizableChannelTokenAuthorizationDelegate
                .forPrivateChannel(
                    authorizationEndpoint:
                        Uri.parse('$baseUrl/broadcasting/auth'),
                    headers: {
              'Authorization': 'Bearer ${_token.value}',
              'Content-Type': 'application/json'
            }));
    privateCalls = calls.bindToAll().listen((data) {
      log('new call received');
      log(data.data.toString());
      final map = jsonDecode(data.data);
      if (map['type'] == 'endCall') {
        endCall();
        try {
          final callController = Get.find<CallController>();
          callController.stop();
        } catch (e) {
          Utils.log(e);
        }
        CallService.endAllCalls();
        return;
      }
      if (map['type'] == 'accept') {
        callOpen = true;
        return;
      }
      if (map['chat'] != null) {
        if (map['caller'] != null) {
          final caller = User.fromJson(map['caller']);
          if (caller.id == user.id) {
            outgoingCall = true;
            return;
          }
          outgoingCall = false;
          CallService.showIncomingAudioCall(caller);
        }
        final chat = Chat.fromJson(map['chat']);
        _callEvents(chat);
      }
    });
    calls.subscribe();
    log('subscribed to calls channel');
  }

  _callEvents(Chat chat) async {
    FlutterCallkitIncoming.onEvent.listen((event) async {
      switch (event?.event) {
        case Event.actionCallAccept:
          _activeChat.value = chat;
          Sockets.activeChat = chat;
          users.value = chat.members;
          users.refresh();
          callRepository.acceptCall(chat.id!);
          Get.toNamed(AppRoutes.callScreen,
              arguments: {'isIncomingCall': true, 'user': chat.members.first});
          callOpen = true;
          break;
        case Event.actionCallDecline:
          // case Event.actionCallEnded:
          callOpen = false;
          callRepository.endCall(chat.id!);
          break;
        default:
          break;
      }
    });
  }

  logout() {
    Get.offAllNamed(AppRoutes.authCheck);
  }
}
