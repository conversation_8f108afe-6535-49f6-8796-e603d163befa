import 'package:cached_network_image/cached_network_image.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/choose_members_controller.dart';
import 'package:family_management/presentation/chat/widgets/search_box.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChooseMembersView extends GetView<ChooseMembersController> {
  const ChooseMembersView({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              withBackButton: true,
              title: 'Our family'.tr,
              withDrawer: false),
          floatingActionButton: Container(
            height: 60,
            width: 60,
            decoration: BoxDecoration(
                shape: BoxShape.circle, color: Theme.of(context).primaryColor),
            child: Center(
              child: ElevatedButton(
                onPressed: () => controller.createChat(),
                style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    alignment: Alignment.center,
                    padding: EdgeInsets.zero),
                child: controller.creating
                    ? const Center(
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 3,
                        ),
                      )
                    : const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 25,
                      ),
              ),
            ),
          ),
          body: GetBuilder<ChooseMembersController>(
            builder: (controller) => controller.loading
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 10),
                    child: Column(
                      children: [
                        const SearchBox(),
                        const SizedBox(
                          height: 20,
                        ),
                        if (controller.selectedMembers.isNotEmpty)
                          SizedBox(
                            width: MediaQuery.of(context).size.width,
                            height: 50,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: ListView.builder(
                                  shrinkWrap: true,
                                  scrollDirection: Axis.horizontal,
                                  itemCount: controller.selectedMembers.length,
                                  itemBuilder: (context, index) {
                                    final item =
                                        controller.selectedMembers[index];
                                    return Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 3),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 5),
                                      decoration: BoxDecoration(
                                          color: grey,
                                          borderRadius:
                                              BorderRadius.circular(25)),
                                      child: Row(
                                        children: [
                                          TextWidget(text: "${item.name}"),
                                          const SizedBox(
                                            width: 5,
                                          ),
                                          GestureDetector(
                                            onTap: () =>
                                                controller.selectMember(item),
                                            child: Container(
                                              height: 18,
                                              width: 18,
                                              decoration: const BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  color: Colors.grey),
                                              child: const Icon(
                                                Icons.close,
                                                color: Colors.white,
                                                size: 10,
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    );
                                  }),
                            ),
                          ),
                        ListView.builder(
                            scrollDirection: Axis.vertical,
                            shrinkWrap: true,
                            itemCount: controller.members.length,
                            itemBuilder: (context, index) {
                              final item = controller.members[index];
                              return Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 10),
                                child: Row(
                                  children: [
                                    Container(
                                      height: 52,
                                      width: 52,
                                      clipBehavior: Clip.antiAliasWithSaveLayer,
                                      decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                      ),
                                      child: item.avatar != null
                                          ? CachedNetworkImage(
                                              width: 25,
                                              height: 25,
                                              imageUrl: item.avatar!,
                                              fit: BoxFit.cover,
                                            )
                                          : Image.asset(
                                              'assets/images/avatar.png',
                                              fit: BoxFit.cover,
                                            ),
                                    ),
                                    const SizedBox(
                                      width: 5,
                                    ),
                                    Column(
                                      children: [
                                        TextWidget(
                                          text: '${item.name}',
                                        ),
                                      ],
                                    ),
                                    const Spacer(),
                                    Checkbox(
                                        value: controller.isSelected(item.id),
                                        onChanged: (value) =>
                                            controller.selectMember(item),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(100),
                                        ))
                                  ],
                                ),
                              );
                            })
                      ],
                    ),
                  ),
          ),
        ));
  }
}
