import 'package:family_management/domain/controllers/chat_controller.dart';
import 'package:family_management/presentation/chat/call_log_view.dart';
import 'package:family_management/presentation/chat/chats_view.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class HomeChatView extends GetView<ChatController> {
  const HomeChatView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          floatingActionButton: FloatingActionButton(
            heroTag: "chat_fab",
            onPressed: () => controller.startNewChat(),
            backgroundColor: Theme.of(context).primaryColor,
            child: const Icon(
              Icons.chat,
              color: Colors.white,
            ),
          ),
          appBar: AppBar(
            title: TextWidget(
              text: 'Conversations'.tr,
              fontWeight: FontWeight.bold,
            ),
            centerTitle: true,
            actions: [
              PopupMenuButton(
                itemBuilder: (context) => [
                  PopupMenuItem(
                      onTap: () => controller.startNewChat(),
                      child: Row(
                        children: [
                          SvgPicture.asset('assets/svgs/chat.svg'),
                          const SizedBox(width: 8),
                          TextWidget(text: 'Start new chat'.tr)
                        ],
                      )),
                  PopupMenuItem(
                      onTap: () => controller.pickMembers(),
                      child: Row(
                        children: [
                          SvgPicture.asset('assets/svgs/create_group.svg'),
                          const SizedBox(width: 8),
                          TextWidget(text: 'Create group'.tr)
                        ],
                      )),
                ],
                icon: SvgPicture.asset(
                  'assets/svgs/add_chat.svg',
                  width: 25,
                ),
              ),
              const SizedBox(
                width: 5,
              ),
            ],
            bottom: TabBar(controller: controller.tabController, tabs: [
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset('assets/svgs/chat.svg',
                        colorFilter: ColorFilter.mode(
                            controller.activeIndex == 0
                                ? Theme.of(context).primaryColor
                                : Colors.grey,
                            BlendMode.srcIn)),
                    const SizedBox(
                      width: 5,
                    ),
                    TextWidget(
                      text: 'Chats'.tr,
                      fontSize: MediaQuery.of(context).size.width / 30,
                      color: controller.activeIndex == 0
                          ? Theme.of(context).primaryColor
                          : Colors.grey,
                    )
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset('assets/svgs/phone.svg',
                        colorFilter: ColorFilter.mode(
                            controller.activeIndex == 1
                                ? Theme.of(context).primaryColor
                                : Colors.grey,
                            BlendMode.srcIn)),
                    const SizedBox(
                      width: 5,
                    ),
                    TextWidget(
                      text: 'Calls'.tr,
                      fontSize: MediaQuery.of(context).size.width / 30,
                      color: controller.activeIndex == 1
                          ? Theme.of(context).primaryColor
                          : Colors.grey,
                    )
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset('assets/svgs/archive.svg',
                        colorFilter: ColorFilter.mode(
                            controller.activeIndex == 2
                                ? Theme.of(context).primaryColor
                                : Colors.grey,
                            BlendMode.srcIn)),
                    const SizedBox(
                      width: 5,
                    ),
                    TextWidget(
                      text: 'Archived'.tr,
                      fontSize: MediaQuery.of(context).size.width / 30,
                      color: controller.activeIndex == 2
                          ? Theme.of(context).primaryColor
                          : Colors.grey,
                    )
                  ],
                ),
              ),
            ]),
          ),
          body: TabBarView(
              physics: const NeverScrollableScrollPhysics(),
              controller: controller.tabController,
              children: const [
                ChatsView(
                  archived: false,
                ),
                CallLogView(),
                ChatsView(
                  archived: true,
                ),
              ]),
        )));
  }
}
