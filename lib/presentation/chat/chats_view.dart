import 'dart:async';

import 'package:family_management/domain/controllers/chat_controller.dart';
import 'package:family_management/presentation/chat/widgets/search_box.dart';
import 'package:family_management/presentation/widget/avatar_image.dart';
import 'package:family_management/presentation/widget/button.dart';
import 'package:family_management/presentation/widget/loading_indicator.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:family_management/domain/enums/message_type.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatsView extends GetView<ChatController> {
  final bool archived;

  const ChatsView({super.key, required this.archived});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          body: RefreshIndicator(
            onRefresh: () => controller.getChats(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              alignment: Alignment.center,
              child: controller.loading
                  ? const LoadingIndicator()
                  : Column(
                      children: [
                        const SizedBox(
                          height: 20,
                        ),
                        SearchBox(
                          initialValue: controller.searchQuery,
                          onChanged: (value) {
                            controller.searchQuery = value;
                            if (controller.timer?.isActive ?? false) {
                              controller.timer?.cancel();
                            }

                            // If search query is empty, immediately refresh the list
                            if (value.isEmpty &&
                                controller.searchQuery.isNotEmpty) {
                              controller.searchQuery = '';
                              if (archived) {
                                controller.getArchived();
                              } else {
                                controller.getChats();
                              }
                              return;
                            }

                            // Debounce search for non-empty queries
                            controller.timer =
                                Timer(const Duration(milliseconds: 900), () {
                              if (archived) {
                                controller.getArchived();
                              } else {
                                controller.getChats();
                              }
                              // Also refresh the UI to update the filtered lists
                              controller.chats.refresh();
                              controller.archivedChats.refresh();
                            });
                          },
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          child: Obx(() => Row(
                                children: [
                                  Button(
                                    text: 'All'.tr,
                                    onPressed: () =>
                                        controller.setChatFilter('all'),
                                    radius: BorderRadius.circular(8),
                                    fontSize: 12,
                                    height: 30,
                                    backgroundColor:
                                        controller.chatFilter == 'all'
                                            ? Theme.of(context).primaryColor
                                            : Colors.grey[350],
                                    textColor: controller.chatFilter == 'all'
                                        ? Colors.white
                                        : Colors.black,
                                  ),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  Button(
                                    text: 'Private'.tr,
                                    onPressed: () =>
                                        controller.setChatFilter('private'),
                                    radius: BorderRadius.circular(8),
                                    fontSize: 12,
                                    height: 30,
                                    backgroundColor:
                                        controller.chatFilter == 'private'
                                            ? Theme.of(context).primaryColor
                                            : Colors.grey[350],
                                    textColor:
                                        controller.chatFilter == 'private'
                                            ? Colors.white
                                            : Colors.black,
                                  ),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  Button(
                                    text: 'Groups'.tr,
                                    onPressed: () =>
                                        controller.setChatFilter('group'),
                                    radius: BorderRadius.circular(8),
                                    fontSize: 12,
                                    height: 30,
                                    backgroundColor:
                                        controller.chatFilter == 'group'
                                            ? Theme.of(context).primaryColor
                                            : Colors.grey[350],
                                    textColor: controller.chatFilter == 'group'
                                        ? Colors.white
                                        : Colors.black,
                                  ),
                                ],
                              )),
                        ),
                        Expanded(child: getChatList(context))
                      ],
                    ),
            ),
          ),
        ),
      ),
    );
  }

  getChatList(BuildContext context) {
    // Get the filtered chats based on the current filter
    final filteredChats = archived
        ? controller.getFilteredArchivedChats()
        : controller.getFilteredChats();

    // Show empty state if no chats match the filter
    if (filteredChats.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              controller.chatFilter == 'group'
                  ? CupertinoIcons.person_3_fill
                  : CupertinoIcons.chat_bubble_2,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            TextWidget(
              text: controller.chatFilter == 'all'
                  ? 'You have no conversations yet'.tr
                  : controller.chatFilter == 'private'
                      ? 'You have no private chats yet'.tr
                      : 'You have no group chats yet'.tr,
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      scrollDirection: Axis.vertical,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        final item = filteredChats[index];
        final chatImage = item.getChatImage();
        return Dismissible(
          onDismissed: (direction) => controller.deleteChat(item.id!),
          confirmDismiss: (direction) => showCupertinoDialog(
              context: context,
              builder: (context) => AlertDialog(
                    content: TextWidget(text: 'Arey you sure'.tr),
                    actions: [
                      TextButton(
                          onPressed: () => Get.back(result: false),
                          child: Text('No'.tr)),
                      TextButton(
                          onPressed: () {
                            if (direction == DismissDirection.endToStart) {
                              controller.archiveChat(item.id!);
                            } else {
                              controller.deleteChat(item.id!);
                            }
                            Get.back();
                          },
                          child: Text('Yes'.tr)),
                    ],
                  )),
          key: Key(index.toString()),
          secondaryBackground: Container(
            decoration: BoxDecoration(
                color: Colors.red, borderRadius: BorderRadius.circular(10)),
            alignment: Alignment.centerRight,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(
                  CupertinoIcons.trash,
                  color: Colors.white,
                ),
                TextWidget(
                  text: 'Delete'.tr,
                  color: Colors.white,
                  fontSize: 12,
                )
              ],
            ),
          ),
          background: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(10),
            ),
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(
                  CupertinoIcons.archivebox,
                  color: Colors.white,
                ),
                TextWidget(
                  text: 'Archive'.tr,
                  color: Colors.white,
                )
              ],
            ),
          ),
          child: InkWell(
            onTap: () => controller.goToChat(item),
            borderRadius: BorderRadius.circular(16),
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 4),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(8),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: Colors.grey.withAlpha(30),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  // Avatar without online status
                  chatImage != null
                      ? AvatarImage(
                          url: chatImage,
                          width: 56,
                          height: 56,
                        )
                      : Container(
                          height: 56,
                          width: 56,
                          decoration: BoxDecoration(
                            image: const DecorationImage(
                              image:
                                  AssetImage('assets/images/user_avatar.png'),
                              fit: BoxFit.cover,
                            ),
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),

                  const SizedBox(width: 16),

                  // Chat content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Chat name and date row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: TextWidget(
                                text: item.getChatName(),
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey[800],
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),

                            // Last message date
                            if (item.lastMessage?.createdAt != null)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.grey.withAlpha(30),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: TextWidget(
                                  text: controller.formatMessageDate(
                                      item.lastMessage!.createdAt),
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey[600],
                                ),
                              ),
                          ],
                        ),

                        const SizedBox(height: 6),

                        // Last message with type indicator
                        _buildLastMessagePreview(item),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
      itemCount: filteredChats.length,
    );
  }

  Widget _buildLastMessagePreview(dynamic item) {
    if (item.lastMessage == null) {
      return TextWidget(
        text: "No messages yet".tr,
        fontSize: 13,
        color: Colors.grey[600],
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );
    }

    final message = item.lastMessage;
    String displayText;
    IconData? icon;

    switch (message.type) {
      case MessageType.image:
        displayText = "Photo".tr;
        icon = CupertinoIcons.photo;
        break;
      case MessageType.audio:
        displayText = "Voice message".tr;
        icon = CupertinoIcons.mic;
        break;
      case MessageType.file:
        displayText = "File".tr;
        icon = CupertinoIcons.doc;
        break;
      case MessageType.contact:
        displayText = "Contact".tr;
        icon = CupertinoIcons.person;
        break;
      case MessageType.video:
        displayText = "Video".tr;
        icon = CupertinoIcons.videocam;
        break;
      case MessageType.text:
      default:
        displayText = message.message ?? "No messages yet".tr;
        icon = null;
        break;
    }

    return Row(
      children: [
        if (icon != null) ...[
          Icon(
            icon,
            size: 14,
            color: Colors.grey[500],
          ),
          const SizedBox(width: 4),
        ],
        Expanded(
          child: TextWidget(
            text: displayText,
            fontSize: 13,
            color: Colors.grey[600],
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
