import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/conversation_controller.dart';
import 'package:family_management/presentation/chat/widgets/media_bottom_sheet.dart';
import 'package:family_management/presentation/chat/widgets/message_widget.dart';
import 'package:family_management/presentation/widget/avatar_image.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class PrivateConversationView extends GetView<ConversationController> {
  const PrivateConversationView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: AppBar(
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(0),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 5),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    IconButton(
                        onPressed: () => Get.back(),
                        icon: const Icon(CupertinoIcons.chevron_back)),
                    const SizedBox(width: 10),
                    controller.user?.avatar != null
                        ? AvatarImage(
                            url: controller.user!.avatar!,
                            width: 32,
                            height: 32,
                          )
                        : Container(
                            height: 32,
                            width: 32,
                            decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                image: DecorationImage(
                                    image: AssetImage(
                                        'assets/images/avatar.png'))),
                          ),
                    const SizedBox(
                      width: 5,
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width / 3,
                      child: TextWidget(
                        text: "${controller.user?.name}",
                        color: Colors.black,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                        onTap: () => controller.call(),
                        child: SvgPicture.asset('assets/svgs/phone.svg',
                            colorFilter: const ColorFilter.mode(
                              Colors.grey,
                              BlendMode.srcIn,
                            ))),
                    const SizedBox(
                      width: 15,
                    ),
                    InkWell(
                      child: SvgPicture.asset('assets/svgs/video_camera.svg'),
                    ),
                    const SizedBox(width: 10)
                    // PopupMenuButton(
                    //     padding: const EdgeInsets.symmetric(
                    //         horizontal: 20, vertical: 10),
                    //     shape: RoundedRectangleBorder(
                    //         borderRadius: BorderRadius.circular(10)),
                    //     menuPadding: const EdgeInsets.symmetric(
                    //         horizontal: 20, vertical: 10),
                    //     popUpAnimationStyle:
                    //         AnimationStyle(curve: Curves.easeIn),
                    //     itemBuilder: (context) => [
                    //           PopupMenuItem(
                    //             child: TextWidget(
                    //               text: 'Clear chat'.tr,
                    //               fontSize: 18,
                    //             ),
                    //             onTap: () => {},
                    //           )
                    //         ]),
                  ],
                ),
              ),
            ),
          ),
          body: controller.loading
              ? const Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                )
              : Container(
                  decoration: const BoxDecoration(
                      image: DecorationImage(
                          image: AssetImage(
                              'assets/images/chat_background_light.jpeg'),
                          fit: BoxFit.cover)),
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  height: MediaQuery.of(context).size.height,
                  child: Column(
                    children: [
                      Expanded(
                          child: SingleChildScrollView(
                        controller: controller.scrollController,
                        child: Column(
                          children: [
                            ListView.builder(
                                physics: const NeverScrollableScrollPhysics(),
                                scrollDirection: Axis.vertical,
                                reverse: true,
                                shrinkWrap: true,
                                itemCount: controller.messages.length,
                                itemBuilder: (context, index) {
                                  final item = controller.messages[index];
                                  return Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment: item.userId ==
                                            controller.appService.user.id
                                        ? CrossAxisAlignment.end
                                        : CrossAxisAlignment.start,
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          if (item.userId ==
                                              controller.appService.user.id) {
                                            controller.messages[index]
                                                .showReadBy = !item.showReadBy;
                                            controller.messages.refresh();
                                          }
                                        },
                                        child: MessageWidget(
                                          message: item,
                                          progress: controller.progress,
                                          fileStatus: controller.fileStatus,
                                        ),
                                      ),
                                      if (item.readBy.firstWhereOrNull((e) =>
                                                  e.id !=
                                                  controller
                                                      .appService.user.id) !=
                                              null &&
                                          (item.showReadBy ||
                                              (item.userId ==
                                                      controller
                                                          .appService.user.id &&
                                                  index == 0)))
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 20),
                                          child: TextWidget(
                                            text: 'read'.tr,
                                            fontSize: 12,
                                            color: Colors.grey,
                                          ),
                                        ),
                                    ],
                                  );
                                })
                          ],
                        ),
                      )),
                      Container(
                        width: MediaQuery.of(context).size.width,
                        margin: const EdgeInsets.symmetric(vertical: 20),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(45),
                            color: Theme.of(context).scaffoldBackgroundColor,
                            boxShadow: const [
                              BoxShadow(
                                  color: Color(0xff808080),
                                  blurRadius: 10,
                                  blurStyle: BlurStyle.outer,
                                  offset: Offset(0, 5))
                            ]),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            IconButton(
                                onPressed: () => mediaBottomSheet(context,
                                    pickFromCamera: () =>
                                        controller.pickImageFromCamera(),
                                    pickFromGallery: () =>
                                        controller.pickImageFromGallery(),
                                    pickFile: () => controller.sendFile(),
                                    pickContact: () =>
                                        controller.sendContact()),
                                icon: const Icon(
                                  CupertinoIcons.paperclip,
                                  color: yellow,
                                )),
                            SizedBox(
                              width: MediaQuery.of(context).size.width - 190,
                              child: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 200),
                                child: controller.recording
                                    ? _recorderWaveForm(context)
                                    : TextField(
                                        controller:
                                            controller.messageController,
                                        maxLines: 100,
                                        minLines: 1,
                                        textInputAction:
                                            TextInputAction.newline,
                                        decoration: InputDecoration(
                                            enabledBorder: InputBorder.none,
                                            focusedBorder: InputBorder.none,
                                            hintText: 'Type a message'.tr,
                                            hintStyle:
                                                const TextStyle(color: yellow)),
                                      ),
                              ),
                            ),
                            AnimatedSwitcher(
                              duration: const Duration(milliseconds: 200),
                              child: controller.recording
                                  ? const SizedBox(width: 50)
                                  : IconButton(
                                      onPressed: () =>
                                          controller.startOrStopRecording(),
                                      icon: const Icon(CupertinoIcons.mic)),
                            ),
                            RotationTransition(
                              turns: const AlwaysStoppedAnimation(-30 / 360),
                              child: IconButton(
                                  onPressed: () => controller.sendMessage(),
                                  icon: const Icon(Icons.send_outlined)),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
        )));
  }

  _recorderWaveForm(BuildContext context) {
    return AudioWaveforms(
      enableGesture: true,
      size: Size(MediaQuery.of(context).size.width - 190, 50),
      recorderController: controller.recorderController,
      waveStyle: WaveStyle(
        waveColor: Theme.of(context).primaryColor,
        extendWaveform: true,
        showMiddleLine: false,
      ),
    );
  }
}
