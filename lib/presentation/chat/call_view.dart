import 'dart:ui';

import 'package:family_management/domain/controllers/call_controller.dart';
import 'package:family_management/presentation/chat/widgets/group_call.dart';
import 'package:family_management/presentation/chat/widgets/private_call.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:livekit_client/livekit_client.dart';

class CallView extends GetView<CallController> {
  const CallView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
          textDirection: controller.appService.locale == const Locale('ar')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Scaffold(
            body: Container(
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                      image: AssetImage('assets/images/call_bg.png'),
                      fit: BoxFit.cover),
                ),
                child: <PERSON><PERSON>(children: [
                  BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                    child: Container(
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.5)),
                    ),
                  ),
                  SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                            child: controller.appService.users
                                        .firstWhereOrNull((e) =>
                                            e.id ==
                                            controller.appService.user.id)
                                        ?.videoRenderer !=
                                    null
                                ? Column(
                                    children: [
                                      SizedBox(
                                        height:
                                            MediaQuery.of(context).size.height -
                                                150,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        child: VideoTrackRenderer(controller
                                            .appService.users
                                            .firstWhere((e) =>
                                                e.id ==
                                                controller.appService.user.id)
                                            .videoRenderer!),
                                      )
                                    ],
                                  )
                                : controller.appService.users
                                            .where((e) =>
                                                e.id !=
                                                controller.appService.user.id)
                                            .length ==
                                        1
                                    ? const PrivateCall()
                                    : const GroupCall()),
                        controller.appService.callOpen
                            ? Container(
                                height: 150,
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(17),
                                      topRight: Radius.circular(17)),
                                  color: Colors.grey.shade500
                                      .withValues(alpha: 0.2),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceAround,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    _actionButton(
                                        context: context,
                                        onTapped: () => controller.toggleMic(),
                                        child: Icon(!controller.micEnabled
                                            ? Icons.mic_off
                                            : Icons.mic_outlined)),
                                    _actionButton(
                                        context: context,
                                        onTapped: () =>
                                            controller.toggleCamera(),
                                        child: const Icon(
                                          CupertinoIcons.video_camera,
                                          size: 30,
                                        )),
                                    _actionButton(
                                        context: context,
                                        onTapped: () =>
                                            controller.toggleSpeaker(),
                                        child: Icon(
                                          controller.speakerEnabled
                                              ? FontAwesomeIcons.volumeHigh
                                              : FontAwesomeIcons.volumeXmark,
                                          size: 30,
                                        )),
                                    GestureDetector(
                                      onTap: () => controller.stop(),
                                      child: Container(
                                        height: 50,
                                        width: 50,
                                        decoration: const BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: Colors.red),
                                        child: const Icon(
                                          Icons.close,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : Padding(
                                padding: const EdgeInsets.only(bottom: 50),
                                child: GestureDetector(
                                  onTap: () => controller.stop(),
                                  child: Container(
                                    height: 75,
                                    width: 75,
                                    decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: Colors.red),
                                    child: const Icon(
                                      Icons.close,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                      ],
                    ),
                  ),
                ])),
          )),
    );
  }

  _actionButton({
    required BuildContext context,
    required Widget child,
    required Function() onTapped,
  }) {
    return GestureDetector(
        onTap: onTapped,
        child: Container(
          height: 50,
          width: 50,
          decoration:
              BoxDecoration(shape: BoxShape.circle, color: Colors.grey[400]),
          child: child,
        ));
  }
}
