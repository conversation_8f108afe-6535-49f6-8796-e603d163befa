// ignore_for_file: must_be_immutable

import 'package:family_management/presentation/widget/text_input.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SearchBox extends StatefulWidget {
  final void Function(String)? onChanged;
  final String? initialValue;

  const SearchBox({super.key, this.onChanged, this.initialValue});

  @override
  State<SearchBox> createState() => _SearchBoxState();
}

class _SearchBoxState extends State<SearchBox> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextInput(
        fillColor: Colors.grey[300],
        radius: BorderRadius.circular(25),
        borderColor: Colors.grey[350],
        height: 36,
        controller: _controller,
        prefixIcon: const Icon(
          Icons.search,
          color: Colors.grey,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 3),
        hint: 'Search'.tr,
        onChange: widget.onChanged);
  }
}
