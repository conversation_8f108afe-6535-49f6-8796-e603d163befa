// ignore_for_file: must_be_immutable

import 'dart:developer';

import 'package:family_management/app_service.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/data/services/local_storage.dart';
import 'package:family_management/domain/controllers/conversation_controller.dart';
import 'package:family_management/domain/enums/file_message_status.dart';
import 'package:family_management/domain/models/message.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:open_file/open_file.dart';

class FileMessageWidget extends StatelessWidget {
  final appService = Get.find<AppService>();
  final conversationController = Get.find<ConversationController>();
  final Message message;
  double progress = 0;
  final Function()? onPressed;
  FileMessageWidget(
      {super.key, required this.message, this.progress = 0, this.onPressed});

  @override
  Widget build(BuildContext context) {
    final isCurrentUser = message.userId == appService.user.id;

    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width - 150,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _getButton(context),
          const SizedBox(width: 8),
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                TextWidget(
                  text: message.file?.fileName ?? '',
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  color: isCurrentUser ? Colors.white : Colors.black87,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 2),
                  child: TextWidget(
                    text: message.fileStatus == FileMessageStatus.downloading ||
                            message.fileStatus == FileMessageStatus.uploading
                        ? '${(message.progress * 100).toInt()}%'
                        : 'File',
                    fontSize: 11,
                    color: isCurrentUser
                        ? Colors.white.withValues(alpha: 180)
                        : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _getButton(BuildContext context) {
    final isCurrentUser = message.userId == appService.user.id;
    final isProcessing = message.fileStatus == FileMessageStatus.uploading ||
        message.fileStatus == FileMessageStatus.downloading;

    return GestureDetector(
      onTap: () => _clickHandler(),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: isCurrentUser
              ? Colors.white.withValues(alpha: 200)
              : primarySwatch.withValues(alpha: 50),
          shape: BoxShape.circle,
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            _getIcon(),
            if (isProcessing)
              CircularProgressIndicator(
                value: message.progress,
                strokeWidth: 2,
                backgroundColor: Colors.transparent,
                valueColor: AlwaysStoppedAnimation<Color>(
                  isCurrentUser ? primarySwatch : primarySwatch,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _getIcon() {
    final isCurrentUser = message.userId == appService.user.id;
    final iconColor = isCurrentUser ? primarySwatch : primarySwatch;

    switch (message.fileStatus) {
      case FileMessageStatus.downloading:
      case FileMessageStatus.uploading:
        return Icon(
          CupertinoIcons.xmark,
          color: iconColor,
          size: 18,
        );

      case FileMessageStatus.downloaded:
      case FileMessageStatus.uploaded:
        return Icon(
          FontAwesomeIcons.file,
          color: iconColor,
          size: 18,
        );

      case FileMessageStatus.failed:
        return Icon(
          Icons.refresh,
          color: iconColor,
          size: 18,
        );

      case FileMessageStatus.notExist:
        return Icon(
          Icons.download,
          color: iconColor,
          size: 18,
        );

      default:
        return Icon(
          FontAwesomeIcons.file,
          color: iconColor,
          size: 18,
        );
    }
  }

  _clickHandler() async {
    final exists = LocalStorage.fileExists(message.uuid!);
    if (exists) {
      final filePath = LocalStorage.getFilePath(message.uuid!);
      log('open file $filePath');
      if (filePath == null) return;
      await OpenFile.open(filePath);
      return;
    }
    log('downloading file ${message.file?.originalUrl}');
    message.fileStatus = FileMessageStatus.downloading;
    conversationController.refreshStates();
    LocalStorage.downloadFile(
      message.uuid!,
      message.file!.originalUrl!,
      message.file!.fileName!,
      onDownloadProgress: (received, progress) {
        message.progress = progress / 100;
        log('downloaded ${message.progress}');
        conversationController.refreshStates();
        if (message.progress == 1) {
          message.fileStatus = FileMessageStatus.downloaded;
          conversationController.refreshStates();
        }
      },
    );
  }
}
