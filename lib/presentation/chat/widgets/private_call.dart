import 'package:family_management/domain/controllers/call_controller.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart' as rtc;

class PrivateCall extends GetView<CallController> {
  const PrivateCall({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Column(
          children: [
            const SizedBox(
              height: 100,
            ),
            Container(
              height: 126,
              width: 126,
              decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  image: DecorationImage(
                      image: controller.appService.users
                                  .firstWhere((e) =>
                                      e.id != controller.appService.user.id)
                                  .avatar !=
                              null
                          ? NetworkImage(controller.appService.users
                              .firstWhere(
                                  (e) => e.id != controller.appService.user.id)
                              .avatar!)
                          : const AssetImage('assets/images/avatar.png'),
                      fit: BoxFit.cover)),
            ),
            const SizedBox(
              height: 20,
            ),
            TextWidget(
              text:
                  '${controller.appService.users.firstWhere((e) => e.id != controller.appService.user.id).name}',
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            if (!controller.appService.callOpen)
              controller.isIncomingCall
                  ? TextWidget(
                      text: 'Connecting'.tr,
                      fontSize: 15,
                      color: Colors.white,
                    )
                  : TextWidget(
                      text: 'Calling'.tr,
                      fontSize: 15,
                      color: Colors.white,
                    )
          ],
        ),
        if (controller.cameraEnabled)
          Positioned(
              bottom: 10,
              right: 0,
              child: Container(
                height: 150,
                width: 100,
                clipBehavior: Clip.antiAliasWithSaveLayer,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                ),
                child: VideoTrackRenderer(
                  controller.localVideoSource(),
                  fit: rtc.RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,
                ),
              ))
      ],
    );
  }
}
