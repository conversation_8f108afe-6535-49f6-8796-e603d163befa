import 'dart:developer';
import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:family_management/app_service.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/enums/file_message_status.dart';
import 'package:family_management/domain/enums/message_type.dart';
import 'package:family_management/domain/models/message.dart';
import 'package:family_management/presentation/chat/widgets/contact_message.dart';
import 'package:family_management/presentation/chat/widgets/file_message_widget.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:voice_message_player/voice_message_player.dart';

class MessageWidget extends StatelessWidget {
  final appService = Get.find<AppService>();
  final Message message;
  final double? progress;
  final double? width;
  final FileMessageStatus? fileStatus;

  MessageWidget(
      {super.key,
      required this.message,
      this.fileStatus,
      this.width,
      this.progress});

  @override
  Widget build(BuildContext context) {
    final isCurrentUser = message.userId == appService.user.id;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Column(
        crossAxisAlignment:
            isCurrentUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // Show sender name for group chats if needed
          if (message.sender != null &&
              !isCurrentUser &&
              message.chat?.isGroup == true)
            Padding(
              padding: const EdgeInsets.only(left: 12, bottom: 2),
              child: TextWidget(
                text: message.sender!.name ?? '',
                fontSize: 12,
                color: primarySwatch,
                fontWeight: FontWeight.bold,
              ),
            ),

          // Message bubble
          Row(
            mainAxisAlignment:
                isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.start,
            children: [
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: width ?? (MediaQuery.of(context).size.width * 0.75),
                ),
                child: Container(
                  margin: const EdgeInsets.symmetric(vertical: 2),
                  padding: _getMessagePadding(),
                  decoration: BoxDecoration(
                    color: isCurrentUser ? primarySwatch : Colors.grey[200],
                    borderRadius: BorderRadius.only(
                      topLeft: const Radius.circular(16),
                      topRight: const Radius.circular(16),
                      bottomLeft: isCurrentUser
                          ? const Radius.circular(16)
                          : const Radius.circular(4),
                      bottomRight: isCurrentUser
                          ? const Radius.circular(4)
                          : const Radius.circular(16),
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _getMessage(context),

                      // Timestamp and status
                      if (message.createdAt != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text(
                                _formatTime(message.createdAt!),
                                style: TextStyle(
                                  fontSize: 10,
                                  color: isCurrentUser
                                      ? Colors.white.withValues(alpha: 180)
                                      : Colors.grey[600],
                                ),
                              ),
                              if (isCurrentUser) ...[
                                const SizedBox(width: 4),
                                Icon(
                                  message.readBy.isNotEmpty
                                      ? Icons.done_all
                                      : Icons.done,
                                  size: 12,
                                  color: message.readBy.isNotEmpty
                                      ? Colors.blue[100]
                                      : Colors.white.withValues(alpha: 180),
                                ),
                              ],
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              // Sending indicator
              if (message.id == null)
                Padding(
                  padding: const EdgeInsets.only(left: 4),
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      shape: BoxShape.circle,
                    ),
                    child: const Center(
                      child: Icon(
                        CupertinoIcons.time,
                        size: 10,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  EdgeInsets _getMessagePadding() {
    // Adjust padding based on message type
    if (message.type == MessageType.image) {
      return const EdgeInsets.all(4);
    } else if (message.type == MessageType.audio) {
      return const EdgeInsets.symmetric(horizontal: 8, vertical: 4);
    } else {
      return const EdgeInsets.symmetric(horizontal: 12, vertical: 8);
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (messageDate == today) {
      // Today, show only time
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (messageDate == today.subtract(const Duration(days: 1))) {
      // Yesterday
      return 'Yesterday';
    } else {
      // Other days, show date
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  Widget _getMessage(BuildContext context) {
    log('loading message widget ${message.type}');
    final isCurrentUser = message.userId == appService.user.id;

    switch (message.type) {
      case MessageType.image:
        if (message.image != null) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width - 120,
                maxHeight: MediaQuery.of(context).size.height / 3,
              ),
              child: message.id != null
                  ? CachedNetworkImage(
                      imageUrl: message.image!.originalUrl!,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        height: 150,
                        width: 200,
                        color: Colors.grey[300],
                        child: const Center(
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        height: 150,
                        width: 200,
                        color: Colors.grey[300],
                        child: const Icon(Icons.error),
                      ),
                    )
                  : Image.file(
                      File(message.image!.originalUrl!),
                      fit: BoxFit.cover,
                    ),
            ),
          );
        }
        break;

      case MessageType.text:
        if (message.message != null) {
          return TextWidget(
            text: message.message!,
            color: isCurrentUser ? Colors.white : Colors.black87,
            fontSize: 15,
          );
        }
        break;

      case MessageType.audio:
        if (message.voice != null) {
          return ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width - 120,
            ),
            child: VoiceMessagePlayer(
              counterTextStyle: TextStyle(
                color: isCurrentUser ? Colors.white : primarySwatch,
              ),
              backgroundColor: Colors.transparent,
              activeSliderColor: isCurrentUser ? Colors.white70 : primarySwatch,
              circlesColor: isCurrentUser ? Colors.white70 : primarySwatch,
              controller: VoiceController(
                audioSrc: message.voice!,
                maxDuration: const Duration(hours: 1),
                isFile: message.id == null,
                onError: (error) => log('Error playing audio: $error'),
                onComplete: () {},
                onPause: () {},
                onPlaying: () {},
              ),
            ),
          );
        }
        break;

      case MessageType.file:
        if (message.file != null) {
          return FileMessageWidget(
            progress: progress ?? 0.0,
            message: message,
          );
        }
        break;

      case MessageType.contact:
        if (message.contact != null) {
          return ContactMessage(
            contact: message.contact!,
            message: message,
          );
        }
        break;

      default:
        break;
    }

    return const SizedBox();
  }
}
