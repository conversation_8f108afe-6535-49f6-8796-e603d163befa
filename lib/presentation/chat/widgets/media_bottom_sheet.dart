import 'package:family_management/config/themes.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

mediaBottomSheet(BuildContext context,
    {required Function() pickFromCamera,
    required Function() pickFromGallery,
    required Function() pickFile,
    required Function() pickContact}) {
  return showModalBottomSheet(
      elevation: 10,
      context: context,
      builder: (context) => Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 50),
            decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(50),
                    topRight: Radius.circular(50))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                getButton(SvgPicture.asset('assets/svgs/camera.svg'),
                    'Camera'.tr, pickFromCamera),
                const SizedBox(
                  height: 10,
                ),
                const Divider(
                  color: grey,
                ),
                const SizedBox(
                  height: 10,
                ),
                getButton(SvgPicture.asset('assets/svgs/gallery.svg'),
                    'Photo & video library'.tr, () => pickFromGallery()),
                const SizedBox(
                  height: 10,
                ),
                const Divider(
                  color: grey,
                ),
                const SizedBox(
                  height: 10,
                ),
                getButton(SvgPicture.asset('assets/svgs/paper.svg'),
                    'Document'.tr, () => pickFile()),
                const SizedBox(
                  height: 10,
                ),
                const Divider(
                  color: grey,
                ),
                const SizedBox(
                  height: 10,
                ),
                getButton(SvgPicture.asset('assets/svgs/contact.svg'),
                    'Contact'.tr, () => pickContact())
              ],
            ),
          ));
}

getButton(Widget icon, String text, Function() callback) {
  return InkWell(
    onTap: callback,
    child: Row(
      children: [
        icon,
        const SizedBox(
          width: 10,
        ),
        TextWidget(
          text: text,
          fontSize: 18,
        )
      ],
    ),
  );
}
