import 'package:family_management/domain/controllers/call_controller.dart';
import 'package:family_management/presentation/widget/avatar_image.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart' as webrtc;

class GroupCall extends GetView<CallController> {
  const GroupCall({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CallController>(builder: (controller) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            GridView.builder(
                shrinkWrap: true,
                scrollDirection: Axis.vertical,
                itemCount: controller.appService.joinedUsers.length,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.8,
                    crossAxisSpacing: 10,
                    mainAxisSpacing: 10),
                itemBuilder: (context, index) {
                  final item = controller.appService.joinedUsers[index];
                  return Stack(
                    children: [
                      item.id == controller.appService.user.id &&
                              controller.cameraEnabled &&
                              controller.localVideoSource() != null
                          ? Container(
                              clipBehavior: Clip.antiAliasWithSaveLayer,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10)),
                              child: VideoTrackRenderer(
                                controller.localVideoSource(),
                                fit: webrtc.RTCVideoViewObjectFit
                                    .RTCVideoViewObjectFitCover,
                              ),
                            )
                          : item.cameraEnabled
                              ? Container(
                                  clipBehavior: Clip.antiAliasWithSaveLayer,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10)),
                                  child: VideoTrackRenderer(
                                    item.videoRenderer!,
                                    fit: webrtc.RTCVideoViewObjectFit
                                        .RTCVideoViewObjectFitCover,
                                  ),
                                )
                              : Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: Theme.of(context).primaryColor,
                                      border: Border.all(
                                          width: 2,
                                          color: item.talking
                                              ? Colors.white
                                              : Theme.of(context)
                                                  .primaryColor)),
                                  child: Center(
                                    child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          AvatarImage(url: item.avatar),
                                          const SizedBox(height: 10),
                                          TextWidget(
                                            text: item.name ?? '',
                                            color: Colors.white,
                                          )
                                        ]),
                                  ),
                                ),
                      if (item.mute)
                        const Positioned(
                            bottom: 5,
                            left: 5,
                            child: Icon(
                              Icons.mic_off,
                              color: Colors.white,
                            ))
                    ],
                  );
                })
          ],
        ),
      );
    });
  }
}
