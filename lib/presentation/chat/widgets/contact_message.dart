import 'package:family_management/app_service.dart';
import 'package:family_management/config/app_routes.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/models/message.dart';
import 'package:family_management/domain/models/shared_contact.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ContactMessage extends StatelessWidget {
  final SharedContact contact;
  final Message message;
  final appService = Get.find<AppService>();
  ContactMessage({super.key, required this.contact, required this.message});

  @override
  Widget build(BuildContext context) {
    final isCurrentUser = appService.user.id == message.userId;

    return GestureDetector(
      onTap: () => Get.toNamed(AppRoutes.viewContact, arguments: contact),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isCurrentUser
                  ? Colors.white.withValues(alpha: 200)
                  : primarySwatch.withValues(alpha: 50),
            ),
            child: Center(
              child: Icon(
                CupertinoIcons.person,
                size: 18,
                color: isCurrentUser ? primarySwatch : primarySwatch,
              ),
            ),
          ),
          const SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              TextWidget(
                text: contact.name ?? '',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isCurrentUser ? Colors.white : Colors.black87,
              ),
              if (contact.phones.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 2),
                  child: TextWidget(
                    text: contact.phones.first,
                    fontSize: 12,
                    color: isCurrentUser
                        ? Colors.white.withValues(alpha: 180)
                        : Colors.grey[600],
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
