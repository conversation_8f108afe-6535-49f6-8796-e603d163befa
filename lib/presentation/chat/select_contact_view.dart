import 'package:family_management/domain/controllers/conversation_controller.dart';
import 'package:family_management/domain/enums/message_type.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/loading_indicator.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SelectContactView extends GetView<ConversationController> {
  const SelectContactView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.direction,
        child: Scaffold(
            appBar: mainAppBar(
                context: context,
                withBackButton: true,
                withDrawer: false,
                title: 'Share contact'.tr),
            body: controller.loadingContacts
                ? const Center(child: LoadingIndicator())
                : ListView.separated(
                    shrinkWrap: true,
                    scrollDirection: Axis.vertical,
                    separatorBuilder: (context, index) => const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Divider(
                        color: Colors.grey,
                      ),
                    ),
                    itemCount: controller.contacts.length,
                    itemBuilder: (context, index) => ListTile(
                      onTap: () {
                        controller.contact = controller.contacts[index];

                        controller.type = MessageType.contact;
                        controller.sendMessage();
                        Get.back();
                        Get.back();
                      },
                      leading: Container(
                        height: 50,
                        width: 50,
                        decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            image: DecorationImage(
                                image:
                                    AssetImage('assets/images/user_avatar.png'),
                                fit: BoxFit.cover)),
                      ),
                      title: TextWidget(
                          text: controller.contacts[index].displayName),
                    ),
                  )),
      ),
    );
  }
}
