import 'package:family_management/domain/controllers/conversation_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class GroupVideoCallView extends GetView<ConversationController> {
  const GroupVideoCallView({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: controller.appService.locale == const Locale('ar')
          ? TextDirection.rtl
          : TextDirection.ltr,
      child: Scaffold(
        appBar: mainAppBar(
            context: context,
            title: 'Video call'.tr,
            withBackButton: true,
            withDrawer: false),
        bottomNavigationBar: BottomAppBar(
          padding: const EdgeInsets.all(0),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.5),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(15),
                topRight: Radius.circular(15),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                InkWell(
                  child: Container(
                    height: 50,
                    width: 50,
                    decoration: const BoxDecoration(
                        shape: BoxShape.circle, color: Colors.white),
                    child: const Icon(
                      Icons.mic,
                    ),
                  ),
                ),
                InkWell(
                  child: Container(
                    height: 50,
                    width: 50,
                    decoration: const BoxDecoration(
                        shape: BoxShape.circle, color: Colors.white),
                    child: const Icon(
                      Icons.camera,
                    ),
                  ),
                ),
                InkWell(
                  child: Container(
                    height: 50,
                    width: 50,
                    decoration: const BoxDecoration(
                        shape: BoxShape.circle, color: Colors.white),
                    child: const Icon(
                      Icons.flip_camera_ios_outlined,
                    ),
                  ),
                ),
                InkWell(
                  child: Container(
                    height: 50,
                    width: 50,
                    decoration: const BoxDecoration(
                        shape: BoxShape.circle, color: Colors.white),
                    child: const Icon(
                      CupertinoIcons.video_camera,
                    ),
                  ),
                ),
                InkWell(
                  child: Container(
                    height: 50,
                    width: 50,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(context).primaryColor),
                    child: const Icon(
                      Icons.people_alt_outlined,
                      color: Colors.white,
                    ),
                  ),
                ),
                InkWell(
                  child: Container(
                    height: 50,
                    width: 50,
                    decoration: const BoxDecoration(
                        shape: BoxShape.circle, color: Colors.red),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 10),
              GridView.builder(
                scrollDirection: Axis.vertical,
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 1,
                  crossAxisSpacing: 2,
                  mainAxisSpacing: 1,
                ),
                itemCount: 6,
                itemBuilder: (context, index) {
                  return Container(
                    height: 216,
                    width: 200,
                    decoration: const BoxDecoration(
                      image: DecorationImage(
                          image:
                              AssetImage('assets/images/video_call_person.png'),
                          fit: BoxFit.cover),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
