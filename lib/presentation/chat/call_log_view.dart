import 'package:family_management/domain/controllers/chat_controller.dart';
import 'package:family_management/presentation/widget/avatar_image.dart';
import 'package:family_management/presentation/widget/loading_indicator.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class CallLogView extends GetView<ChatController> {
  const CallLogView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Directionality(
        textDirection: controller.appService.direction,
        child: Scaffold(
          body: RefreshIndicator(
              onRefresh: () => controller.getCallLogs(),
              child: controller.loadingCallLog
                  ? const Center(
                      child: LoadingIndicator(),
                    )
                  : controller.callLog.isEmpty
                      ? Center(
                          child: TextWidget(
                            text: 'No calls yet'.tr,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 20),
                          child: ListView.builder(
                              shrinkWrap: true,
                              scrollDirection: Axis.vertical,
                              itemCount: controller.callLog.length,
                              itemBuilder: (context, index) {
                                final item = controller.callLog[index];
                                return Container(
                                  margin:
                                      const EdgeInsets.symmetric(vertical: 10),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          AvatarImage(
                                              width: 40,
                                              height: 40,
                                              url: item.getImage()),
                                          const SizedBox(width: 10),
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              SizedBox(
                                                  width: MediaQuery.of(context)
                                                          .size
                                                          .width -
                                                      100,
                                                  child: TextWidget(
                                                    text: item.getTitle(),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 1,
                                                  )),
                                              Row(children: [
                                                item.user?.id ==
                                                        controller
                                                            .appService.user.id
                                                    ? const Icon(
                                                        Icons.call_made,
                                                        color: Colors.green,
                                                        size: 15,
                                                      )
                                                    : (item.isMissed ?? false)
                                                        ? const Icon(
                                                            Icons.call_missed,
                                                            color: Colors.red,
                                                            size: 15,
                                                          )
                                                        : const Icon(
                                                            Icons.call_received,
                                                            color: Colors.red,
                                                            size: 15,
                                                          ),
                                                TextWidget(
                                                  text: intl.DateFormat(
                                                          'MMMM, dd, H:mm a')
                                                      .format(item.createdAt!),
                                                  color: Colors.grey,
                                                  fontSize: 13,
                                                )
                                              ]),
                                            ],
                                          )
                                        ],
                                      ),
                                    ],
                                  ),
                                );
                              }),
                        )),
        )));
  }
}
