import 'package:family_management/config/app_routes.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/conversation_controller.dart';
import 'package:family_management/domain/models/message.dart';
import 'package:family_management/presentation/chat/widgets/media_bottom_sheet.dart';
import 'package:family_management/presentation/chat/widgets/message_widget.dart';
import 'package:family_management/presentation/widget/avatar_image.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:flutter/cupertino.dart';

class GroupChatView extends GetView<ConversationController> {
  const GroupChatView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: AppBar(
            leading: IconButton(
                onPressed: () => Get.back(),
                icon: const Icon(CupertinoIcons.chevron_back)),
            title: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    height: 32,
                    width: 32,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        image: DecorationImage(
                            image: controller.user?.avatar != null
                                ? NetworkImage("${controller.user?.avatar}")
                                : const AssetImage(
                                    'assets/images/avatar.png'))),
                  ),
                  const SizedBox(
                    width: 5,
                  ),
                  SizedBox(
                    width: 100,
                    child: TextWidget(
                      text: "${controller.chat.name}",
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      color: Colors.black,
                    ),
                  )
                ],
              ),
            ),
            actions: [
              GestureDetector(
                  onTap: () => controller.call(),
                  child: SvgPicture.asset('assets/svgs/phone.svg',
                      colorFilter: const ColorFilter.mode(
                        Colors.grey,
                        BlendMode.srcIn,
                      ))),
              const SizedBox(
                width: 15,
              ),
              GestureDetector(
                  onTap: () => Get.toNamed(AppRoutes.groupVideoCall),
                  child: SvgPicture.asset('assets/svgs/video_camera.svg',
                      colorFilter: const ColorFilter.mode(
                        pink,
                        BlendMode.srcIn,
                      ))),
              PopupMenuButton(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10)),
                  menuPadding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  popUpAnimationStyle: AnimationStyle(curve: Curves.easeIn),
                  itemBuilder: (context) => [
                        PopupMenuItem(
                          child: TextWidget(
                            text: 'Clear chat'.tr,
                            fontSize: 18,
                          ),
                          onTap: () => {},
                        )
                      ]),
            ],
          ),
          body: controller.loading
              ? const Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                )
              : Container(
                  decoration: const BoxDecoration(
                      image: DecorationImage(
                          image: AssetImage(
                              'assets/images/chat_background_light.jpeg'),
                          fit: BoxFit.cover)),
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  height: MediaQuery.of(context).size.height,
                  child: Column(
                    children: [
                      Expanded(
                          child: SingleChildScrollView(
                        controller: controller.scrollController,
                        child: Column(
                          children: [
                            ListView.builder(
                                physics: const NeverScrollableScrollPhysics(),
                                scrollDirection: Axis.vertical,
                                reverse: true,
                                shrinkWrap: true,
                                itemCount: controller.messages.length,
                                itemBuilder: (context, index) {
                                  final item = controller.messages[index];
                                  return Column(
                                      crossAxisAlignment: item.userId ==
                                              controller.appService.user.id
                                          ? CrossAxisAlignment.end
                                          : CrossAxisAlignment.start,
                                      children: [
                                        _getMessageWidget(
                                            context,
                                            item,
                                            index > 0
                                                ? controller.messages[index - 1]
                                                : null),
                                        if (item.showReadBy ||
                                            (item.userId ==
                                                    controller
                                                        .appService.user.id &&
                                                index == 0))
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 20),
                                            child: SizedBox(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width /
                                                  2,
                                              height: 20,
                                              child: Stack(
                                                children: [
                                                  ...item.readBy
                                                      .map((e) => Positioned(
                                                          right: index * 2,
                                                          child: AvatarImage(
                                                            url: e.avatar,
                                                            width: 15,
                                                            height: 15,
                                                          )))
                                                ],
                                              ),
                                            ),
                                          ),
                                      ]);
                                })
                          ],
                        ),
                      )),
                      Container(
                        width: MediaQuery.of(context).size.width,
                        margin: const EdgeInsets.symmetric(vertical: 20),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(45),
                            color: Theme.of(context).scaffoldBackgroundColor,
                            boxShadow: const [
                              BoxShadow(
                                  color: Color(0xff808080),
                                  blurRadius: 10,
                                  blurStyle: BlurStyle.outer,
                                  offset: Offset(0, 5))
                            ]),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            IconButton(
                                onPressed: () => mediaBottomSheet(context,
                                    pickFromCamera: () =>
                                        controller.pickImageFromCamera(),
                                    pickFromGallery: () =>
                                        controller.pickImageFromGallery(),
                                    pickFile: () => controller.sendFile(),
                                    pickContact: () =>
                                        controller.sendContact()),
                                icon: const Icon(
                                  CupertinoIcons.paperclip,
                                  color: yellow,
                                )),
                            SizedBox(
                              width: MediaQuery.of(context).size.width - 190,
                              child: TextField(
                                controller: controller.messageController,
                                maxLines: 100,
                                minLines: 1,
                                textInputAction: TextInputAction.newline,
                                decoration: InputDecoration(
                                    enabledBorder: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    hintText: 'Type a message'.tr,
                                    hintStyle: const TextStyle(color: yellow)),
                              ),
                            ),
                            IconButton(
                                onPressed: () => {},
                                icon: const Icon(CupertinoIcons.mic)),
                            RotationTransition(
                              turns: const AlwaysStoppedAnimation(-30 / 360),
                              child: IconButton(
                                  onPressed: () => controller.sendMessage(),
                                  icon: const Icon(Icons.send_outlined)),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
        )));
  }

  _getMessageWidget(BuildContext context, Message message, Message? prev) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Directionality(
        textDirection: message.sender?.id == controller.authUser?.id
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            if (prev?.userId != message.userId &&
                message.userId != controller.authUser?.id)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                height: 35,
                width: 35,
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    image: DecorationImage(
                        image: message.sender?.avatar != null
                            ? NetworkImage(message.sender!.avatar!)
                            : const AssetImage('assets/images/avatar.png'),
                        fit: BoxFit.cover)),
              ),
            if (prev?.userId == message.userId &&
                message.userId != controller.authUser?.id)
              const SizedBox(
                width: 40,
              ),
            MessageWidget(
              message: message,
              width: MediaQuery.of(context).size.width / 1.3,
            ),
          ],
        ),
      ),
    );
  }
}
