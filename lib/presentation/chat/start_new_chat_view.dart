import 'package:family_management/data/services/sockets.dart';
import 'package:family_management/domain/controllers/start_new_chat_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/avatar_image.dart';

import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class StartNewChatView extends GetView<StartNewChatController> {
  const StartNewChatView({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: controller.appService.locale == const Locale('ar')
          ? TextDirection.rtl
          : TextDirection.ltr,
      child: Scaffold(
        appBar: mainAppBar(
          context: context,
          withBackButton: true,
          title: 'Start new chat'.tr,
          withDrawer: false,
        ),
        body: GetBuilder<StartNewChatController>(
          builder: (controller) => controller.loading
              ? const Center(
                  child: CircularProgressIndicator(),
                )
              : Column(
                  children: [
                    // Search bar
                    Container(
                      padding: const EdgeInsets.all(16),
                      child: TextField(
                        onChanged: (value) {
                          controller.searchQuery = value;
                          controller.update();
                        },
                        decoration: InputDecoration(
                          hintText: 'Search family members...'.tr,
                          prefixIcon: Icon(
                            Icons.search,
                            color: Colors.grey[600],
                          ),
                          suffixIcon: controller.searchQuery.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    controller.clearSearch();
                                  },
                                )
                              : null,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide(
                              color: Colors.grey[300]!,
                              width: 1,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide(
                              color: Colors.grey[300]!,
                              width: 1,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide(
                              color: Theme.of(context).primaryColor,
                              width: 2,
                            ),
                          ),
                          filled: true,
                          fillColor: Colors.grey[50],
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 16,
                          ),
                        ),
                      ),
                    ),

                    // Members list
                    Expanded(
                      child: controller.filteredMembers.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                    'assets/svgs/chat.svg',
                                    width: 64,
                                    height: 64,
                                    colorFilter: ColorFilter.mode(
                                      Colors.grey[400]!,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  TextWidget(
                                    text: controller.searchQuery.isNotEmpty
                                        ? 'No members found'.tr
                                        : 'No family members available'.tr,
                                    fontSize: 16,
                                    color: Colors.grey[600],
                                  ),
                                  if (controller.searchQuery.isNotEmpty) ...[
                                    const SizedBox(height: 8),
                                    TextWidget(
                                      text: 'Try a different search term'.tr,
                                      fontSize: 14,
                                      color: Colors.grey[500],
                                    ),
                                  ],
                                ],
                              ),
                            )
                          : ListView.separated(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              itemCount: controller.filteredMembers.length,
                              separatorBuilder: (context, index) => Divider(
                                color: Colors.grey[200],
                                height: 1,
                              ),
                              itemBuilder: (context, index) {
                                final member =
                                    controller.filteredMembers[index];
                                final isOnline =
                                    Sockets.isUserOnline(member.id!);

                                return ListTile(
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 8,
                                  ),
                                  leading: Stack(
                                    children: [
                                      AvatarImage(
                                        url: member.avatar,
                                        width: 50,
                                        height: 50,
                                      ),
                                      // Online status indicator
                                      Positioned(
                                        right: 0,
                                        bottom: 0,
                                        child: Container(
                                          width: 16,
                                          height: 16,
                                          decoration: BoxDecoration(
                                            color: isOnline
                                                ? Colors.green
                                                : Colors.grey,
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: Colors.white,
                                              width: 2,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  title: TextWidget(
                                    text: member.name ?? 'Unknown',
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  subtitle: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      if (member.email != null) ...[
                                        const SizedBox(height: 4),
                                        TextWidget(
                                          text: member.email!,
                                          fontSize: 14,
                                          color: Colors.grey[600],
                                        ),
                                      ],
                                      const SizedBox(height: 4),
                                      Row(
                                        children: [
                                          Container(
                                            width: 8,
                                            height: 8,
                                            decoration: BoxDecoration(
                                              color: isOnline
                                                  ? Colors.green
                                                  : Colors.grey,
                                              shape: BoxShape.circle,
                                            ),
                                          ),
                                          const SizedBox(width: 6),
                                          Expanded(
                                            child: TextWidget(
                                              text: isOnline
                                                  ? 'Online'.tr
                                                  : member.lastSeenAt != null
                                                      ? 'Last seen ${_formatLastSeen(member.lastSeenAt!)}'
                                                      : 'Offline'.tr,
                                              fontSize: 12,
                                              color: isOnline
                                                  ? Colors.green[700]
                                                  : Colors.grey[600],
                                              fontWeight: FontWeight.w500,
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  trailing: controller.creating
                                      ? const SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                          ),
                                        )
                                      : Icon(
                                          Icons.chat_bubble_outline,
                                          color: Theme.of(context).primaryColor,
                                          size: 24,
                                        ),
                                  onTap: () =>
                                      controller.startChatWithMember(member),
                                );
                              },
                            ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  String _formatLastSeen(DateTime lastSeen) {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);

    if (difference.inMinutes < 1) {
      return 'just now'.tr;
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} ${'minutes ago'.tr}';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ${'hours ago'.tr}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} ${'days ago'.tr}';
    } else {
      return 'a long time ago'.tr;
    }
  }
}
