import 'package:family_management/domain/controllers/view_contact_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/button.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:get/get.dart';

class ContactDetailsView extends GetView<ViewContactController> {
  const ContactDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
        textDirection: controller.appService.direction,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              withDrawer: false,
              withBackButton: true,
              title: 'View contact'.tr),
          body: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                const SizedBox(height: 30),
                ListTile(
                  leading: Container(
                    height: 50,
                    width: 50,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle, color: Colors.grey[200]),
                    child: const Center(
                      child: Icon(CupertinoIcons.person),
                    ),
                  ),
                  title: TextWidget(text: controller.contact.name ?? ''),
                  trailing: Button(
                      text: 'Add'.tr,
                      onPressed: () => FlutterContacts.openExternalInsert(
                          Contact(
                              displayName: controller.contact.name ?? '',
                              phones: controller.contact.phones
                                  .map((e) => Phone(e))
                                  .toList()))),
                  dense: true,
                ),
                Divider(
                  color: Colors.grey[200],
                ),
                ListView.separated(
                    shrinkWrap: true,
                    itemBuilder: (context, index) => ListTile(
                          leading: const Icon(
                            Icons.phone,
                            color: Colors.grey,
                          ),
                          title: TextWidget(
                              text: controller.contact.phones[index]),
                        ),
                    separatorBuilder: (context, index) => Divider(
                          color: Colors.grey[200],
                        ),
                    itemCount: controller.contact.phones.length)
              ],
            ),
          ),
        ));
  }
}
