import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/event_description_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';

class EventDescriptionView extends GetView<EventDescriptionController> {
  const EventDescriptionView({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
        textDirection: TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              title: 'event Name'.tr,
              withBackButton: true,
              withDrawer: false),
          body: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 30),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextWidget(
                          text: 'Event name'.tr,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: pink,
                        ),
                        TextWidget(
                          text: 'frequent event'.tr,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          color: Colors.grey,
                        )
                      ],
                    ),
                    const SizedBox(height: 30),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const Icon(Icons.cake),
                        const SizedBox(width: 5),
                        TextWidget(
                          text: 'Birthday event'.tr,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          color: Colors.grey,
                        )
                      ],
                    ),
                    const SizedBox(height: 10),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const Icon(CupertinoIcons.clock),
                        const SizedBox(width: 5),
                        TextWidget(
                          text: 'Due Date : '.tr,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 5),
                        TextWidget(
                          text: '12 : 12 pm'.tr,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        )
                      ],
                    ),
                    const SizedBox(height: 10),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const Icon(CupertinoIcons.calendar),
                        const SizedBox(width: 5),
                        TextWidget(
                          text: 'the Date :'.tr,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 5),
                        TextWidget(
                          text: '12 / 12 /2025'.tr,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        )
                      ],
                    ),
                    const SizedBox(height: 10),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const Icon(CupertinoIcons.person_add),
                        const SizedBox(width: 5),
                        TextWidget(
                          text: 'Priority people :'.tr,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 5),
                        Row(
                          children: List.generate(
                            6,
                            (index) => const Padding(
                              padding: EdgeInsets.only(right: 1),
                              child: CircleAvatar(
                                radius: 14,
                                child: Icon(
                                  CupertinoIcons.person,
                                  color: Colors.white,
                                  size: 14,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 30),
                    Row(
                      children: [
                        const Icon(CupertinoIcons.location_solid),
                        const SizedBox(width: 5),
                        TextWidget(
                          text: 'Location :'.tr,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    TextWidget(
                      text:
                          'LoremspamLoremspamLoremspamLoremspamLorems pamvLoremspamLoremspam'
                              .tr,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                    const SizedBox(height: 30),
                    Row(
                      children: [
                        const Icon(Icons.notifications_none),
                        const SizedBox(width: 5),
                        TextWidget(
                          text: 'Notify me 30 min before '.tr,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ],
                    ),
                    const SizedBox(height: 30),
                    TextWidget(
                      text: 'The nots for event :'.tr,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 10),
                    TextWidget(
                      text:
                          'LoremspamLoremspamLoremspamLoremspamLorems pamvLoremspamLoremspam'
                              .tr,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                    const SizedBox(height: 30),
                    TextWidget(
                      text: 'event content based on public holidays :'.tr,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 10),
                    TextWidget(
                      text:
                          'LoremspamLoremspamLoremspamLoremspamLorems pamvLoremspamLoremspam'
                              .tr,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                    const SizedBox(height: 30),
                    TextWidget(
                      text: ' event content based on previous events :'.tr,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 10),
                    TextWidget(
                      text:
                          'LoremspamLoremspamLoremspamLoremspamLorems pamvLoremspamLoremspam'
                              .tr,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                    const SizedBox(height: 30),
                    Center(
                      child: Container(
                        width: 250,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: green,
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Icon(
                              CupertinoIcons.airplane,
                              color: Colors.white,
                            ),
                            TextWidget(
                              text: 'The task is completed'.tr,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ));
  }
}
