import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/achievement_board_controller.dart';
import 'package:family_management/presentation/widget/banner_carousel.dart';
import 'package:family_management/presentation/widget/drawer.dart';
import 'package:family_management/presentation/widget/event_card.dart';
import 'package:family_management/presentation/widget/improved_task_card.dart';
import 'package:get/get.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';

class AchievementBoardView extends GetView<AchievementBoardController> {
  const AchievementBoardView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
          textDirection: controller.appService.locale == const Locale('ar')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Scaffold(
            extendBody: true,
            drawer: const MainDrawer(),
            appBar: mainAppBar(
                context: context,
                title: 'Achievement Board'.tr,
                withBackButton: false,
                withDrawer: true),
            body: controller.loading
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : RefreshIndicator(
                    onRefresh: () => controller.getAchievements(),
                    child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        margin: const EdgeInsets.only(bottom: 60),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 30),
                              BannerCarousel(
                                banners: controller.banners,
                                height: 200,
                                width: MediaQuery.of(context).size.width,
                              ),
                              const SizedBox(height: 30),
                              Row(
                                children: [
                                  TextWidget(
                                    text: 'Leaderboard'.tr,
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  const Spacer(),
                                  Icon(
                                    Icons.emoji_events,
                                    color: Colors.amber[600],
                                    size: 20,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 10),
                              _leaderboard(context),
                              const SizedBox(height: 30),
                              Row(
                                children: [
                                  TextWidget(
                                    text: 'Completed events'.tr,
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  const Spacer(),
                                  TextButton(
                                      onPressed: () => Get.toNamed(
                                          AppRoutes.allEvents,
                                          arguments: {'completed': true}),
                                      child: TextWidget(
                                        text: 'See all'.tr,
                                      ))
                                ],
                              ),
                              const SizedBox(height: 10),
                              _completedEvents(context),
                              const SizedBox(height: 30),
                              Row(
                                children: [
                                  TextWidget(
                                    text: 'Tasks completed'.tr,
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  const Spacer(),
                                  TextButton(
                                      onPressed: () => Get.toNamed(
                                          AppRoutes.allTasks,
                                          arguments: {'status': 'done'}),
                                      child: TextWidget(text: 'See all'.tr))
                                ],
                              ),
                              const SizedBox(height: 10),
                              _completedTasks(context),
                              const SizedBox(height: 30),
                              Row(
                                children: [
                                  TextWidget(
                                    text: 'Special occasions'.tr,
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  const Spacer(),
                                  TextButton(
                                      onPressed: () => Get.toNamed(
                                          AppRoutes.allEvents,
                                          arguments: {'occasions': true}),
                                      child: TextWidget(text: 'See all'.tr))
                                ],
                              ),
                              const SizedBox(height: 10),
                              _specialOccasions(context),
                              const SizedBox(height: 30),
                            ],
                          ),
                        )),
                  ),
          )),
    );
  }

  _completedEvents(BuildContext context) {
    if (controller.events.isEmpty) {
      return _buildEmptyState(
        icon: Icons.event_available_outlined,
        message: 'No completed events yet'.tr,
        hint: 'Completed events will appear here'.tr,
        height: 150,
      );
    }

    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: 150,
      child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemCount: controller.events.length,
          itemBuilder: (context, index) {
            final event = controller.events[index];
            return SizedBox(
              width: MediaQuery.of(context).size.width - 60,
              // margin: const EdgeInsets.only(right: 12),
              child: EventCard(
                event: event,
                onPressed: (event) =>
                    Get.toNamed(AppRoutes.eventDetails, arguments: event),
              ),
            );
          }),
    );
  }

  _completedTasks(BuildContext context) {
    if (controller.tasks.isEmpty) {
      return _buildEmptyState(
        icon: Icons.task_alt,
        message: 'No completed tasks yet'.tr,
        hint: 'Completed tasks will appear here'.tr,
        height: 230,
      );
    }

    return SizedBox(
      height: 250,
      child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemCount: controller.tasks.length,
          itemBuilder: (context, index) {
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width - 50,
                  child: ImprovedTaskCard(
                    task: controller.tasks[index],
                    onPressed: (task) =>
                        Get.toNamed(AppRoutes.taskDetails, arguments: task),
                  ),
                ),
                const SizedBox(
                  width: 5,
                )
              ],
            );
          }),
    );
  }

  _specialOccasions(BuildContext context) {
    if (controller.occasions.isEmpty) {
      return _buildEmptyState(
        icon: Icons.celebration_outlined,
        message: 'No special occasions yet'.tr,
        hint: 'Special occasions will appear here'.tr,
        height: 150,
      );
    }

    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: 150,
      child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemCount: controller.occasions.length,
          itemBuilder: (context, index) {
            final event = controller.occasions[index];
            return Container(
              width: MediaQuery.of(context).size.width - 60,
              margin: const EdgeInsets.only(right: 12),
              child: EventCard(
                event: event,
                onPressed: (event) =>
                    Get.toNamed(AppRoutes.eventDetails, arguments: event),
              ),
            );
          }),
    );
  }

  _leaderboard(BuildContext context) {
    if (controller.leaderboard.isEmpty) {
      return _buildEmptyState(
        icon: Icons.emoji_events_outlined,
        message: 'No leaderboard data yet'.tr,
        hint: 'Complete tasks to see rankings here'.tr,
        height: 200,
      );
    }

    return Container(
      height: 280,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue[50]!,
            Colors.purple[50]!,
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Top 3 podium
          if (controller.leaderboard.length >= 3) _buildPodium(context),

          const SizedBox(height: 16),

          // Rest of the leaderboard
          // Expanded(
          //   child: ListView.builder(
          //     itemCount: controller.leaderboard.length > 3
          //         ? controller.leaderboard.length - 3
          //         : 0,
          //     itemBuilder: (context, index) {
          //       final actualIndex = index + 3;
          //       final leaderboardItem = controller.leaderboard[actualIndex];
          //       return _buildLeaderboardItem(
          //           context, leaderboardItem, actualIndex + 1);
          //     },
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildPodium(BuildContext context) {
    final top3 = controller.leaderboard.take(3).toList();

    return SizedBox(
      height: 120,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // 2nd place
          if (top3.length > 1) _buildPodiumItem(context, top3[1], 2, 80),
          // 1st place
          _buildPodiumItem(context, top3[0], 1, 100),
          // 3rd place
          if (top3.length > 2) _buildPodiumItem(context, top3[2], 3, 60),
        ],
      ),
    );
  }

  Widget _buildPodiumItem(
      BuildContext context, leaderboardItem, int position, double height) {
    final colors = {
      1: [Colors.amber[400]!, Colors.amber[600]!],
      2: [Colors.grey[300]!, Colors.grey[500]!],
      3: [Colors.orange[300]!, Colors.orange[500]!],
    };

    final icons = {
      1: Icons.emoji_events,
      2: Icons.military_tech,
      3: Icons.workspace_premium,
    };

    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // User avatar and info
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: colors[position]!,
            ),
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: colors[position]![1].withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              // Trophy icon
              Icon(
                icons[position],
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(height: 4),
              // User avatar
              CircleAvatar(
                radius: 20,
                backgroundImage: leaderboardItem.user?.avatar != null
                    ? NetworkImage(leaderboardItem.user!.avatar!)
                    : null,
                backgroundColor: Colors.grey[300],
                child: leaderboardItem.user?.avatar == null
                    ? Icon(Icons.person, color: Colors.grey[600])
                    : null,
              ),
              const SizedBox(height: 4),
              // User name
              SizedBox(
                width: 60,
                child: TextWidget(
                  text: leaderboardItem.user?.name ?? 'Unknown',
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  textAlign: TextAlign.center,
                  maxLines: 1,
                ),
              ),
              const SizedBox(height: 2),
              // Score
              TextWidget(
                text: '${leaderboardItem.score ?? 0}',
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ],
          ),
        ),
        // Podium base
        Container(
          width: 60,
          height: height,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: colors[position]!,
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: Center(
            child: TextWidget(
              text: '$position',
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLeaderboardItem(
      BuildContext context, leaderboardItem, int position) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.grey[100]!,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Position
          Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(15),
            ),
            child: Center(
              child: TextWidget(
                text: '$position',
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
          ),
          const SizedBox(width: 12),
          // User avatar
          CircleAvatar(
            radius: 20,
            backgroundImage: leaderboardItem.user?.avatar != null
                ? NetworkImage(leaderboardItem.user!.avatar!)
                : null,
            backgroundColor: Colors.grey[300],
            child: leaderboardItem.user?.avatar == null
                ? Icon(Icons.person, color: Colors.grey[600], size: 20)
                : null,
          ),
          const SizedBox(width: 12),
          // User name
          Expanded(
            child: TextWidget(
              text: leaderboardItem.user?.name ?? 'Unknown',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey[800],
            ),
          ),
          // Score
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(20),
            ),
            child: TextWidget(
              text: '${leaderboardItem.score ?? 0} pts',
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds an empty state placeholder widget with a subtle animation
  Widget _buildEmptyState({
    required IconData icon,
    required String message,
    required String hint,
    double? height,
  }) {
    return Container(
      height: height,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.grey[300]!, width: 1),
      ),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _PulsingIcon(
            icon: icon,
            size: 48,
            color: Colors.grey[400]!,
          ),
          const SizedBox(height: 12),
          TextWidget(
            text: message,
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          TextWidget(
            text: hint,
            fontSize: 14,
            color: Colors.grey[500],
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// A widget that displays an icon with a subtle pulsing animation
class _PulsingIcon extends StatefulWidget {
  final IconData icon;
  final double size;
  final Color color;

  const _PulsingIcon({
    required this.icon,
    required this.size,
    required this.color,
  });

  @override
  State<_PulsingIcon> createState() => _PulsingIconState();
}

class _PulsingIconState extends State<_PulsingIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _animation = Tween<double>(begin: 0.8, end: 1.1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: _animation.value,
          alignment: Alignment.center,
          child: SizedBox(
            width: widget.size,
            height: widget.size,
            child: Center(
              child: Icon(
                widget.icon,
                size: widget.size,
                color: widget.color,
              ),
            ),
          ),
        );
      },
    );
  }
}
