import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/achievement_board_controller.dart';
import 'package:family_management/presentation/widget/banner_carousel.dart';
import 'package:family_management/presentation/widget/drawer.dart';
import 'package:family_management/presentation/widget/event_card.dart';
import 'package:family_management/presentation/widget/improved_task_card.dart';
import 'package:get/get.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';

class AchievementBoardView extends GetView<AchievementBoardController> {
  const AchievementBoardView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
          textDirection: controller.appService.locale == const Locale('ar')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Scaffold(
            extendBody: true,
            drawer: const MainDrawer(),
            appBar: mainAppBar(
                context: context,
                title: 'Achievement Board'.tr,
                withBackButton: false,
                withDrawer: true),
            body: controller.loading
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : RefreshIndicator(
                    onRefresh: () => controller.getAchievements(),
                    child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        margin: const EdgeInsets.only(bottom: 60),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 30),
                              BannerCarousel(
                                banners: controller.banners,
                                height: 200,
                                width: MediaQuery.of(context).size.width,
                              ),
                              const SizedBox(height: 30),
                              Row(
                                children: [
                                  TextWidget(
                                    text: 'Leaderboard'.tr,
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  const Spacer(),
                                  Icon(
                                    Icons.emoji_events,
                                    color: Colors.amber[600],
                                    size: 20,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 10),
                              _leaderboard(context),
                              const SizedBox(height: 30),
                              Row(
                                children: [
                                  TextWidget(
                                    text: 'Completed events'.tr,
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  const Spacer(),
                                  TextButton(
                                      onPressed: () => Get.toNamed(
                                          AppRoutes.allEvents,
                                          arguments: {'completed': true}),
                                      child: TextWidget(
                                        text: 'See all'.tr,
                                      ))
                                ],
                              ),
                              const SizedBox(height: 10),
                              _completedEvents(context),
                              const SizedBox(height: 30),
                              Row(
                                children: [
                                  TextWidget(
                                    text: 'Tasks completed'.tr,
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  const Spacer(),
                                  TextButton(
                                      onPressed: () => Get.toNamed(
                                          AppRoutes.allTasks,
                                          arguments: {'status': 'done'}),
                                      child: TextWidget(text: 'See all'.tr))
                                ],
                              ),
                              const SizedBox(height: 10),
                              _completedTasks(context),
                              const SizedBox(height: 30),
                              Row(
                                children: [
                                  TextWidget(
                                    text: 'Special occasions'.tr,
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  const Spacer(),
                                  TextButton(
                                      onPressed: () => Get.toNamed(
                                          AppRoutes.allEvents,
                                          arguments: {'occasions': true}),
                                      child: TextWidget(text: 'See all'.tr))
                                ],
                              ),
                              const SizedBox(height: 10),
                              _specialOccasions(context),
                              const SizedBox(height: 30),
                            ],
                          ),
                        )),
                  ),
          )),
    );
  }

  _completedEvents(BuildContext context) {
    if (controller.events.isEmpty) {
      return _buildEmptyState(
        icon: Icons.event_available_outlined,
        message: 'No completed events yet'.tr,
        hint: 'Completed events will appear here'.tr,
        height: 150,
      );
    }

    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: 150,
      child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemCount: controller.events.length,
          itemBuilder: (context, index) {
            final event = controller.events[index];
            return SizedBox(
              width: MediaQuery.of(context).size.width - 60,
              // margin: const EdgeInsets.only(right: 12),
              child: EventCard(
                event: event,
                onPressed: (event) =>
                    Get.toNamed(AppRoutes.eventDetails, arguments: event),
              ),
            );
          }),
    );
  }

  _completedTasks(BuildContext context) {
    if (controller.tasks.isEmpty) {
      return _buildEmptyState(
        icon: Icons.task_alt,
        message: 'No completed tasks yet'.tr,
        hint: 'Completed tasks will appear here'.tr,
        height: 230,
      );
    }

    return SizedBox(
      height: 250,
      child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemCount: controller.tasks.length,
          itemBuilder: (context, index) {
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width - 50,
                  child: ImprovedTaskCard(
                    task: controller.tasks[index],
                    onPressed: (task) =>
                        Get.toNamed(AppRoutes.taskDetails, arguments: task),
                  ),
                ),
                const SizedBox(
                  width: 5,
                )
              ],
            );
          }),
    );
  }

  _specialOccasions(BuildContext context) {
    if (controller.occasions.isEmpty) {
      return _buildEmptyState(
        icon: Icons.celebration_outlined,
        message: 'No special occasions yet'.tr,
        hint: 'Special occasions will appear here'.tr,
        height: 150,
      );
    }

    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: 150,
      child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemCount: controller.occasions.length,
          itemBuilder: (context, index) {
            final event = controller.occasions[index];
            return Container(
              width: MediaQuery.of(context).size.width - 60,
              margin: const EdgeInsets.only(right: 12),
              child: EventCard(
                event: event,
                onPressed: (event) =>
                    Get.toNamed(AppRoutes.eventDetails, arguments: event),
              ),
            );
          }),
    );
  }

  _leaderboard(BuildContext context) {
    if (controller.leaderboard.isEmpty) {
      return _buildEmptyState(
        icon: Icons.emoji_events_outlined,
        message: 'No leaderboard data yet'.tr,
        hint: 'Complete tasks to see rankings here'.tr,
        height: 180,
      );
    }

    return SizedBox(
      height: 250,
      child: ListView.builder(
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        itemCount: controller.leaderboard.length,
        itemBuilder: (context, index) {
          final leaderboardItem = controller.leaderboard[index];
          return Container(
            width: MediaQuery.of(context).size.width - 80,
            margin: const EdgeInsets.only(right: 12),
            child: _buildLeaderboardCard(context, leaderboardItem, index + 1),
          );
        },
      ),
    );
  }

  Widget _buildLeaderboardCard(
      BuildContext context, leaderboardItem, int position) {
    // Define colors and icons for top 3 positions
    final isTopThree = position <= 3;
    final colors = {
      1: [Colors.amber[400]!, Colors.amber[600]!],
      2: [Colors.grey[400]!, Colors.grey[600]!],
      3: [Colors.orange[400]!, Colors.orange[600]!],
    };

    final icons = {
      1: Icons.emoji_events,
      2: Icons.military_tech,
      3: Icons.workspace_premium,
    };

    return Container(
      decoration: BoxDecoration(
        gradient: isTopThree
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors:
                    colors[position] ?? [Colors.blue[50]!, Colors.blue[100]!],
              )
            : LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.grey[50]!, Colors.grey[100]!],
              ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
            color: isTopThree
                ? (colors[position]?[1] ?? Colors.grey[300]!)
                : Colors.grey[200]!,
            width: 2),
        boxShadow: [
          BoxShadow(
            color: isTopThree
                ? (colors[position]?[1].withValues(alpha: 0.3) ??
                    Colors.grey[300]!.withValues(alpha: 0.3))
                : Colors.grey[200]!.withValues(alpha: 0.5),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Position and trophy icon
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isTopThree ? Colors.white : Colors.grey[200],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: TextWidget(
                    text: '$position',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isTopThree
                        ? (colors[position]?[1] ?? Colors.grey[700])
                        : Colors.grey[700],
                  ),
                ),
              ),
              if (isTopThree)
                Icon(
                  icons[position],
                  color: Colors.white,
                  size: 28,
                ),
            ],
          ),

          const SizedBox(height: 16),

          // User avatar
          CircleAvatar(
            radius: 30,
            backgroundImage: leaderboardItem.user?.avatar != null
                ? NetworkImage(leaderboardItem.user!.avatar!)
                : null,
            backgroundColor: Colors.white,
            child: leaderboardItem.user?.avatar == null
                ? Icon(Icons.person, color: Colors.grey[600], size: 30)
                : null,
          ),

          const SizedBox(height: 12),

          // User name
          TextWidget(
            text: leaderboardItem.user?.name ?? 'Unknown',
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: isTopThree ? Colors.white : Colors.grey[800],
            textAlign: TextAlign.center,
            maxLines: 1,
          ),

          const SizedBox(height: 8),

          // Score
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isTopThree ? Colors.white : Colors.blue[50],
              borderRadius: BorderRadius.circular(20),
            ),
            child: TextWidget(
              text: '${leaderboardItem.score ?? 0} pts',
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: isTopThree
                  ? (colors[position]?[1] ?? Colors.blue[700])
                  : Colors.blue[700],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds an empty state placeholder widget with a subtle animation
  Widget _buildEmptyState({
    required IconData icon,
    required String message,
    required String hint,
    double? height,
  }) {
    return Container(
      height: height,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.grey[300]!, width: 1),
      ),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _PulsingIcon(
            icon: icon,
            size: 48,
            color: Colors.grey[400]!,
          ),
          const SizedBox(height: 12),
          TextWidget(
            text: message,
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          TextWidget(
            text: hint,
            fontSize: 14,
            color: Colors.grey[500],
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// A widget that displays an icon with a subtle pulsing animation
class _PulsingIcon extends StatefulWidget {
  final IconData icon;
  final double size;
  final Color color;

  const _PulsingIcon({
    required this.icon,
    required this.size,
    required this.color,
  });

  @override
  State<_PulsingIcon> createState() => _PulsingIconState();
}

class _PulsingIconState extends State<_PulsingIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _animation = Tween<double>(begin: 0.8, end: 1.1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: _animation.value,
          alignment: Alignment.center,
          child: SizedBox(
            width: widget.size,
            height: widget.size,
            child: Center(
              child: Icon(
                widget.icon,
                size: widget.size,
                color: widget.color,
              ),
            ),
          ),
        );
      },
    );
  }
}
