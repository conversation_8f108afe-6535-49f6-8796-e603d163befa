import 'package:family_management/config/themes.dart';
import 'package:get/get.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';

import '../../domain/controllers/achievement_board_details_controller.dart';
import '../chat/widgets/search_box.dart';

class AchievementBoardDetailsView
    extends GetView<AchievementBoardDetailsController> {
  const AchievementBoardDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
        textDirection: TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              title: 'Achievement Board'.tr,
              withBackButton: true,
              withDrawer: false),
          body: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 30),
                    const SearchBox(),
                    const SizedBox(height: 30),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 200,
                          decoration: BoxDecoration(
                            border: Border.all(
                              width: 1,
                              color: pink,
                            ),
                            borderRadius: BorderRadius.circular(10),
                            color: lightPink2,
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 10),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              TextWidget(
                                text: '18 / 30'.tr,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                                color: Colors.black,
                              ),
                              TextWidget(
                                text: 'Completed'.tr,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 10)
                      ],
                    ),
                    const SizedBox(height: 30),
                    TextWidget(
                      text: 'All events'.tr,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    const SizedBox(height: 10),
                    _allEvents(context),
                    const SizedBox(height: 30),
                    TextWidget(
                      text: 'All Tasks'.tr,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    const SizedBox(height: 10),
                    _allTasks(context),
                  ],
                ),
              )),
        ));
  }

  _allEvents(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: 100,
      child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemCount: 5,
          itemBuilder: (context, index) {
            return Row(
              children: [
                Container(
                  width: 250,
                  decoration: BoxDecoration(
                    border: Border.all(
                        width: 1, color: Theme.of(context).primaryColor),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 3),
                          Container(
                            height: 10,
                            width: 10,
                            decoration: BoxDecoration(
                                border: Border.all(width: 2.5, color: pink),
                                shape: BoxShape.circle),
                          ),
                        ],
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(
                            text: '19:00 - 20:00'.tr,
                            color: pink,
                            fontSize: 12,
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          TextWidget(
                            text: 'Workout with Ella'.tr,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          TextWidget(
                            text: 'we will do the legs and back workout'.tr,
                            color: Colors.grey,
                            fontSize: 11,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          )
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 10)
              ],
            );
          }),
    );
  }

  _allTasks(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: 125,
      child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemCount: 5,
          itemBuilder: (context, index) {
            return Row(
              children: [
                Container(
                  width: 250,
                  decoration: BoxDecoration(
                    color: lightPink,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 5),
                          Row(
                            children: [
                              TextWidget(
                                text: 'Workout with Ella'.tr,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                                color: Colors.white,
                              ),
                              const SizedBox(
                                width: 5,
                              ),
                              TextWidget(
                                text: '13 / 2 / 2024'.tr,
                                fontSize: 12,
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          TextWidget(
                            text: 'Owner task : ahmad ali'.tr,
                            color: Colors.grey,
                            fontSize: 11,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 5),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 10)
              ],
            );
          }),
    );
  }
}
