import 'package:family_management/config/app_routes.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/expense_controller.dart';
import 'package:family_management/domain/enums/task_type.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../widget/loading_indicator.dart';

class TotalExpenseView extends GetView<ExpenseController> {
  const TotalExpenseView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
            context: context,
            withDrawer: false,
            withBackButton: true,
            title: 'Total Expenses'.tr,
          ),
          body: RefreshIndicator(
            onRefresh: () => controller.getExpenses(),
            child: controller.loading
                ? const Center(child: LoadingIndicator())
                : controller.expenses.isEmpty
                    ? Center(
                        child: TextWidget(text: 'You have no expenses'.tr),
                      )
                    : Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 30),
                            _expenses(context),
                          ],
                        ),
                      ),
          ),
        ),
      ),
    );
  }

  _expenses(BuildContext context) {
    return Expanded(
      child: GridView.builder(
        clipBehavior: Clip.none,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 20,
          mainAxisSpacing: 10,
          childAspectRatio: 1,
        ),
        itemCount: controller.expenses.length,
        itemBuilder: (context, index) {
          final item = controller.expenses[index];
          return GestureDetector(
            onTap: () {
              controller.selectedExpense = item;
              Get.toNamed(AppRoutes.expenseDetails, arguments: item);
            },
            child: Container(
              width: 250,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(5),
                  shape: BoxShape.rectangle,
                  boxShadow: const [
                    BoxShadow(
                      blurRadius: 1,
                      color: Colors.grey,
                    )
                  ]),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextWidget(
                    text: (controller.expenses[index].type == TaskType.recurring
                            ? 'Recurrent expenditure'
                            : 'One time expenditure')
                        .tr,
                    fontSize: 10,
                    color: pink,
                    fontWeight: FontWeight.bold,
                  ),
                  const SizedBox(height: 5),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextWidget(
                        text: '\$${controller.expenses[index].amount}'.tr,
                        fontSize: 24,
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextWidget(
                        text:
                            '${controller.expenses[index].expenseCategory!.name}',
                        fontSize: 16,
                        color: Colors.black,
                      ),
                      TextWidget(
                        text: controller.expenses[index].date
                            .toString()
                            .substring(0, 10)
                            .tr,
                        fontSize: 6,
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  TextWidget(
                    text: '${controller.expenses[index].description}'.tr,
                    fontSize: 10,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
