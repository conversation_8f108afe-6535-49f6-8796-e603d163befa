import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/expense_details_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class ExpenseDetailsView extends GetView<ExpenseDetailsController> {
  const ExpenseDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              title: 'Expense Details'.tr,
              withBackButton: true,
              withDrawer: false),
          body: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 30),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(Icons.category_outlined),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: 'Category :'.tr,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: controller.expense.expenseCategory!.name!.tr,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      )
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(CupertinoIcons.clock),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: 'Date : '.tr,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: controller.expense.date != null
                            ? intl.DateFormat('d / M / y')
                                .format(controller.expense.date!)
                            : 'N/A',
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      )
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(CupertinoIcons.person),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: 'Responsible Person :'.tr,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: controller.expense.user?.name ?? '',
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      )
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(CupertinoIcons.money_dollar),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: 'Amount :'.tr,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: '${controller.expense.amount}'.tr,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      )
                    ],
                  ),
                  const SizedBox(height: 30),
                  TextWidget(
                    text: 'The expense type :'.tr,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 10),
                  TextWidget(
                    text: '${controller.expense.type}',
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                  const SizedBox(height: 10),
                  TextWidget(
                    text: 'The expense description :'.tr,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 10),
                  TextWidget(
                    text: controller.expense.description!.tr,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                  const SizedBox(height: 30),
                  Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () =>
                              controller.deleteExpense(controller.expense.id!),
                          child: Container(
                            width: 150,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              color: Colors.red,
                            ),
                            alignment: Alignment.center,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 10,
                              vertical: 15,
                            ),
                            child: controller.loading
                                ? const SizedBox(
                                    height: 25,
                                    width: 25,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.white,
                                    ),
                                  )
                                : TextWidget(
                                    text: 'Delete'.tr,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                    color: Colors.white,
                                  ),
                          ),
                        ),
                        const SizedBox(width: 20),
                        GestureDetector(
                          onTap: () {
                            Get.toNamed(
                              AppRoutes.addExpense,
                              arguments: controller.expense,
                            );
                          },
                          child: Container(
                            width: 150,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              color: Theme.of(context).primaryColor,
                            ),
                            alignment: Alignment.center,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 10,
                              vertical: 15,
                            ),
                            child: TextWidget(
                              text: 'Edit'.tr,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
