import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_routes.dart';
import '../../domain/controllers/expense_controller.dart';
import '../widget/loading_indicator.dart';
import '../widget/text_widget.dart';
import '../widget/app_bar.dart';

class BudgetTrackingView extends GetView<ExpenseController> {
  const BudgetTrackingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Obx(
          () => Scaffold(
            appBar: mainAppBar(
                context: context,
                title: 'Budget Tracking'.tr,
                withDrawer: false,
                withBackButton: true),
            body: RefreshIndicator(
              onRefresh: () => controller.getExpenseCategories(),
              child: controller.loading
                  ? const Center(child: LoadingIndicator())
                  : controller.latestExpenses.isEmpty
                      ? _buildEmptyState(context)
                      : _buildContent(context),
            ),
            floatingActionButton: FloatingActionButton(
              heroTag: "budget_tracking_fab",
              onPressed: () => Get.toNamed(AppRoutes.addExpense),
              backgroundColor: Theme.of(context).primaryColor,
              child: const Icon(
                Icons.add,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 80,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          TextWidget(
            text: 'No expenses yet'.tr,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 8),
          TextWidget(
            text:
                'Start tracking your expenses by adding your first expense'.tr,
            fontSize: 14,
            color: Colors.grey[500],
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Get.toNamed(AppRoutes.addExpense),
            icon: const Icon(Icons.add),
            label: TextWidget(
              text: 'Add First Expense'.tr,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 20),
                  _buildTotalBalanceCard(context),
                  const SizedBox(height: 30),
                  _buildSectionHeader('Recent Expenses'.tr, 'View all'.tr, () {
                    controller.getExpenses();
                    Get.toNamed(AppRoutes.totalExpense);
                  }),
                  const SizedBox(height: 16),
                  _buildRecentExpenses(context),
                  const SizedBox(height: 30),
                  _buildSectionHeader('Expense Categories'.tr, null, null),
                  const SizedBox(height: 16),
                  _buildPieChart(),
                  const SizedBox(height: 20),
                  _buildLegend(),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTotalBalanceCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextWidget(
                text: 'Total Expenses'.tr,
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.9),
                fontWeight: FontWeight.w500,
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 14,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                    const SizedBox(width: 4),
                    TextWidget(
                      text: _getDateRangeText(),
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextWidget(
            text:
                '\$${controller.chartData.value?.total?.total?.toStringAsFixed(2) ?? '0.00'}',
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.trending_up,
                size: 16,
                color: Colors.white.withValues(alpha: 0.8),
              ),
              const SizedBox(width: 4),
              TextWidget(
                text:
                    '${controller.latestExpenses.length} transactions this period'
                        .tr,
                fontSize: 14,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(
      String title, String? actionText, VoidCallback? onAction) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        TextWidget(
          text: title,
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
        if (actionText != null && onAction != null)
          TextButton(
            onPressed: onAction,
            child: TextWidget(
              text: actionText,
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Theme.of(Get.context!).primaryColor,
            ),
          ),
      ],
    );
  }

  String _getDateRangeText() {
    if (controller.latestExpenses.isEmpty) return 'No data';

    // Get the oldest and newest dates from latest expenses
    DateTime? oldest, newest;
    for (var expense in controller.latestExpenses) {
      if (expense.date != null) {
        if (oldest == null || expense.date!.isBefore(oldest)) {
          oldest = expense.date;
        }
        if (newest == null || expense.date!.isAfter(newest)) {
          newest = expense.date;
        }
      }
    }

    if (oldest == null || newest == null) return 'No dates';

    if (oldest.year == newest.year && oldest.month == newest.month) {
      return '${oldest.day}-${newest.day} ${_getMonthName(oldest.month)}';
    } else {
      return '${oldest.day} ${_getMonthName(oldest.month)} - ${newest.day} ${_getMonthName(newest.month)}';
    }
  }

  String _getMonthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }

  Widget _buildRecentExpenses(BuildContext context) {
    if (controller.latestExpenses.isEmpty) {
      return Container(
        height: 120,
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.receipt_long, color: Colors.grey[400], size: 32),
              const SizedBox(height: 8),
              TextWidget(
                text: 'No recent expenses'.tr,
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ],
          ),
        ),
      );
    }

    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: controller.latestExpenses.length,
        itemBuilder: (context, index) {
          final expense = controller.latestExpenses[index];
          return _buildExpenseCard(context, expense, index);
        },
      ),
    );
  }

  Widget _buildExpenseCard(BuildContext context, expense, int index) {
    return Container(
      width: 160,
      height: 120, // Fixed height to prevent overflow
      margin: EdgeInsets.only(
          right: index < controller.latestExpenses.length - 1 ? 16 : 0),
      padding: const EdgeInsets.all(12), // Reduced padding
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: GestureDetector(
        onTap: () {
          controller.selectedExpense = expense;
          Get.toNamed(AppRoutes.expenseDetails, arguments: expense);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // Prevent overflow
          children: [
            Row(
              children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: controller.getCategoryColor(
                        expense.expenseCategory?.name ?? 'Unknown'),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: TextWidget(
                    text: expense.expenseCategory?.name ?? 'Unknown',
                    fontSize: 10,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            TextWidget(
              text: '\$${expense.amount?.toStringAsFixed(2) ?? '0.00'}',
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            const SizedBox(height: 4),
            Expanded(
              child: TextWidget(
                text: expense.description ?? 'No description',
                fontSize: 11,
                color: Colors.grey[500],
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            TextWidget(
              text: expense.date?.toString().substring(0, 10) ?? '',
              fontSize: 9,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPieChart() {
    if (controller.chartData.value?.categories == null ||
        controller.chartData.value!.categories!.isEmpty) {
      return const SizedBox(
        height: 250,
        child: Center(
          child: TextWidget(
            text: 'No expense data available',
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    final categories = controller.chartData.value!.categories!;
    final int total = controller.chartData.value!.total!.total ?? 0;

    return SizedBox(
      height: 250,
      child: PieChart(
        PieChartData(
          sections: categories.map((category) {
            final double percentage =
                ((category.totalExpenses ?? 0) / total) * 100;
            return PieChartSectionData(
              color: controller.getCategoryColor(category.name ?? 'Unknown'),
              value: (category.totalExpenses ?? 0).toDouble(),
              title: '${percentage.toStringAsFixed(1)}%',
              radius: 60,
              titleStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            );
          }).toList(),
          centerSpaceRadius: 50,
          sectionsSpace: 2,
        ),
      ),
    );
  }

  Widget _buildLegend() {
    if (controller.chartData.value?.categories == null ||
        controller.chartData.value!.categories!.isEmpty) {
      return const SizedBox.shrink();
    }

    final categories = controller.chartData.value!.categories!;
    final int total = controller.chartData.value!.total!.total ?? 0;

    return Column(
      children: categories.map((category) {
        final double percentage = ((category.totalExpenses ?? 0) / total) * 100;
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 5),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: controller
                          .getCategoryColor(category.name ?? 'Unknown'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  TextWidget(
                    text: (category.name ?? 'Unknown').tr,
                    fontSize: 10,
                  ),
                ],
              ),
              TextWidget(
                text: '\$${(category.totalExpenses ?? 0).toStringAsFixed(2)}',
                fontSize: 10,
              ),
              TextWidget(
                text: '${percentage.toStringAsFixed(1)}%',
                fontSize: 10,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
