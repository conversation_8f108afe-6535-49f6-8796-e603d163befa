import 'package:family_management/domain/controllers/add_expense_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../widget/custom_dropdown.dart';
import '../widget/date_picker.dart';
import '../widget/text_widget.dart';
import 'package:intl/intl.dart' as intl;

class AddExpenseView extends GetView<AddExpenseController> {
  const AddExpenseView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              title: 'Add Expense'.tr,
              withBackButton: true,
              withDrawer: false),
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 30),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: TextInput(
                      controller: controller.amountController,
                      hint: '+ Add the Amount'.tr,
                      prefixIcon: const SizedBox.shrink(),
                      error: controller.amountError,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: CustomDropdown(
                      items: controller.expenseCategories
                          .map((e) => DropdownMenuItem<String>(
                                value: e.id.toString(),
                                child: TextWidget(text: e.name ?? ''),
                              ))
                          .toList(),
                      hint: '+ Add the Category'.tr,
                      value: controller.selectedCategory?.id.toString(),
                      onChanged: (value) {
                        controller.selectedCategory = controller
                            .expenseCategories
                            .firstWhereOrNull((e) => e.id.toString() == value);
                      },
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: DatePicker(
                      error: controller.dateError,
                      value: controller.date,
                      onChange: (value) =>
                      controller.date = intl.DateFormat('y-M-d').format(value!),
                      label: '+ Add  Due Date'.tr,
                      prefixIcon: IconButton(
                        onPressed: null,
                        icon: Icon(CupertinoIcons.clock,
                            color: Get.theme.primaryColor),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: TextInput(
                      error: controller.descriptionError,
                      controller: controller.descriptionController,
                      hint: 'Type the Description here...'.tr,
                      minLines: 4,
                      maxLines: 10,
                      prefixIcon: const SizedBox.shrink(),
                    ),
                  ),
                  Row(
                    children: [
                      Checkbox(
                        value: controller.selectedExpenseType,
                        onChanged: (value) =>
                        controller.selectedExpenseType = value ?? false,
                        activeColor: Theme.of(context).primaryColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5),
                          side: BorderSide(
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                      TextWidget(
                        text: 'Recurring alimony'.tr,
                        fontSize: 10,
                      )
                    ],
                  ),
                  const SizedBox(height: 40),
                  FullWidthButton(
                    text: 'Confirm'.tr,
                    onPressed: () => controller.validate(),
                    loading: controller.loading,
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
