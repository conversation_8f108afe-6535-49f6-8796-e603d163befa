import 'package:family_management/domain/controllers/map_controller.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/presentation/widget/avatar_image.dart';
import 'package:family_management/presentation/widget/back_button.dart';
import 'package:family_management/presentation/widget/online_status_indicator.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MapView extends GetView<MapController> {
  const MapView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        // Show streamers modal when controller indicates it should be shown
        if (controller.streamersModalShown && controller.users.isNotEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _showStreamersModal(context);
            controller
                .setStreamersModalShown(false); // Reset flag after showing
          });
        }

        return Directionality(
          textDirection: controller.appService.direction,
          child: Scaffold(
            body: SafeArea(
              child: GetBuilder<MapController>(builder: (controller) {
                return SizedBox(
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                  child: Stack(
                    children: [
                      SizedBox(
                        height: MediaQuery.of(context).size.height,
                        width: MediaQuery.of(context).size.width,
                        child: GoogleMap(
                          markers: <Marker>{
                            ...controller.users.map(
                              (user) => Marker(
                                  onTap: () {
                                    controller.selectedUser = user;
                                    controller.update();
                                  },
                                  markerId: MarkerId(user.id.toString()),
                                  position:
                                      LatLng(user.lat ?? 0, user.lng ?? 0),
                                  infoWindow: InfoWindow(
                                    title: '${user.name}',
                                  )),
                            )
                          },
                          initialCameraPosition: controller.centerLocation,
                          polygons: controller.polygons,
                          onMapCreated: (mapController) =>
                              controller.complete(mapController),
                          myLocationButtonEnabled: true,
                          myLocationEnabled: true,
                        ),
                      ),
                      Positioned(top: 10, left: 20, child: CustomBackButton()),
                      // Streamers list button
                      if (controller.users.isNotEmpty)
                        Positioned(
                          top: 110,
                          right: 10,
                          child: GestureDetector(
                            onTap: () => _showStreamersModal(context),
                            child: Container(
                              height: 50,
                              width: 50,
                              decoration: BoxDecoration(
                                color:
                                    Theme.of(context).scaffoldBackgroundColor,
                                borderRadius: BorderRadius.circular(10),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Stack(
                                children: [
                                  const Center(
                                    child: Icon(
                                      Icons.people_alt_rounded,
                                      color: Colors.blue,
                                      size: 20,
                                    ),
                                  ),
                                  Positioned(
                                    top: 6,
                                    right: 6,
                                    child: Container(
                                      padding: const EdgeInsets.all(2),
                                      decoration: BoxDecoration(
                                        color: Colors.red,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      constraints: const BoxConstraints(
                                        minWidth: 16,
                                        minHeight: 16,
                                      ),
                                      child: Text(
                                        '${controller.users.length}',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      // Move stop/start button to top right
                      controller.locationServiceRunning
                          ? Positioned(
                              top: 50,
                              right: 10,
                              child: GestureDetector(
                                onTap: () => controller.stopSharingLocation(),
                                child: Container(
                                  height: 50,
                                  width: 50,
                                  decoration: BoxDecoration(
                                      color: Theme.of(context)
                                          .scaffoldBackgroundColor,
                                      borderRadius: BorderRadius.circular(10),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black
                                              .withValues(alpha: 0.1),
                                          blurRadius: 4,
                                          offset: const Offset(0, 2),
                                        ),
                                      ]),
                                  child: const Center(
                                    child: Icon(
                                      FontAwesomeIcons.powerOff,
                                      color: Colors.red,
                                      size: 20,
                                    ),
                                  ),
                                ),
                              ))
                          : Positioned(
                              top: 50,
                              right: 10,
                              child: GestureDetector(
                                onTap: () => controller.shareMyLocation(),
                                child: Container(
                                  height: 50,
                                  width: 50,
                                  decoration: BoxDecoration(
                                      color: Theme.of(context)
                                          .scaffoldBackgroundColor,
                                      borderRadius: BorderRadius.circular(10),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black
                                              .withValues(alpha: 0.1),
                                          blurRadius: 4,
                                          offset: const Offset(0, 2),
                                        ),
                                      ]),
                                  child: const Center(
                                    child: Icon(
                                      FontAwesomeIcons.locationArrow,
                                      color: Colors.blue,
                                      size: 20,
                                    ),
                                  ),
                                ),
                              )),
                      if (controller.selectedUser.id != null)
                        _showSelectedUser(context)
                    ],
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }

  _showSelectedUser(BuildContext context) {
    return Positioned(
        bottom: 60,
        left: 25,
        child: Container(
          height: 200,
          width: 300,
          padding: const EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              color: Colors.white.withValues(alpha: 0.9)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // User avatar with online status
                  Stack(
                    children: [
                      AvatarImage(
                        url: controller.selectedUser.avatar,
                        width: 50,
                        height: 50,
                      ),
                      Positioned(
                        right: 0,
                        bottom: 0,
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: controller.selectedUser.isOnline
                                ? Colors.green
                                : Colors.grey,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white,
                              width: 1.5,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  // Close button
                  IconButton(
                    onPressed: () {
                      controller.selectedUser = User();
                      controller.update();
                    },
                    icon: const Icon(Icons.close),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              TextWidget(
                text: controller.selectedUser.name ?? '',
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              const SizedBox(height: 4),
              OnlineStatusIndicator(
                user: controller.selectedUser,
                dotSize: 8,
                textStyle: const TextStyle(fontSize: 11),
              ),
              const SizedBox(height: 4),
              TextWidget(
                text: '${controller.selectedUser.email}',
                fontSize: 12,
              ),
              TextWidget(
                text:
                    '${'Battery level'.tr}: ${controller.selectedUser.battery}',
                fontSize: 12,
              )
            ],
          ),
        ));
  }

  void _showStreamersModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.2, // 1/5 of screen height
        minChildSize: 0.1,
        maxChildSize: 0.6,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 8),
                height: 4,
                width: 40,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.people_alt_rounded,
                          color: Theme.of(context).primaryColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        TextWidget(
                          text: 'Online Users'.tr,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: TextWidget(
                        text: '${controller.users.length}',
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),

              // Users list
              Expanded(
                child: Obx(() {
                  if (controller.users.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.person_off_outlined,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          TextWidget(
                            text: 'No online users'.tr,
                            fontSize: 16,
                            color: Colors.grey[600],
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    controller: scrollController,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    itemCount: controller.users.length,
                    itemBuilder: (context, index) {
                      final user = controller.users[index];
                      return InkWell(
                        onTap: () {
                          Navigator.pop(context);
                          controller.mapController.animateCamera(
                            CameraUpdate.newLatLng(
                              LatLng(user.lat ?? 0, user.lng ?? 0),
                            ),
                          );
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          child: Row(
                            children: [
                              Stack(
                                children: [
                                  AvatarImage(
                                    url: user.avatar,
                                    width: 40,
                                    height: 40,
                                  ),
                                  Positioned(
                                    right: 0,
                                    bottom: 0,
                                    child: Container(
                                      width: 12,
                                      height: 12,
                                      decoration: BoxDecoration(
                                        color: user.isOnline
                                            ? Colors.green
                                            : Colors.grey,
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: Colors.white,
                                          width: 2,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    TextWidget(
                                      text: user.name ?? '',
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 4),
                                    if (user.battery != null)
                                      Row(
                                        children: [
                                          Icon(
                                            _getBatteryIcon(user.battery ?? 0),
                                            size: 14,
                                            color: _getBatteryColor(
                                                user.battery ?? 0),
                                          ),
                                          const SizedBox(width: 6),
                                          TextWidget(
                                            text: '${user.battery}%',
                                            fontSize: 13,
                                            color: Colors.grey[600],
                                          ),
                                        ],
                                      ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.location_on,
                                size: 20,
                                color: Theme.of(context).primaryColor,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getBatteryIcon(int batteryLevel) {
    if (batteryLevel >= 75) return Icons.battery_full;
    if (batteryLevel >= 50) return Icons.battery_5_bar;
    if (batteryLevel >= 25) return Icons.battery_3_bar;
    return Icons.battery_1_bar;
  }

  Color _getBatteryColor(int batteryLevel) {
    if (batteryLevel >= 50) return Colors.green;
    if (batteryLevel >= 25) return Colors.orange;
    return Colors.red;
  }
}
