import 'package:family_management/domain/controllers/map_controller.dart';
import 'package:family_management/presentation/widget/back_button.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class DrawAreaView extends GetView<MapController> {
  const DrawAreaView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Directionality(
        textDirection: controller.appService.direction,
        child: Safe<PERSON>rea(
          child: GetBuilder<MapController>(builder: (controller) {
            return SizedBox(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              child: Stack(
                children: [
                  SizedBox(
                    height: MediaQuery.of(context).size.height,
                    width: MediaQuery.of(context).size.width,
                    child: GoogleMap(
                      onTap: controller.addPoint,
                      polygons: controller.polygons,
                      initialCameraPosition: controller.centerLocation,
                      onMapCreated: (mapController) =>
                          controller.complete(mapController),
                      myLocationButtonEnabled: true,
                      myLocationEnabled: true,
                    ),
                  ),
                  Positioned(top: 10, left: 20, child: CustomBackButton()),

                  // Professional animated hint at the top
                  Obx(() => controller.points.isEmpty
                      ? Positioned(
                          top: 80,
                          left: 20,
                          right: 20,
                          child: _buildAnimatedHint(context),
                        )
                      : const SizedBox.shrink()),

                  // Areas list button
                  Positioned(
                    top: 10,
                    right: 20,
                    child: Obx(() => GestureDetector(
                          onTap: () => _showAreasBottomSheet(context),
                          child: Container(
                            height: 50,
                            width: 50,
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Stack(
                              children: [
                                const Center(
                                  child: Icon(
                                    FontAwesomeIcons.list,
                                    color: Colors.blue,
                                    size: 20,
                                  ),
                                ),
                                if (controller.areas.isNotEmpty)
                                  Positioned(
                                    top: 8,
                                    right: 8,
                                    child: Container(
                                      padding: const EdgeInsets.all(2),
                                      decoration: BoxDecoration(
                                        color: Colors.red,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      constraints: const BoxConstraints(
                                        minWidth: 16,
                                        minHeight: 16,
                                      ),
                                      child: Text(
                                        '${controller.areas.length}',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        )),
                  ),
                  if (controller.points.isNotEmpty)
                    Positioned(
                        bottom: 75,
                        left: 15,
                        child: GestureDetector(
                          onTap: () => controller.showAreaNameDialog(),
                          child: Container(
                            height: 50,
                            width: 50,
                            decoration: BoxDecoration(
                                color:
                                    Theme.of(context).scaffoldBackgroundColor,
                                borderRadius: BorderRadius.circular(10)),
                            child: Center(
                              child: controller.saving
                                  ? const SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(),
                                    )
                                  : const Icon(
                                      FontAwesomeIcons.upload,
                                      color: Colors.red,
                                    ),
                            ),
                          ),
                        )),
                  Positioned(
                      bottom: 10,
                      left: 15,
                      child: GestureDetector(
                        onTap: () => controller.clearPoints(),
                        child: Container(
                          height: 50,
                          width: 50,
                          decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              borderRadius: BorderRadius.circular(10)),
                          child: const Center(
                            child: Icon(
                              FontAwesomeIcons.trashCan,
                              color: Colors.red,
                            ),
                          ),
                        ),
                      )),
                ],
              ),
            );
          }),
        )));
  }

  void _showAreasBottomSheet(BuildContext context) {
    // Refresh areas list when modal opens
    controller.getAreas();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 8),
              height: 4,
              width: 40,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    FontAwesomeIcons.mapLocation,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  TextWidget(
                    text: 'Existing Areas'.tr,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  const Spacer(),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color:
                          Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TextWidget(
                      text: '${controller.areas.length}',
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () => controller.getAreas(),
                    icon: Icon(
                      Icons.refresh,
                      color: Theme.of(context).primaryColor,
                      size: 20,
                    ),
                    tooltip: 'Refresh'.tr,
                  ),
                ],
              ),
            ),

            const Divider(height: 1),

            // Areas list
            Expanded(
              child: Obx(() {
                if (controller.loading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (controller.areas.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FontAwesomeIcons.mapLocation,
                          size: 48,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        TextWidget(
                          text: 'No areas found'.tr,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(height: 8),
                        TextWidget(
                          text:
                              'Start drawing on the map to create your first area'
                                  .tr,
                          fontSize: 14,
                          color: Colors.grey[500],
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: controller.areas.length,
                  itemBuilder: (context, index) {
                    final area = controller.areas[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      elevation: 1,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        leading: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Theme.of(context)
                                .primaryColor
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Icon(
                            FontAwesomeIcons.mapLocation,
                            color: Theme.of(context).primaryColor,
                            size: 16,
                          ),
                        ),
                        title: TextWidget(
                          text: area.name ?? 'Unnamed Area',
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.location_on_outlined,
                                  size: 12,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 4),
                                TextWidget(
                                  text: '${area.points.length} points'.tr,
                                  fontSize: 11,
                                  color: Colors.grey[600],
                                ),
                              ],
                            ),
                          ],
                        ),
                        trailing: controller.deleting
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              )
                            : IconButton(
                                onPressed: () {
                                  // Show delete confirmation dialog
                                  Get.dialog(AlertDialog(
                                    title: TextWidget(text: 'Delete Area'.tr),
                                    content: TextWidget(
                                      text:
                                          'Are you sure you want to delete "${area.name}"? This action cannot be undone.'
                                              .tr,
                                    ),
                                    actions: [
                                      TextButton(
                                        onPressed: () => Get.back(),
                                        child: TextWidget(
                                          text: 'Cancel'.tr,
                                          color: Colors.grey,
                                        ),
                                      ),
                                      TextButton(
                                        onPressed: () async {
                                          final currentContext = context;
                                          Get.back(); // Close confirmation dialog
                                          Navigator.pop(
                                              currentContext); // Close modal
                                          await controller.deleteArea(area.id!);
                                          // Reopen modal with updated list
                                          if (currentContext.mounted) {
                                            Future.delayed(
                                                const Duration(
                                                    milliseconds: 300), () {
                                              if (currentContext.mounted) {
                                                _showAreasBottomSheet(
                                                    currentContext);
                                              }
                                            });
                                          }
                                        },
                                        child: TextWidget(
                                          text: 'Delete'.tr,
                                          color: Colors.red,
                                        ),
                                      ),
                                    ],
                                  ));
                                },
                                icon: const Icon(
                                  Icons.delete_outline,
                                  color: Colors.red,
                                  size: 20,
                                ),
                                tooltip: 'Delete Area'.tr,
                              ),
                        onTap: () {
                          Navigator.pop(context);
                          // Focus on this area on the map
                          if (area.points.isNotEmpty) {
                            final bounds = _calculateBounds(area.points);
                            controller.mapController.animateCamera(
                              CameraUpdate.newLatLngBounds(bounds, 100),
                            );
                          }
                        },
                      ),
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  LatLngBounds _calculateBounds(List<LatLng> points) {
    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;

    for (final point in points) {
      minLat = minLat < point.latitude ? minLat : point.latitude;
      maxLat = maxLat > point.latitude ? maxLat : point.latitude;
      minLng = minLng < point.longitude ? minLng : point.longitude;
      maxLng = maxLng > point.longitude ? maxLng : point.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  Widget _buildAnimatedHint(BuildContext context) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 800),
      tween: Tween(begin: -100.0, end: 0.0),
      builder: (context, slideOffset, child) {
        return Transform.translate(
          offset: Offset(0, slideOffset),
          child: TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 1000),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, opacity, child) {
              return Opacity(
                opacity: opacity,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).primaryColor.withValues(alpha: 0.95),
                        Theme.of(context).primaryColor.withValues(alpha: 0.85),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.15),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                      BoxShadow(
                        color: Theme.of(context)
                            .primaryColor
                            .withValues(alpha: 0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 1500),
                        tween: Tween(begin: 0.0, end: 1.0),
                        builder: (context, opacity, child) {
                          return AnimatedContainer(
                            duration: const Duration(milliseconds: 800),
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color:
                                  Colors.white.withValues(alpha: 0.2 * opacity),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              FontAwesomeIcons.handPointer,
                              color: Colors.white,
                              size: 20,
                            ),
                          );
                        },
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            TweenAnimationBuilder<double>(
                              duration: const Duration(milliseconds: 1000),
                              tween: Tween(begin: 0.0, end: 1.0),
                              builder: (context, opacity, child) {
                                return Opacity(
                                  opacity: opacity,
                                  child: Material(
                                    elevation: 0,
                                    color: Colors.transparent,
                                    child: TextWidget(
                                      text: 'Start Drawing'.tr,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                );
                              },
                            ),
                            const SizedBox(height: 2),
                            TweenAnimationBuilder<double>(
                              duration: const Duration(milliseconds: 1200),
                              tween: Tween(begin: 0.0, end: 1.0),
                              builder: (context, opacity, child) {
                                return Opacity(
                                  opacity: opacity,
                                  child: Material(
                                    color: Colors.transparent,
                                    child: TextWidget(
                                      text:
                                          'Tap on the map to create points and draw your area'
                                              .tr,
                                      fontSize: 13,
                                      color:
                                          Colors.white.withValues(alpha: 0.9),
                                      maxLines: 2,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 1800),
                        tween: Tween(begin: 0.0, end: 1.0),
                        builder: (context, opacity, child) {
                          return AnimatedContainer(
                            duration: const Duration(milliseconds: 600),
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color:
                                  Colors.white.withValues(alpha: 0.2 * opacity),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: TweenAnimationBuilder<double>(
                              duration: const Duration(milliseconds: 2000),
                              tween: Tween(begin: 0.5, end: 1.0),
                              builder: (context, pulseScale, child) {
                                return Transform.scale(
                                  scale: pulseScale,
                                  child: const Icon(
                                    Icons.touch_app,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                );
                              },
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}
