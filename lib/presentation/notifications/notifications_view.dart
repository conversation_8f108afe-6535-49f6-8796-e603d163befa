import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/notifications_controller.dart';
import 'package:family_management/domain/models/notification.dart' as model;
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' hide TextDirection;

import '../widget/loading_indicator.dart';

class NotificationsView extends GetView<NotificationsController> {
  const NotificationsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          backgroundColor: const Color(0xFFF5F7FA),
          appBar: mainAppBar(
            context: context,
            title: "Notifications".tr,
            withDrawer: false,
            withBackButton: true,
            // actions: controller.notifications.isNotEmpty
            //     ? [
            //         IconButton(
            //           icon: const Icon(
            //             CupertinoIcons.trash,
            //             color: Colors.red,
            //             size: 22,
            //           ),
            //           onPressed: () => _showClearConfirmation(context),
            //           tooltip: 'Clear all notifications'.tr,
            //         ),
            //       ]
            //     : const [],
          ),
          body: RefreshIndicator(
            color: primarySwatch,
            onRefresh: () => controller.getNotifications(),
            child: controller.loading
                ? const Center(child: LoadingIndicator())
                : _buildNotificationsContent(context),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationsContent(BuildContext context) {
    if (controller.notifications.isEmpty) {
      return _buildEmptyState();
    }

    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextWidget(
                  text: 'Notifications'.tr,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                  color: Colors.grey[800],
                ),
                TextButton.icon(
                  onPressed: () {
                    // Mark all as read functionality
                  },
                  icon: const Icon(
                    CupertinoIcons.checkmark_circle,
                    size: 18,
                  ),
                  label: TextWidget(
                    text: 'Mark all as read'.tr,
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                  style: TextButton.styleFrom(
                    foregroundColor: primarySwatch,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: _buildDateGroups(context),
        ),
      ],
    );
  }

  Widget _buildDateGroups(BuildContext context) {
    // Group notifications by date
    final Map<String, List<model.Notification>> groupedNotifications = {};

    for (var notification in controller.notifications) {
      final date = notification.sentAt!;
      final dateString = _getDateGroupString(date);

      if (!groupedNotifications.containsKey(dateString)) {
        groupedNotifications[dateString] = [];
      }

      groupedNotifications[dateString]!.add(notification);
    }

    // Sort the dates with most recent first
    final sortedDates = groupedNotifications.keys.toList()
      ..sort((a, b) {
        if (a == 'Today') return -1;
        if (b == 'Today') return 1;
        if (a == 'Yesterday') return -1;
        if (b == 'Yesterday') return 1;
        return b.compareTo(a);
      });

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final dateString = sortedDates[index];
        final notifications = groupedNotifications[dateString]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Row(
                children: [
                  Container(
                    width: 4,
                    height: 16,
                    decoration: BoxDecoration(
                      color: primarySwatch,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 8),
                  TextWidget(
                    text: dateString,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                ],
              ),
            ),
            _buildNotificationsList(context, notifications),
          ],
        );
      },
    );
  }

  String _getDateGroupString(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final notificationDate = DateTime(date.year, date.month, date.day);

    if (notificationDate == today) {
      return 'Today';
    } else if (notificationDate == yesterday) {
      return 'Yesterday';
    } else {
      return DateFormat('MMMM d, yyyy').format(date);
    }
  }

  Widget _buildNotificationsList(
    BuildContext context,
    List<model.Notification> notifications,
  ) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        return _buildNotificationItem(context, notifications[index]);
      },
    );
  }

  Widget _buildNotificationItem(
      BuildContext context, model.Notification notification) {
    final sentDate = notification.sentAt!;
    final now = DateTime.now();
    final difference = now.difference(sentDate);
    final timeAgo = controller.formatTimeAgo(difference);

    // Determine if this is a new notification (less than 24 hours old)
    final isNew = difference.inHours < 24;

    return Dismissible(
      key: Key(notification.title! + notification.sentAt.toString()),
      background: Container(
        color: Colors.red[400],
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        child: const Icon(
          CupertinoIcons.delete,
          color: Colors.white,
        ),
      ),
      direction: DismissDirection.endToStart,
      onDismissed: (direction) {
        // Handle notification dismissal/deletion
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Notification removed'.tr),
            duration: const Duration(seconds: 2),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          color: isNew ? Colors.white : Colors.grey[50],
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(10),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              // Handle notification tap
            },
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Notification icon or indicator
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: _getNotificationColor(notification).withAlpha(30),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Icon(
                        _getNotificationIcon(notification),
                        color: _getNotificationColor(notification),
                        size: 20,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Notification content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: TextWidget(
                                text: notification.title ?? '',
                                fontWeight: FontWeight.bold,
                                fontSize: 15,
                                color: Colors.grey[800],
                              ),
                            ),
                            if (isNew)
                              Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  color: pink,
                                  shape: BoxShape.circle,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        TextWidget(
                          text: notification.body ?? '',
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Icon(
                              CupertinoIcons.clock,
                              size: 12,
                              color: Colors.grey[500],
                            ),
                            const SizedBox(width: 4),
                            TextWidget(
                              text: timeAgo.tr,
                              fontSize: 12,
                              color: Colors.grey[500],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  IconData _getNotificationIcon(model.Notification notification) {
    // Determine icon based on notification content
    final title = notification.title?.toLowerCase() ?? '';

    if (title.contains('task')) {
      return CupertinoIcons.checkmark_circle;
    } else if (title.contains('event')) {
      return CupertinoIcons.calendar;
    } else if (title.contains('message') || title.contains('chat')) {
      return CupertinoIcons.chat_bubble_2;
    } else if (title.contains('reminder')) {
      return CupertinoIcons.bell;
    } else {
      return CupertinoIcons.info_circle;
    }
  }

  Color _getNotificationColor(model.Notification notification) {
    // Determine color based on notification content
    final title = notification.title?.toLowerCase() ?? '';

    if (title.contains('task')) {
      return green;
    } else if (title.contains('event')) {
      return pink;
    } else if (title.contains('message') || title.contains('chat')) {
      return primarySwatch;
    } else if (title.contains('reminder')) {
      return yellow;
    } else {
      return Colors.grey;
    }
  }

  Widget _buildEmptyState() {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        const SizedBox(height: 80),
        SvgPicture.asset(
          'assets/svgs/no_notifications.svg',
          height: 180,
        ),
        const SizedBox(height: 24),
        Center(
          child: TextWidget(
            text: "You don't have any notifications yet".tr,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 12),
        Center(
          child: TextWidget(
            text: "We'll notify you when something important happens".tr,
            fontSize: 14,
            color: Colors.grey[600],
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 24),
        Center(
          child: ElevatedButton.icon(
            onPressed: () => controller.getNotifications(),
            icon: const Icon(CupertinoIcons.refresh),
            label: TextWidget(
              text: 'Refresh'.tr,
              color: Colors.white,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: primarySwatch,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _showClearConfirmation(BuildContext context) {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: TextWidget(
          text: 'Clear all notifications?'.tr,
          fontWeight: FontWeight.bold,
          fontSize: 18,
          textAlign: TextAlign.center,
        ),
        content: TextWidget(
          text: 'This action cannot be undone.'.tr,
          fontSize: 14,
          color: Colors.grey[700],
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: TextWidget(
              text: 'Cancel'.tr,
              color: Colors.grey[700],
            ),
          ),
          TextButton(
            onPressed: () {
              // Clear all notifications
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('All notifications cleared'.tr),
                  duration: const Duration(seconds: 2),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            child: TextWidget(
              text: 'Clear'.tr,
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
