import 'package:family_management/domain/controllers/home_controller.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class BottomBar extends GetView<HomeController> {
  const BottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(20),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: BottomAppBar(
            padding: EdgeInsets.zero,
            elevation: 0,
            color: Colors.white,
            height: 75,
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF134982), // primarySwatch
                    Color(0xFF1A5C9E), // slightly lighter
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildNavItem(
                    context: context,
                    index: 0,
                    icon: 'assets/svgs/home.svg',
                    label: 'Home'.tr,
                  ),
                  _buildNavItem(
                    context: context,
                    index: 1,
                    icon: 'assets/svgs/tasks.svg',
                    label: 'Tasks'.tr,
                  ),
                  _buildNavItem(
                    context: context,
                    index: 2,
                    icon: 'assets/svgs/chats.svg',
                    label: 'Chats'.tr,
                  ),
                  _buildNavItem(
                    context: context,
                    index: 3,
                    icon: 'assets/svgs/calendar.svg',
                    label: 'Calendar'.tr,
                  ),
                  _buildNavItem(
                    context: context,
                    index: 4,
                    icon: 'assets/svgs/contact.svg',
                    label: 'Profile'.tr,
                    iconSize: 28,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required int index,
    required String icon,
    required String label,
    double? iconSize,
  }) {
    final isActive = controller.activeIndex == index;

    return InkWell(
      onTap: () => controller.activeIndex = index,
      splashColor: Colors.white.withAlpha(50),
      highlightColor: Colors.white.withAlpha(30),
      borderRadius: BorderRadius.circular(16),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive ? Colors.white.withAlpha(20) : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              icon,
              height: iconSize ?? 24,
              width: iconSize ?? 24,
              colorFilter: ColorFilter.mode(
                isActive ? Colors.white : Colors.white.withAlpha(150),
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(height: 4),
            TextWidget(
              text: label,
              fontSize: 12,
              fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
              color: isActive ? Colors.white : Colors.white.withAlpha(150),
            ),
          ],
        ),
      ),
    );
  }
}
