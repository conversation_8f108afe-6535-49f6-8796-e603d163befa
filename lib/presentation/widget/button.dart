import 'package:flutter/material.dart';

class <PERSON><PERSON> extends StatelessWidget {
  final String text;
  final Function() onPressed;
  final bool? loading;
  final bool? disabled;
  final BorderRadius? radius;
  final double? fontSize;
  final Color? backgroundColor;
  final Color? textColor;
  final double? height;
  final double? width;
  final EdgeInsets? padding;
  const Button(
      {super.key,
      this.fontSize,
      this.height,
      this.width,
      this.padding,
      this.backgroundColor,
      this.textColor,
      required this.text,
      required this.onPressed,
      this.radius,
      this.disabled,
      this.loading});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: (loading ?? false) ? () => {} : onPressed,
        style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? Theme.of(context).primaryColor,
            padding: padding,
            shape: RoundedRectangleBorder(
                borderRadius: radius ?? BorderRadius.circular(25))),
        child: (loading ?? false)
            ? SizedBox(
                height: 25,
                width: 25,
                child: Center(
                  child: CircularProgressIndicator(
                    color: textColor ?? Colors.white,
                    strokeWidth: 3,
                  ),
                ),
              )
            : Text(
                text,
                style: TextStyle(
                  color: textColor ?? Colors.white,
                  fontSize: fontSize ?? 17,
                ),
              ),
      ),
    );
  }
}
