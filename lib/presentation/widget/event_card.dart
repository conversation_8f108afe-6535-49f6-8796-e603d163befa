import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/models/event.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class EventCard extends StatelessWidget {
  final Event event;
  final double? width;
  final Function(Event event)? onPressed;
  const EventCard({super.key, required this.event, this.width, this.onPressed});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (onPressed != null) {
          onPressed!(event);
        }
      },
      borderRadius: BorderRadius.circular(16),
      child: Padding(
        padding: const EdgeInsets.all(3.0),
        child: Material(
          elevation: 2,
          borderRadius: BorderRadius.circular(10),
          child: Container(
            width: width, // Use the provided width if available
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IntrinsicHeight(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Left color indicator
                  Container(
                    width: 6,
                    decoration: BoxDecoration(
                      color: event.isPassed ? green : pink,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        bottomLeft: Radius.circular(16),
                      ),
                    ),
                  ),
                  // Event content
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Event name and time
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Event name
                              Expanded(
                                child: TextWidget(
                                  text: event.name ?? '',
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  color: Colors.grey[800],
                                ),
                              ),
                              // Event time
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.grey[100],
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      CupertinoIcons.clock,
                                      size: 12,
                                      color: Colors.grey[600],
                                    ),
                                    const SizedBox(width: 4),
                                    TextWidget(
                                      text: intl.DateFormat('HH:mm')
                                          .format(event.scheduledAt!),
                                      color: Colors.grey[600],
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),

                          // Event description
                          TextWidget(
                            text: event.description ?? '',
                            color: Colors.grey[600],
                            fontSize: 14,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),

                          const SizedBox(height: 12),

                          // Event status and date
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Status badge
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  color: event.isPassed
                                      ? green.withAlpha(30)
                                      : Colors.grey[200],
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      event.isPassed
                                          ? CupertinoIcons.check_mark_circled
                                          : CupertinoIcons.time,
                                      size: 14,
                                      color: event.isPassed
                                          ? green
                                          : Colors.grey[700],
                                    ),
                                    const SizedBox(width: 4),
                                    TextWidget(
                                      text: event.isPassed
                                          ? 'Done'.tr
                                          : 'Pending'.tr,
                                      color: event.isPassed
                                          ? green
                                          : Colors.grey[700],
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ],
                                ),
                              ),

                              // Date
                              TextWidget(
                                text: intl.DateFormat('EE, d MMM')
                                    .format(event.scheduledAt!),
                                color: Colors.grey[500],
                                fontSize: 12,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
