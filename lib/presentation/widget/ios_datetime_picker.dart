import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class IOSDateTimePicker extends StatelessWidget {
  final Widget? prefixIcon;
  final String? label;
  final String? error;
  final double? width;
  final double? height;
  final Color? borderColor;
  final Color? textColor;
  final EdgeInsets? padding;
  final Color? fillColor;
  final BorderRadius? radius;
  final String? value;
  final Function(DateTime? value)? onChange;
  final CupertinoDatePickerMode mode;
  
  const IOSDateTimePicker({
    super.key,
    this.label,
    this.prefixIcon,
    this.error,
    this.width,
    this.borderColor,
    this.textColor,
    this.value,
    this.padding,
    this.fillColor,
    this.height,
    this.onChange,
    this.radius,
    this.mode = CupertinoDatePickerMode.dateAndTime,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () => _showIOSDateTimePicker(context),
          child: Container(
            width: width ?? MediaQuery.of(context).size.width,
            height: height,
            padding: padding ??
                const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
            decoration: BoxDecoration(
              color: fillColor,
              borderRadius: radius ?? BorderRadius.circular(35),
              border: Border.all(
                color: error != null && error!.isNotEmpty
                    ? Colors.red
                    : (borderColor ?? Theme.of(context).primaryColor),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                if (prefixIcon != null)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 10),
                    child: prefixIcon,
                  ),
                SizedBox(
                  width: width != null
                      ? width! - 22
                      : MediaQuery.of(context).size.width - 150,
                  child: TextWidget(
                    text: value != null && value!.isNotEmpty
                        ? value!
                        : (label ?? ''),
                    color: textColor ?? Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ),
        if (error != null && error!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Row(
              children: [
                const Icon(
                  CupertinoIcons.info,
                  color: Colors.red,
                  size: 17,
                ),
                const SizedBox(width: 5),
                Text(
                  '$error',
                  style: const TextStyle(color: Colors.red, fontSize: 12),
                ),
              ],
            ),
          )
      ],
    );
  }

  void _showIOSDateTimePicker(BuildContext context) {
    DateTime selectedDateTime = DateTime.now();
    
    // Parse current value if exists
    if (value != null && value!.isNotEmpty) {
      try {
        selectedDateTime = DateTime.parse(value!);
      } catch (e) {
        selectedDateTime = DateTime.now();
      }
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: 400,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 8),
              height: 4,
              width: 40,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Header with Done button
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'Cancel'.tr,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                  ),
                  Text(
                    _getTitle(),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      if (onChange != null) {
                        onChange!(selectedDateTime);
                      }
                    },
                    child: Text(
                      'Done'.tr,
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            const Divider(height: 1),
            
            // iOS-style date/time picker
            Expanded(
              child: CupertinoDatePicker(
                mode: mode,
                initialDateTime: selectedDateTime,
                minimumDate: mode == CupertinoDatePickerMode.time 
                    ? null 
                    : DateTime.now().subtract(const Duration(days: 365)),
                maximumDate: mode == CupertinoDatePickerMode.time 
                    ? null 
                    : DateTime.now().add(const Duration(days: 365 * 2)),
                onDateTimeChanged: (DateTime newDateTime) {
                  selectedDateTime = newDateTime;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getTitle() {
    switch (mode) {
      case CupertinoDatePickerMode.date:
        return 'Select Date'.tr;
      case CupertinoDatePickerMode.time:
        return 'Select Time'.tr;
      case CupertinoDatePickerMode.dateAndTime:
        return 'Select Date & Time'.tr;
      default:
        return 'Select'.tr;
    }
  }
}
