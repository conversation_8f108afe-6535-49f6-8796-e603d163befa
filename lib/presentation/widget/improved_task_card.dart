import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/enums/task_status.dart';
import 'package:family_management/domain/models/task.dart';
import 'package:family_management/presentation/widget/avatar_image.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;
import 'package:percent_indicator/linear_percent_indicator.dart';

class ImprovedTaskCard extends StatelessWidget {
  final Task task;
  final Function(Task task)? onPressed;
  final bool showAssignees;
  final int? currentUserId;

  const ImprovedTaskCard({
    super.key,
    required this.task,
    this.onPressed,
    this.showAssignees = true,
    this.currentUserId,
  });

  @override
  Widget build(BuildContext context) {
    final dueDate = task.dueDate;
    final isOverdue = dueDate != null &&
        dueDate.isBefore(DateTime.now()) &&
        task.taskStatus != TaskStatus.done;
    final completedSubtasks =
        task.subTasks?.where((e) => e.status == "done").length ?? 0;
    final totalSubtasks = task.subTasks?.length ?? 0;
    final hasSubtasks = totalSubtasks > 0;
    final progress = hasSubtasks ? completedSubtasks / totalSubtasks : 0.0;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isOverdue
              ? Colors.red.withValues(alpha: 128)
              : Colors.grey.withValues(alpha: 26),
          width: isOverdue ? 1.5 : 0.5,
        ),
      ),
      child: InkWell(
        onTap: () {
          if (onPressed != null) {
            onPressed!(task);
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with status badge and due date
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Status badge
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                      color: task.getBadgeColor(),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: TextWidget(
                      text: task.taskStatus.toString(),
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  // Due date with icon
                  if (dueDate != null)
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 14,
                          color: isOverdue ? Colors.red : Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        TextWidget(
                          text: intl.DateFormat('MMM d, y').format(dueDate),
                          fontSize: 12,
                          color: isOverdue ? Colors.red : Colors.grey[600],
                          fontWeight:
                              isOverdue ? FontWeight.bold : FontWeight.normal,
                        ),
                      ],
                    ),
                ],
              ),

              const SizedBox(height: 12),

              // Task title
              TextWidget(
                text: task.title ?? '',
                fontWeight: FontWeight.bold,
                fontSize: 16,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              // Task description (if available)
              if (task.description != null && task.description!.isNotEmpty) ...[
                const SizedBox(height: 8),
                TextWidget(
                  text: task.description ?? '',
                  fontSize: 14,
                  color: Colors.grey[700],
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              // Progress indicator for subtasks
              if (hasSubtasks) ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    Icon(
                      Icons.checklist,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 8),
                    TextWidget(
                      text:
                          '$completedSubtasks/$totalSubtasks ${'subtasks'.tr}',
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearPercentIndicator(
                  lineHeight: 6,
                  percent: progress,
                  progressColor: _getProgressColor(progress),
                  backgroundColor: Colors.grey[200],
                  barRadius: const Radius.circular(3),
                  padding: EdgeInsets.zero,
                  animation: true,
                ),
              ],

              // Footer with creator and assignees
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Creator info
                  if (task.createdBy != null)
                    Expanded(
                      child: Row(
                        children: [
                          // Show a different icon if current user is the creator
                          Icon(
                            currentUserId != null &&
                                    task.createdBy?.id == currentUserId
                                ? Icons.person
                                : Icons.person_outline,
                            size: 14,
                            color: currentUserId != null &&
                                    task.createdBy?.id == currentUserId
                                ? primarySwatch
                                : Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: TextWidget(
                              text: currentUserId != null &&
                                      task.createdBy?.id == currentUserId
                                  ? 'Created by you'.tr
                                  : '${'Created by'.tr}: ${task.createdBy?.name ?? ''}',
                              fontSize: 12,
                              color: currentUserId != null &&
                                      task.createdBy?.id == currentUserId
                                  ? primarySwatch
                                  : Colors.grey[600],
                              fontWeight: currentUserId != null &&
                                      task.createdBy?.id == currentUserId
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Assignees
                  if (showAssignees &&
                      (task.assignees?.isNotEmpty ?? false)) ...[
                    const SizedBox(width: 8),
                    _buildAssignees(),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAssignees() {
    final assignees = task.assignees ?? [];
    const maxDisplayed = 3;

    return Row(
      children: [
        ...assignees.take(maxDisplayed).map((user) => Padding(
              padding: const EdgeInsets.only(right: 4),
              child: Tooltip(
                message: user.name ?? '',
                child: AvatarImage(
                  url: user.avatar,
                  width: 24,
                  height: 24,
                ),
              ),
            )),
        if (assignees.length > maxDisplayed)
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              shape: BoxShape.circle,
            ),
            child: Center(
              child: TextWidget(
                text: '+${assignees.length - maxDisplayed}',
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
          ),
      ],
    );
  }

  Color _getProgressColor(double progress) {
    if (progress >= 1.0) return Colors.green;
    if (progress >= 0.5) return Colors.orange;
    return Colors.blue;
  }
}
