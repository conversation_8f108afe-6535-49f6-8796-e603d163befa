import 'package:family_management/domain/models/user.dart';
import 'package:family_management/presentation/widget/avatar_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class AssigneesWidget extends StatelessWidget {
  final List<User> users;
  const AssigneesWidget({super.key, required this.users});

  @override
  Widget build(BuildContext context) {
    return Stack(
        children: users
            .map((user) => Positioned(
                  left: users.indexWhere((e) => e.id == user.id) * 20,
                  child: SizedBox(
                    height: 32,
                    width: 32,
                    child: AvatarImage(url: user.avatar),
                  ),
                ))
            .toList());
  }
}
