import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TextInputWithSuggestions<T> extends StatelessWidget {
  final String? hint;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextEditingController? controller;
  final Function(String)? onChange;
  final String? error;
  final double? width;
  final double? height;
  final BorderRadius? radius;
  final int? maxLength;
  final TextAlign? textAlign;
  final FocusNode? focusNode;
  final bool? autoFocus;
  final TextStyle? style;
  final String? counterText;
  final int? minLines;
  final int? maxLines;
  final Color? fillColor;
  final Color? borderColor;
  final Color? textColor;
  final Function(String value)? onSubmitted;
  final EdgeInsets? padding;

  // Suggestion-specific properties
  final List<T> suggestions;
  final bool showSuggestions;
  final String Function(T) suggestionBuilder;
  final Function(T) onSuggestionSelected;
  final VoidCallback? onSuggestionsDismissed;
  final IconData? suggestionIcon;
  final Widget Function(T)? customSuggestionBuilder;

  const TextInputWithSuggestions({
    super.key,
    this.error,
    this.hint,
    this.onSubmitted,
    this.padding,
    this.textColor,
    this.borderColor,
    this.fillColor,
    this.minLines,
    this.maxLines = 1,
    this.prefixIcon,
    this.controller,
    this.onChange,
    this.style,
    this.width,
    this.height,
    this.textAlign,
    this.focusNode,
    this.radius,
    this.maxLength,
    this.autoFocus,
    this.suffixIcon,
    this.counterText,
    required this.suggestions,
    required this.showSuggestions,
    required this.suggestionBuilder,
    required this.onSuggestionSelected,
    this.onSuggestionsDismissed,
    this.suggestionIcon,
    this.customSuggestionBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Dismiss suggestions when tapping outside
        if (showSuggestions && onSuggestionsDismissed != null) {
          onSuggestionsDismissed!();
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: width ?? MediaQuery.of(context).size.width,
            height: height,
            padding: padding ??
                const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
            decoration: BoxDecoration(
              color: fillColor,
              borderRadius: radius ?? BorderRadius.circular(35),
              border: Border.all(
                  color: error != null && error!.isNotEmpty
                      ? Colors.red
                      : (borderColor ?? Theme.of(context).primaryColor)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                if (prefixIcon != null)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 10),
                    child: prefixIcon,
                  ),
                Expanded(
                  child: TextField(
                    onSubmitted: onSubmitted,
                    minLines: minLines,
                    maxLines: maxLines,
                    focusNode: focusNode,
                    autofocus: autoFocus ?? false,
                    maxLength: maxLength,
                    textAlign: textAlign ?? TextAlign.start,
                    controller: controller,
                    onChanged: onChange,
                    style: style,
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 0, vertical: 10),
                      hintText: hint,
                      counterText: counterText,
                      hintStyle: TextStyle(
                          color: textColor ?? Colors.grey, fontSize: 15),
                      border: InputBorder.none,
                    ),
                  ),
                ),
                if (suffixIcon != null)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 10),
                    child: suffixIcon,
                  ),
              ],
            ),
          ),

          // Suggestions dropdown
          if (showSuggestions && suggestions.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(top: 5),
              decoration: BoxDecoration(
                color: Get.theme.cardColor,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              constraints: const BoxConstraints(maxHeight: 200),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: suggestions.length,
                itemBuilder: (context, index) {
                  final suggestion = suggestions[index];
                  return InkWell(
                    onTap: () => onSuggestionSelected(suggestion),
                    child: customSuggestionBuilder != null
                        ? customSuggestionBuilder!(suggestion)
                        : Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            child: Row(
                              children: [
                                Icon(
                                  suggestionIcon ?? Icons.history,
                                  size: 16,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    suggestionBuilder(suggestion),
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Get.theme.primaryColor,
                                    ),
                                  ),
                                ),
                                Icon(
                                  Icons.arrow_forward_ios,
                                  size: 12,
                                  color: Colors.grey[400],
                                ),
                              ],
                            ),
                          ),
                  );
                },
              ),
            ),

          if (error != null && error!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              child: Row(
                children: [
                  const Icon(
                    CupertinoIcons.info,
                    color: Colors.red,
                    size: 17,
                  ),
                  const SizedBox(width: 5),
                  Text(
                    '$error',
                    style: const TextStyle(color: Colors.red, fontSize: 12),
                  ),
                ],
              ),
            )
        ],
      ),
    );
  }
}
