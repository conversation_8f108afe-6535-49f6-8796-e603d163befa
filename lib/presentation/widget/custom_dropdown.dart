import 'package:flutter/material.dart';

class CustomDropdown extends StatelessWidget {
  final Function(String?)? onChanged;
  final String? hint;
  final String? value;
  final List<DropdownMenuItem<String>> items;
  final InputBorder? border;
  const CustomDropdown(
      {super.key,
      this.onChanged,
      this.border,
      required this.items,
      this.hint,
      this.value});

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
        value: value,
        decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.grey),
            enabledBorder: border ??
                OutlineInputBorder(
                    borderRadius: BorderRadius.circular(28),
                    borderSide: BorderSide(
                        width: 1, color: Theme.of(context).primaryColor)),
            border: border ??
                OutlineInputBorder(
                    borderRadius: BorderRadius.circular(28),
                    borderSide: BorderSide(
                        width: 1, color: Theme.of(context).primaryColor)),
            focusedBorder: border ??
                OutlineInputBorder(
                    borderRadius: BorderRadius.circular(28),
                    borderSide: BorderSide(
                        width: 1, color: Theme.of(context).primaryColor))),
        items: items,
        onChanged: onChanged);
  }
}
