import 'package:family_management/app_service.dart';
import 'package:family_management/config/themes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CustomBackButton extends StatelessWidget {
  final appService = Get.find<AppService>();
  CustomBackButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 35,
      width: 35,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(width: 1, color: grey)),
      child: ElevatedButton(
        onPressed: () => Get.back(),
        style: ElevatedButton.styleFrom(
            elevation: 0,
            padding: EdgeInsets.zero,
            backgroundColor: Colors.white),
        child: Icon(
          size: 20,
          appService.locale == const Locale('ar')
              ? Icons.chevron_right
              : Icons.chevron_left,
        ),
      ),
    );
  }
}
