import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class DatePicker extends StatelessWidget {
  final Widget? prefixIcon;
  final String? label;
  final String? error;
  final double? width;
  final double? height;
  final Color? borderColor;
  final Color? textColor;
  final EdgeInsets? padding;
  final Color? fillColor;
  final BorderRadius? radius;
  final String? value;
  final Function(DateTime? value)? onChange;
  const DatePicker(
      {super.key,
      this.label,
      this.prefixIcon,
      this.error,
      this.width,
      this.borderColor,
      this.textColor,
      this.value,
      this.padding,
      this.fillColor,
      this.height,
      this.onChange,
      this.radius});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () => _showIOSDatePicker(context),
          child: Container(
              width: width ?? MediaQuery.of(context).size.width,
              height: height,
              padding: padding ??
                  const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
              decoration: BoxDecoration(
                color: fillColor,
                borderRadius: radius ?? BorderRadius.circular(35),
                border: Border.all(
                    color: error != null && error!.isNotEmpty
                        ? Colors.red
                        : (borderColor ?? Theme.of(context).primaryColor)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  if (prefixIcon != null)
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 10),
                      child: prefixIcon,
                    ),
                  SizedBox(
                    width: width != null
                        ? width! - 22
                        : MediaQuery.of(context).size.width - 150,
                    child: TextWidget(
                      text: value != null && value!.isNotEmpty
                          ? _formatDisplayDate(value!)
                          : (label ?? ''),
                      color: textColor ?? Colors.grey,
                    ),
                  ),
                ],
              )),
        ),
        if (error != null && error!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Row(
              children: [
                const Icon(
                  CupertinoIcons.info,
                  color: Colors.red,
                  size: 17,
                ),
                const SizedBox(
                  width: 5,
                ),
                Text(
                  '$error',
                  style: const TextStyle(color: Colors.red, fontSize: 12),
                ),
              ],
            ),
          )
      ],
    );
  }

  void _showIOSDatePicker(BuildContext context) {
    DateTime selectedDate = DateTime.now();

    // Parse current value if exists
    if (value != null && value!.isNotEmpty) {
      try {
        selectedDate = DateTime.parse(value!);
      } catch (e) {
        selectedDate = DateTime.now();
      }
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 350,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 8),
              height: 4,
              width: 40,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header with Done button
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'Cancel'.tr,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                  ),
                  Text(
                    'Select Date'.tr,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      if (onChange != null) {
                        onChange!(selectedDate);
                      }
                    },
                    child: Text(
                      'Done'.tr,
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const Divider(height: 1),

            // iOS-style date picker
            Expanded(
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.date,
                initialDateTime: selectedDate,
                minimumDate: DateTime.now().subtract(const Duration(days: 365)),
                maximumDate: DateTime.now().add(const Duration(days: 365 * 2)),
                onDateTimeChanged: (DateTime newDate) {
                  selectedDate = newDate;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDisplayDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return intl.DateFormat('MMM dd, yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }
}
