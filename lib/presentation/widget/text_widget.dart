import 'package:flutter/material.dart';

class TextWidget extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  final TextDecoration? decoration;
  const TextWidget(
      {super.key,
      required this.text,
      this.fontWeight,
      this.color,
      this.maxLines,
      this.overflow,
      this.textAlign,
      this.fontSize,
      this.decoration,
      this.style});

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
      style: TextStyle(
          color: color ?? Theme.of(context).primaryColor,
          fontSize: fontSize,
          fontWeight: fontWeight,
          decoration: decoration),
    );
  }
}
