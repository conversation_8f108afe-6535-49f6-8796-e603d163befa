import 'package:flutter/material.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

class MultiSelect extends StatelessWidget {
  final List<DropdownItem<String>> items;
  final MultiSelectController<String> controller;
  final String? hint;
  const MultiSelect(
      {super.key, required this.items, required this.controller, this.hint});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: MultiDropdown<String>(
        items: items,
        controller: controller,
        enabled: true,
        searchEnabled: true,
        chipDecoration: ChipDecoration(
          backgroundColor: Theme.of(context).primaryColor,
          wrap: true,
          runSpacing: 2,
          labelStyle: const TextStyle(color: Colors.white),
          spacing: 10,
        ),
        fieldDecoration: FieldDecoration(
          hintStyle: const TextStyle(color: Colors.grey),
          hintText: hint,
          border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(25),
              borderSide: BorderSide(color: Theme.of(context).primaryColor)),
        ),
      ),
    );
  }
}
