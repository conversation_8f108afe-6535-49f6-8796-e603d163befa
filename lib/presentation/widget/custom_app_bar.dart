import 'package:flutter/material.dart';

class CustomAppBar extends StatelessWidget {
  final String title;
  final Color titleColor; // إمكانية تخصيص لون النص

  const CustomAppBar({
    super.key,
    required this.title,
    this.titleColor = const Color(0xFF1C2D4B), // لون افتراضي للنص
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        bottomLeft: Radius.circular(30),
        bottomRight: Radius.circular(30),
      ),
      child: Container(
        width: double.infinity, // يمتد بعرض الشاشة بالكامل
        height: 160,
        color: Theme.of(context).primaryColor,
        child: Safe<PERSON><PERSON>(
          child: Stack(
            alignment: Alignment.center,
            children: [
              // العنوان
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: titleColor, // استخدام اللون القادم من المعاملات
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
