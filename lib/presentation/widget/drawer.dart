import 'package:family_management/app_service.dart';
import 'package:family_management/config/app_routes.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/auth_check_controller.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

class MainDrawer extends StatelessWidget {
  const MainDrawer({super.key});

  /// Show logout confirmation dialog
  void _showLogoutConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(
                Icons.logout,
                color: Colors.red[600],
                size: 24,
              ),
              const SizedBox(width: 12),
              TextWidget(
                text: 'Logout'.tr,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextWidget(
                text: 'Are you sure you want to logout?'.tr,
                fontSize: 16,
                color: Colors.grey[700],
              ),
              const SizedBox(height: 8),
              TextWidget(
                text: 'You will need to login again to access your account.'.tr,
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ],
          ),
          actions: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Cancel Button
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(color: Colors.grey[300]!),
                      ),
                    ),
                    child: TextWidget(
                      text: 'Cancel'.tr,
                      color: Colors.grey[700],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // Logout Button
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _performLogout(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    child: TextWidget(
                      text: 'Logout'.tr,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  /// Perform the actual logout
  void _performLogout(BuildContext context) async {
    try {
      // Close the confirmation dialog
      Navigator.of(context).pop();

      // Close the drawer
      Navigator.of(context).pop();

      // Show loading indicator
      Get.dialog(
        AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              TextWidget(
                text: 'Logging out...'.tr,
                fontSize: 16,
                color: Colors.grey[700],
              ),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      // Add a small delay for better UX
      await Future.delayed(const Duration(milliseconds: 800));

      // Close loading dialog
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // Try to get the AuthCheckController, if it exists use it, otherwise use AppService
      try {
        final authController = Get.find<AuthCheckController>();
        await authController.logout();
      } catch (e) {
        // Fallback to AppService if AuthCheckController is not available
        final appService = Get.find<AppService>();
        appService.logout();
      }
    } catch (e) {
      // Close any open dialogs
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // Show error message
      Get.snackbar(
        'Error'.tr,
        'Failed to logout. Please try again.'.tr,
        backgroundColor: Colors.red[100],
        colorText: Colors.red[800],
        snackPosition: SnackPosition.TOP,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
        duration: const Duration(seconds: 3),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      child: Container(
        height: MediaQuery.of(context).size.height,
        margin: const EdgeInsets.symmetric(vertical: 50, horizontal: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            ListTile(
              leading: Image.asset('assets/images/logo.png'),
              title: const TextWidget(
                text: 'Family360',
                fontSize: 17,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 0),
              child: Divider(
                color: Colors.grey[300],
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: SvgPicture.asset('assets/svgs/drawer/meals.svg',
                  colorFilter: ColorFilter.mode(
                      Theme.of(context).primaryColor, BlendMode.srcIn)),
              title: TextWidget(
                text: 'Shopping list'.tr,
                fontSize: 14,
              ),
              iconColor: Theme.of(context).primaryColor,
              onTap: () => Get.toNamed(AppRoutes.shoppingAndMeal),
            ),
            ListTile(
              leading: SvgPicture.asset('assets/svgs/drawer/gallery.svg',
                  colorFilter: ColorFilter.mode(
                      Theme.of(context).primaryColor, BlendMode.srcIn)),
              title: TextWidget(
                text: 'Our gallery'.tr,
                fontSize: 14,
              ),
              iconColor: Theme.of(context).primaryColor,
              onTap: () => Get.toNamed(AppRoutes.gallery),
            ),
            ListTile(
              leading: SvgPicture.asset('assets/svgs/polls.svg',
                  width: 20,
                  height: 20,
                  colorFilter: ColorFilter.mode(
                      Theme.of(context).primaryColor, BlendMode.srcIn)),
              title: TextWidget(
                text: 'Polls'.tr,
                fontSize: 14,
              ),
              iconColor: Theme.of(context).primaryColor,
              onTap: () => Get.toNamed(AppRoutes.polls),
            ),
            ListTile(
              leading: Icon(
                FontAwesomeIcons.mapLocation,
                color: Theme.of(context).primaryColor,
              ),
              title: TextWidget(
                text: 'Location tracking'.tr,
                fontSize: 14,
              ),
              iconColor: Theme.of(context).primaryColor,
              onTap: () => Get.toNamed(AppRoutes.map),
            ),
            ListTile(
              leading: Icon(
                FontAwesomeIcons.mapLocation,
                color: Theme.of(context).primaryColor,
              ),
              title: TextWidget(
                text: 'Manage areas'.tr,
                fontSize: 14,
              ),
              iconColor: Theme.of(context).primaryColor,
              onTap: () => Get.toNamed(AppRoutes.addArea),
            ),
            ListTile(
              leading: const Icon(Icons.attach_money_outlined),
              title: TextWidget(
                text: 'Budget Tracking'.tr,
                fontSize: 14,
              ),
              iconColor: Theme.of(context).primaryColor,
              onTap: () => Get.toNamed(AppRoutes.budgetTracking),
            ),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 25),
              child: Divider(
                color: pink,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: ListTile(
                leading: const Icon(
                  Icons.logout,
                  color: Colors.red,
                ),
                title: TextWidget(
                  text: 'Log out'.tr,
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
                onTap: () => _showLogoutConfirmation(context),
                contentPadding: const EdgeInsets.symmetric(horizontal: 0),
              ),
            )
          ],
        ),
      ),
    );
  }
}
