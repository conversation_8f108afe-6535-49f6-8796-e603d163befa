import 'package:family_management/domain/models/task.dart';
import 'package:family_management/presentation/widget/assignees_widget.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;
import 'package:percent_indicator/linear_percent_indicator.dart';

class TaskCard extends StatelessWidget {
  final Task task;
  final Function(Task task)? onPressed;
  const TaskCard({super.key, required this.task, this.onPressed});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (onPressed != null) {
          onPressed!(task);
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(5.0),
        child: Material(
          elevation: 3,
          borderRadius: BorderRadius.circular(15),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            child: Material(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextWidget(
                        text: task.dueDate != null
                            ? intl.DateFormat('dd / MM / y')
                                .format(task.dueDate!)
                            : '',
                        fontSize: 12,
                      ),
                    ],
                  ),
                  TextWidget(
                    text: '${task.title}',
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                        color: task.getBadgeColor(),
                        borderRadius: BorderRadius.circular(25)),
                    child: TextWidget(
                      text: task.taskStatus.toString(),
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 5),
                  TextWidget(
                    text: '${'Created by'.tr}: ${task.createdBy?.name}',
                    color: Colors.grey,
                    fontSize: 12,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (task.subTasks != null && task.subTasks!.isNotEmpty) ...[
                    const SizedBox(height: 10),
                    SizedBox(
                      width: 150,
                      child: LinearPercentIndicator(
                        barRadius: const Radius.circular(10),
                        progressColor: Theme.of(context).primaryColor,
                        percent: task.subTasks!
                                .where((e) => e.status == "done")
                                .length /
                            task.subTasks!.length,
                      ),
                    ),
                  ],
                  const SizedBox(height: 20),
                  SizedBox(
                      width: MediaQuery.of(context).size.width,
                      height: 40,
                      child: AssigneesWidget(users: task.assignees ?? []))
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
