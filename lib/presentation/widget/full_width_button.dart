import 'package:flutter/material.dart';

class FullWidthButton extends StatelessWidget {
  final String text;
  final Function() onPressed;
  final bool? loading;
  final bool? disabled;
  final BorderRadius? radius;
  const FullWidthButton(
      {super.key,
      required this.text,
      required this.onPressed,
      this.radius,
      this.disabled,
      this.loading});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: ElevatedButton(
        onPressed: (loading ?? false) ? () => {} : onPressed,
        style: ElevatedButton.styleFrom(
            backgroundColor: (disabled ?? false)
                ? Colors.grey
                : Theme.of(context).primaryColor,
            padding: const EdgeInsets.symmetric(vertical: 15),
            shape: RoundedRectangleBorder(
                borderRadius: radius ?? BorderRadius.circular(25))),
        child: (loading ?? false)
            ? const SizedBox(
                height: 25,
                width: 25,
                child: Center(
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 3,
                  ),
                ),
              )
            : Text(
                text,
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 17,
                    fontWeight: FontWeight.bold),
              ),
      ),
    );
  }
}
