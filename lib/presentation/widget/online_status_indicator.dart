import 'package:family_management/domain/models/user.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// A widget that displays a user's online status
/// 
/// This can be used in various places throughout the app to show
/// whether a user is online or offline, along with their last seen time.
class OnlineStatusIndicator extends StatelessWidget {
  final User user;
  final bool showText;
  final bool showDot;
  final double dotSize;
  final TextStyle? textStyle;
  final bool alignLeft;

  const OnlineStatusIndicator({
    Key? key,
    required this.user,
    this.showText = true,
    this.showDot = true,
    this.dotSize = 8.0,
    this.textStyle,
    this.alignLeft = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showDot) ...[
          Container(
            width: dotSize,
            height: dotSize,
            decoration: BoxDecoration(
              color: user.isOnline ? Colors.green : Colors.grey,
              shape: BoxShape.circle,
            ),
          ),
          if (showText) const SizedBox(width: 4),
        ],
        if (showText)
          Flexible(
            child: TextWidget(
              text: user.isOnline ? 'Online'.tr : user.getLastSeenText().tr,
              fontSize: 12,
              color: user.isOnline ? Colors.green : Colors.grey[600],
              textAlign: alignLeft ? TextAlign.left : TextAlign.right,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: textStyle,
            ),
          ),
      ],
    );
  }
}

/// A widget that displays a user's avatar with an online status indicator
class UserAvatarWithStatus extends StatelessWidget {
  final User user;
  final double avatarSize;
  final double indicatorSize;
  final bool showStatusText;
  final VoidCallback? onTap;

  const UserAvatarWithStatus({
    Key? key,
    required this.user,
    this.avatarSize = 40.0,
    this.indicatorSize = 10.0,
    this.showStatusText = false,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(avatarSize / 2),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              // User avatar
              CircleAvatar(
                radius: avatarSize / 2,
                backgroundColor: Colors.grey[300],
                backgroundImage: user.avatar != null
                    ? NetworkImage(user.avatar!)
                    : null,
                child: user.avatar == null
                    ? Icon(
                        Icons.person,
                        size: avatarSize / 2,
                        color: Colors.grey[600],
                      )
                    : null,
              ),
              
              // Online status indicator
              Positioned(
                right: 0,
                bottom: 0,
                child: Container(
                  width: indicatorSize,
                  height: indicatorSize,
                  decoration: BoxDecoration(
                    color: user.isOnline ? Colors.green : Colors.grey,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 1.5,
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          // Status text (optional)
          if (showStatusText) ...[
            const SizedBox(height: 4),
            OnlineStatusIndicator(
              user: user,
              showDot: false,
              textStyle: const TextStyle(fontSize: 10),
            ),
          ],
        ],
      ),
    );
  }
}
