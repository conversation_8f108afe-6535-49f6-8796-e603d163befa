import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class TextInput extends StatelessWidget {
  final String? hint;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextEditingController? controller;
  final Function(String)? onChange;
  final bool? isPassword;
  final TextInputAction? textInputAction;
  final String? error;
  final double? width;
  final double? height;
  final BorderRadius? radius;
  final int? maxLength;
  final TextAlign? textAlign;
  final FocusNode? focusNode;
  final bool? autoFocus;
  final TextStyle? style;
  final String? counterText;
  final int? minLines;
  final int? maxLines;
  final Color? fillColor;
  final Color? borderColor;
  final Color? textColor;
  final Function(String value)? onSubmitted;
  final EdgeInsets? padding;
  const TextInput(
      {super.key,
      this.error,
      this.hint,
      this.onSubmitted,
      this.padding,
      this.textColor,
      this.borderColor,
      this.fillColor,
      this.minLines,
      this.maxLines = 1,
      this.textInputAction,
      this.prefixIcon,
      this.controller,
      this.onChange,
      this.style,
      this.width,
      this.height,
      this.textAlign,
      this.focusNode,
      this.radius,
      this.maxLength,
      this.autoFocus,
      this.suffixIcon,
      this.counterText,
      this.isPassword});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
            width: width ?? MediaQuery.of(context).size.width,
            height: height,
            padding: padding ??
                const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
            decoration: BoxDecoration(
              color: fillColor,
              borderRadius: radius ?? BorderRadius.circular(35),
              border: Border.all(
                  color: error != null && error!.isNotEmpty
                      ? Colors.red
                      : (borderColor ?? Theme.of(context).primaryColor)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                if (prefixIcon != null)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 10),
                    child: prefixIcon,
                  ),
                Expanded(
                  child: TextField(
                    onSubmitted: onSubmitted,
                    minLines: minLines,
                    maxLines: maxLines,
                    focusNode: focusNode,
                    autofocus: autoFocus ?? false,
                    maxLength: maxLength,
                    textAlign: textAlign ?? TextAlign.start,
                    controller: controller,
                    onChanged: onChange,
                    obscureText: isPassword ?? false,
                    textInputAction: textInputAction ?? TextInputAction.next,
                    style: style,
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 0, vertical: 10),
                      hintText: hint,
                      counterText: counterText,
                      hintStyle: TextStyle(
                          color: textColor ?? Colors.grey, fontSize: 15),
                      border: InputBorder.none,
                    ),
                  ),
                ),
                if (suffixIcon != null)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 10),
                    child: suffixIcon,
                  ),
              ],
            )),
        if (error != null && error!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Row(
              children: [
                const Icon(
                  CupertinoIcons.info,
                  color: Colors.red,
                  size: 17,
                ),
                const SizedBox(
                  width: 5,
                ),
                Text(
                  '$error',
                  style: const TextStyle(color: Colors.red, fontSize: 12),
                ),
              ],
            ),
          )
      ],
    );
  }
}
