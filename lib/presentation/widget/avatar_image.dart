import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class AvatarImage extends StatelessWidget {
  final String? url;
  final double? width;
  final double? height;
  const AvatarImage({super.key, required this.url, this.width, this.height});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 50,
      width: width ?? 50,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
      ),
      child: url != null && url!.isNotEmpty
          ? CachedNetworkImage(
              imageUrl: url!,
              fit: BoxFit.cover,
              errorWidget: (_, __, ___) => Image.asset(
                'assets/images/avatar.png',
                fit: BoxFit.cover,
              ),
            )
          : Image.asset(
              'assets/images/avatar.png',
              fit: BoxFit.cover,
            ),
    );
  }
}
