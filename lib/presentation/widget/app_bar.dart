import 'package:family_management/config/app_routes.dart';
import 'package:family_management/presentation/widget/back_button.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

mainAppBar({
  required BuildContext context,
  String? title,
  bool withBackButton = false,
  bool withDrawer = true,
  List<Widget> actions = const [],
}) {
  return AppBar(
    automaticallyImplyLeading: false,
    bottom: PreferredSize(
        preferredSize: Size.fromHeight(actions.isNotEmpty ? 12 : 10),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              withBackButton
                  ? CustomBackButton()
                  : Builder(
                      builder: (context) => GestureDetector(
                            onTap: () => Scaffold.of(context).openDrawer(),
                            child: SvgPicture.asset(
                              'assets/svgs/menu.svg',
                            ),
                          )),
              TextWidget(
                text: title ?? '',
                fontWeight: FontWeight.bold,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Row(
                children: [
                  ...actions,
                  if (withDrawer)
                    GestureDetector(
                      onTap: () => Get.toNamed(AppRoutes.notifications),
                      child: SvgPicture.asset('assets/svgs/bell.svg'),
                    ),
                  if (!withDrawer && actions.isEmpty) const SizedBox(width: 25),
                ],
              ),
            ],
          ),
        )),
  );
}
