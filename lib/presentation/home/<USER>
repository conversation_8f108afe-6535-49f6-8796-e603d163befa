import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/home_controller.dart';
import 'package:family_management/presentation/chat/widgets/search_box.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../config/app_routes.dart';

class HomeScreen extends GetView<HomeController> {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          extendBody: true,
          appBar: mainAppBar(context: context, withDrawer: true),
          drawer: Drawer(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                const DrawerHeader(
                  decoration: BoxDecoration(),
                  child: Text(
                    'Family App',
                    style: TextStyle(color: Colors.black, fontSize: 18),
                  ),
                ),
                ListTile(
                  leading: const Icon(
                    Icons.restaurant,
                    color: pink,
                  ),
                  title: const Text('Shopping'),
                  onTap: () {
                    Get.toNamed(AppRoutes.shoppingAndMeal);
                  },
                )
              ],
            ),
          ),
          body: Column(
            children: [
              SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SearchBox(),
                      const SizedBox(height: 50),
                      Container(
                        width: MediaQuery.of(context).size.width,
                        height: 140,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(11),
                            border: Border.all(
                                width: 1,
                                color: Theme.of(context).primaryColor)),
                      ),
                      const SizedBox(height: 35),
                      TextWidget(
                        text: 'All Events'.tr,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      const SizedBox(height: 10),
                      _events(context),
                      const SizedBox(height: 20),
                      TextWidget(
                        text: 'All Tasks'.tr,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                      const SizedBox(height: 10),
                      _tasks(context)
                    ],
                  ),
                ),
              ),
            ],
          ),
        ));
  }

  _events(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: 100,
      child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemCount: 5,
          itemBuilder: (context, index) {
            return Row(
              children: [
                Container(
                  width: 250,
                  decoration: BoxDecoration(
                    border: Border.all(
                        width: 1, color: Theme.of(context).primaryColor),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 3),
                          Container(
                            height: 10,
                            width: 10,
                            decoration: BoxDecoration(
                                border: Border.all(width: 2.5, color: pink),
                                shape: BoxShape.circle),
                          ),
                        ],
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(
                            text: '19:00 - 20:00'.tr,
                            color: pink,
                            fontSize: 12,
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          TextWidget(
                            text: 'Workout with Ella'.tr,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          TextWidget(
                            text: 'we will do the legs and back workout'.tr,
                            color: Colors.grey,
                            fontSize: 11,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          )
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 10)
              ],
            );
          }),
    );
  }

  _tasks(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: 100,
      child: ListView.builder(
          scrollDirection: Axis.horizontal,
          shrinkWrap: true,
          itemCount: 5,
          itemBuilder: (context, index) {
            return Row(
              children: [
                Container(),
                const SizedBox(
                  width: 5,
                )
              ],
            );
          }),
    );
  }
}
