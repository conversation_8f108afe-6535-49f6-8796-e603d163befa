import 'package:family_management/config/app_routes.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/home_controller.dart';
import 'package:family_management/presentation/achievement_board/achievement_board_view.dart';
import 'package:family_management/presentation/calendar/calendar_view.dart';
import 'package:family_management/presentation/chat/home_chat_view.dart';
import 'package:family_management/presentation/profile/profile_view.dart';
import 'package:family_management/presentation/widget/bottom_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../task/home_task_view.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        bottomNavigationBar: const BottomBar(),
        floatingActionButton: _floatingButton(),
        extendBody: true,
        body: IndexedStack(
          index: controller.activeIndex,
          children: const [
            AchievementBoardView(),
            HomeTaskView(),
            HomeChatView(),
            CalendarView(),
            ProfileView(),
          ],
        ),
      ),
    );
  }

  _floatingButton() {
    if (controller.activeIndex == 1) {
      return FloatingActionButton(
        heroTag: "task_fab",
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
        backgroundColor: primarySwatch,
        elevation: 4,
        onPressed: () => Get.toNamed(AppRoutes.addTask),
        child: const Icon(
          Icons.add,
          color: Colors.white,
        ),
      );
    }
    if (controller.activeIndex == 3) {
      return FloatingActionButton(
        heroTag: "calendar_fab",
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
        onPressed: () => Get.toNamed(AppRoutes.addEvent),
        backgroundColor: primarySwatch,
        elevation: 4,
        child: const Icon(
          Icons.add,
          color: Colors.white,
        ),
      );
    }
    return null;
  }
}
