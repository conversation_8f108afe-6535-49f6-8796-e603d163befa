import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/login_controller.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/icon_widget.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class LoginView extends GetView<LoginController> {
  const LoginView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
          appBar: AppBar(),
          body: SingleChildScrollView(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              height: MediaQuery.of(context).size.height - 100,
              child: Column(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextWidget(
                          text: "Login information".tr,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        TextWidget(
                          text:
                              'Fill in the following information to complete the registration process successfully'
                                  .tr,
                          color: Colors.grey,
                        ),
                        const SizedBox(
                          height: 50,
                        ),
                        TextInput(
                          controller: controller.emailController,
                          hint: 'Email'.tr,
                          error: controller.emailError.isNotEmpty
                              ? controller.emailError
                              : null,
                          prefixIcon:
                              const IconWidget(icon: Icons.email_outlined),
                          onChange: controller.checkCanLogin(),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        TextInput(
                          controller: controller.passwordController,
                          hint: 'Password'.tr,
                          error: controller.passwordError.isEmpty
                              ? null
                              : controller.passwordError,
                          isPassword: true,
                          prefixIcon:
                              const IconWidget(icon: Icons.lock_outlined),
                          onChange: controller.checkCanLogin(),
                        ),
                        Container(
                          alignment: Alignment.centerRight,
                          child: TextButton(
                              onPressed: () => controller.forgetPassword(),
                              child: TextWidget(text: 'Forgot password?'.tr)),
                        ),
                        const SizedBox(
                          height: 15,
                        ),
                        FullWidthButton(
                          text: 'Log in'.tr,
                          onPressed: () => controller.login(),
                          loading: controller.loading,
                          disabled: !controller.canLogin,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            TextWidget(
                              text: 'Don’t Have an account?'.tr,
                              color: Colors.grey,
                            ),
                            TextButton(
                              onPressed: () => Get.toNamed(AppRoutes.register),
                              child: TextWidget(
                                text: 'Sign up'.tr,
                              ),
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [SvgPicture.asset('assets/svgs/login.svg')],
                  ),
                ],
              ),
            ),
          )),
    );
  }
}
