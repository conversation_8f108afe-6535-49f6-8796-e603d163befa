import 'package:family_management/domain/controllers/register_controller.dart';
import 'package:family_management/presentation/auth/widgets/stepper.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

class RegisterView extends GetView<RegisterController> {
  const RegisterView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
          appBar: AppBar(),
          body: SingleChildScrollView(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  TextWidget(
                    text: 'Register information'.tr,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  TextWidget(
                    text:
                        'Fill in the following information to complete the registration process successfully'
                            .tr,
                    color: Colors.grey,
                    fontSize: 13,
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  if (!controller.joinByInvite)
                    StepperWidget(activeStep: controller.activeStep),
                  const SizedBox(
                    height: 20,
                  ),
                  TextInput(
                    error: controller.nameError,
                    controller: controller.nameController,
                    hint: '${'Your full name'.tr}*',
                    prefixIcon: Icon(
                      CupertinoIcons.person,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(
                    height: 19,
                  ),
                  TextInput(
                    error: controller.emailError,
                    controller: controller.emailController,
                    hint: '${'Email address'.tr}*',
                    prefixIcon: Icon(
                      Icons.email_outlined,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(
                    height: 19,
                  ),
                  TextInput(
                    error: controller.passwordError,
                    controller: controller.passwordController,
                    hint: '${'Password'.tr}*',
                    isPassword: !controller.showPassword,
                    prefixIcon: Icon(Icons.lock_outline,
                        color: Theme.of(context).primaryColor),
                    suffixIcon: InkWell(
                      onTap: () =>
                          controller.showPassword = !controller.showPassword,
                      child: Icon(Icons.remove_red_eye_outlined,
                          color: Theme.of(context).primaryColor),
                    ),
                  ),
                  const SizedBox(
                    height: 19,
                  ),
                  TextInput(
                    controller: controller.bioController,
                    hint: 'Bio'.tr,
                    prefixIcon: Icon(Icons.list_alt_rounded,
                        color: Theme.of(context).primaryColor),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  controller.hasImage
                      ? Center(
                          child: SizedBox(
                            width: 100,
                            child: Stack(
                              children: [
                                Container(
                                  height: 100,
                                  margin:
                                      const EdgeInsets.symmetric(vertical: 10),
                                  decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      image: DecorationImage(
                                          fit: BoxFit.cover,
                                          image: FileImage(controller.image!))),
                                ),
                                Positioned(
                                  top: 5,
                                  right: 5,
                                  child: Container(
                                      height: 25,
                                      width: 25,
                                      decoration: const BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.red),
                                      child: Center(
                                        child: IconButton(
                                            onPressed: () =>
                                                controller.hasImage = false,
                                            icon: const Icon(
                                              CupertinoIcons.xmark,
                                              size: 12,
                                              color: Colors.white,
                                            )),
                                      )),
                                )
                              ],
                            ),
                          ),
                        )
                      : GestureDetector(
                          onTap: () => bottomSheet(context),
                          child: Container(
                            width: MediaQuery.of(context).size.width,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 20, vertical: 10),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(25),
                                border: Border.all(
                                    color: Theme.of(context).primaryColor)),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextWidget(
                                  text: 'Enter your profile image'.tr,
                                  color: Colors.grey,
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Center(
                                  child:
                                      Image.asset('assets/images/gallery.png'),
                                ),
                                const SizedBox(
                                  height: 20,
                                )
                              ],
                            ),
                          ),
                        ),
                  const SizedBox(
                    height: 10,
                  ),
                  if (controller.joinByInvite)
                    TextInput(
                      error: controller.inviteCodeError,
                      controller: controller.inviteCodeController,
                      hint: 'Enter invitation code *'.tr,
                      prefixIcon: SvgPicture.asset('assets/svgs/dots.svg'),
                    ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Checkbox(
                          value: controller.joinByInvite,
                          onChanged: (value) =>
                              controller.joinByInvite = value ?? false,
                          activeColor: Theme.of(context).primaryColor,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(5),
                              side: BorderSide(
                                  color: Theme.of(context).primaryColor))),
                      TextWidget(
                        text: 'Join by invite code'.tr,
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                  FullWidthButton(
                      loading: controller.loading,
                      onPressed: () => controller.register(),
                      text: 'Continue'.tr),
                ],
              ),
            ),
          )),
    );
  }

  bottomSheet(BuildContext context) {
    showModalBottomSheet(
        context: context,
        builder: (context) => SizedBox(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: 10),
                    height: 5,
                    width: 75,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  const SizedBox(
                    height: 50,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 5, vertical: 5),
                          child: InkWell(
                            onTap: () =>
                                controller.pickImage(ImageSource.gallery),
                            child: Row(
                              children: [
                                SvgPicture.asset('assets/svgs/image.svg'),
                                const SizedBox(width: 10),
                                TextWidget(
                                  text: 'Gallery'.tr,
                                  fontSize: 18,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const Divider(
                          color: Color.fromARGB(88, 0, 0, 0),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 5, vertical: 5),
                          child: InkWell(
                            onTap: () =>
                                controller.pickImage(ImageSource.camera),
                            child: Row(
                              children: [
                                SvgPicture.asset('assets/svgs/camera.svg'),
                                const SizedBox(width: 10),
                                TextWidget(
                                  text: 'Camera'.tr,
                                  fontSize: 18,
                                )
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 50,
                  ),
                ],
              ),
            ));
  }
}
