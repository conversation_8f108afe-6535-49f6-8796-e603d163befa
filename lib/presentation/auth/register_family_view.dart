import 'package:family_management/domain/controllers/register_family_controller.dart';
import 'package:family_management/presentation/auth/widgets/stepper.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RegisterFamilyView extends GetView<RegisterFamilyController> {
  const RegisterFamilyView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
          textDirection: controller.appService.locale == const Locale('ar')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Scaffold(
            appBar: AppBar(),
            body: SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 50,
                    ),
                    TextWidget(
                      text: 'Register information'.tr,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    TextWidget(
                      text:
                          'Fill in the following information to complete the registration process'
                              .tr,
                      color: Colors.grey,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    const StepperWidget(activeStep: 1),
                    const SizedBox(
                      height: 50,
                    ),
                    TextInput(
                      controller: controller.nameController,
                      hint: 'Enter the Family Name'.tr,
                      prefixIcon: Icon(
                        CupertinoIcons.person_2,
                        color: Theme.of(context).primaryColor,
                      ),
                      onChange: (value) => controller.check(),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    FullWidthButton(
                      text: 'Continue'.tr,
                      onPressed: () => controller.register(),
                      disabled: !controller.canContinue,
                      loading: controller.loading,
                    )
                  ],
                ),
              ),
            ),
          )),
    );
  }
}
