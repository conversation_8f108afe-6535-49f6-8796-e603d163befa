import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/auth_check_controller.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AuthCheckView extends GetView<AuthCheckController> {
  const AuthCheckView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF8F9FA),
              Color(0xFFE9ECEF),
            ],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo/Icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: primarySwatch,
                borderRadius: BorderRadius.circular(30),
                boxShadow: [
                  BoxShadow(
                    color: primarySwatch.withAlpha(60),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: const Icon(
                Icons.family_restroom,
                size: 60,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 40),

            // App Name
            const TextWidget(
              text: 'Family360',
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: primarySwatch,
            ),

            const SizedBox(height: 8),

            // App Tagline
            TextWidget(
              text: 'Stay connected with your family'.tr,
              fontSize: 16,
              color: Colors.grey[600],
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 60),

            // Loading Indicator
            Obx(() => controller.isChecking
                ? Column(
                    children: [
                      SizedBox(
                        width: 40,
                        height: 40,
                        child: CircularProgressIndicator(
                          strokeWidth: 3,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(primarySwatch),
                        ),
                      ),
                      const SizedBox(height: 20),
                      TextWidget(
                        text: 'Checking authentication...'.tr,
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ],
                  )
                : const SizedBox.shrink()),

            const SizedBox(height: 100),

            // Version or Footer Text
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: TextWidget(
                text: 'Connecting families, one moment at a time'.tr,
                fontSize: 12,
                color: Colors.grey[500],
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
