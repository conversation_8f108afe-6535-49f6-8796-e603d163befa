import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/invite_emails_controller.dart';
import 'package:family_management/presentation/auth/widgets/stepper.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class InviteEmailView extends GetView<InviteEmailsController> {
  const InviteEmailView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
          textDirection: controller.appService.direction,
          child: Scaffold(
            appBar: AppBar(
              actions: [
                TextButton(
                  child: TextWidget(text: 'Skip'.tr),
                  onPressed: () => Get.offAllNamed(AppRoutes.home),
                )
              ],
            ),
            body: SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      height: 50,
                    ),
                    TextWidget(
                      text: 'Invite users '.tr,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    TextWidget(
                        text:
                            'Invite your family members by filling in their emails'
                                .tr,
                        color: Colors.grey),
                    const SizedBox(
                      height: 50,
                    ),
                    const StepperWidget(activeStep: 3),
                    const SizedBox(
                      height: 50,
                    ),
                    ListView.builder(
                      scrollDirection: Axis.vertical,
                      shrinkWrap: true,
                      itemBuilder: (context, index) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 5),
                        child: TextInput(
                          hint: 'Email'.tr,
                          prefixIcon: const Icon(
                            Icons.email_outlined,
                          ),
                          controller: controller.emails[index],
                        ),
                      ),
                      itemCount: controller.emails.length,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: InkWell(
                        onTap: () => controller.addEmail(),
                        child: Row(
                          children: [
                            SvgPicture.asset('assets/svgs/add.svg'),
                            const SizedBox(
                              width: 5,
                            ),
                            TextWidget(text: 'Enter another email'.tr)
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    FullWidthButton(
                      text: 'Send invite',
                      onPressed: () => controller.sendInvites(),
                      loading: controller.loading,
                    )
                  ],
                ),
              ),
            ),
          )),
    );
  }
}
