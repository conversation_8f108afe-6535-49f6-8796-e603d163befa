import 'package:family_management/domain/controllers/forget_password_controller.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class EnterEmailView extends GetView<ForgetPasswordController> {
  const EnterEmailView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: AppBar(),
          body: SizedBox(
            height: MediaQuery.of(context).size.height,
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    const SizedBox(
                      height: 50,
                    ),
                    TextWidget(
                      text: 'Get the code'.tr,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    TextWidget(
                      text:
                          'Fill in the following information to complete the registration process successfully'
                              .tr,
                      color: Colors.grey,
                      fontSize: 15,
                    ),
                    const SizedBox(
                      height: 100,
                    ),
                    Center(
                      child: SvgPicture.asset('assets/svgs/enter_email.svg'),
                    ),
                    const SizedBox(
                      height: 50,
                    ),
                    TextInput(
                      controller: controller.emailController,
                      prefixIcon: const Icon(Icons.email_outlined),
                      hint: 'Enter your email'.tr,
                      error: controller.emailError,
                    ),
                    const SizedBox(
                      height: 35,
                    ),
                    FullWidthButton(
                        text: 'Get the code'.tr,
                        loading: controller.sendingCode,
                        onPressed: () => controller.requestCodeAndRedirect())
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
