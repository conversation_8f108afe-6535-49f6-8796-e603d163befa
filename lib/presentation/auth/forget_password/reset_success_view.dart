import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/forget_password_controller.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class ResetSuccessView extends GetView<ForgetPasswordController> {
  const ResetSuccessView({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: AppBar(),
          body: SingleChildScrollView(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: MediaQuery.of(context).size.height / 10,
                  ),
                  TextWidget(
                    text: 'Reset successful'.tr,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                  TextWidget(
                    text: 'Your password has been reset successfully'.tr,
                    color: Colors.grey,
                  ),
                  const SizedBox(
                    height: 100,
                  ),
                  Center(
                    child: SvgPicture.asset('assets/svgs/lock.svg'),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  TextWidget(
                    text:
                        'Your password has been updated successfully. You can now log in with your new password.'
                            .tr,
                    color: Colors.grey,
                  ),
                  const SizedBox(
                    height: 50,
                  ),
                  FullWidthButton(
                      text: 'Continue'.tr,
                      onPressed: () => Get.offAllNamed(AppRoutes.login))
                ],
              ),
            ),
          ),
        ));
  }
}
