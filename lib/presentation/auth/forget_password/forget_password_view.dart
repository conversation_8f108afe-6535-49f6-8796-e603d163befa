import 'package:family_management/domain/controllers/forget_password_controller.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class ForgetPasswordView extends GetView<ForgetPasswordController> {
  const ForgetPasswordView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
          textDirection: controller.appService.locale == const Locale('ar')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Scaffold(
            appBar: AppBar(),
            body: SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(height: 50),
                    TextWidget(
                      text: 'Reset Password'.tr,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    TextWidget(
                      text:
                          'Fill in the following information to complete the registration process successfully'
                              .tr,
                      fontSize: 13,
                      color: Colors.grey,
                    ),
                    const SizedBox(
                      height: 50,
                    ),
                    Center(
                      child: SvgPicture.asset('assets/svgs/enter_email.svg'),
                    ),
                    const SizedBox(
                      height: 50,
                    ),
                    TextInput(
                      controller: controller.passwordController,
                      isPassword: !controller.showPassword,
                      prefixIcon: Icon(
                        Icons.lock_outline_rounded,
                        color: Theme.of(context).primaryColor,
                      ),
                      suffixIcon: InkWell(
                        onTap: () =>
                            controller.showPassword = !controller.showPassword,
                        child: Icon(
                          Icons.remove_red_eye_outlined,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      hint: 'Enter new password'.tr,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    TextInput(
                      controller: controller.confirmPasswordController,
                      isPassword: !controller.showConfirmPassword,
                      textInputAction: TextInputAction.done,
                      prefixIcon: Icon(
                        Icons.lock_outline_rounded,
                        color: Theme.of(context).primaryColor,
                      ),
                      suffixIcon: InkWell(
                        onTap: () => controller.showConfirmPassword =
                            !controller.showConfirmPassword,
                        child: Icon(
                          Icons.remove_red_eye_outlined,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      hint: 'Confirm password'.tr,
                      error: controller.passwordError,
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                    FullWidthButton(
                      onPressed: () => controller.resetPassword(),
                      loading: controller.loading,
                      text: 'Reset password'.tr,
                    )
                  ],
                ),
              ),
            ),
          )),
    );
  }
}
