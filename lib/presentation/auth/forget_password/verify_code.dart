import 'package:family_management/domain/controllers/forget_password_controller.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class VerifyCodeView extends GetView<ForgetPasswordController> {
  const VerifyCodeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
          textDirection: controller.appService.locale == const Locale('ar')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Scaffold(
            appBar: AppBar(),
            body: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      height: 50,
                    ),
                    TextWidget(
                      text: 'Get the code'.tr,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    TextWidget(
                      text: 'Enter the verification code to continue'.tr,
                      color: Colors.grey,
                      fontSize: 15,
                    ),
                    const SizedBox(
                      height: 100,
                    ),
                    Center(
                      child: Image.asset('assets/images/verify_code.png'),
                    ),
                    Center(
                      child: TextWidget(
                          textAlign: TextAlign.center,
                          color: Colors.black,
                          text:
                              "The code will be sent to the following email ${controller.emailController.text}  Enter the code"
                                  .tr),
                    ),
                    const SizedBox(
                      height: 50,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextInput(
                          width: 79,
                          height: 76,
                          controller: controller.firstController,
                          radius: BorderRadius.circular(25),
                          maxLength: 1,
                          counterText: '',
                          textAlign: TextAlign.center,
                          autoFocus: true,
                          style: const TextStyle(
                              fontSize: 20, fontWeight: FontWeight.bold),
                          onChange: (value) =>
                              FocusScope.of(context).nextFocus(),
                        ),
                        TextInput(
                          width: 79,
                          height: 76,
                          controller: controller.seconController,
                          maxLength: 1,
                          counterText: '',
                          style: const TextStyle(
                              fontSize: 20, fontWeight: FontWeight.bold),
                          radius: BorderRadius.circular(25),
                          textAlign: TextAlign.center,
                          onChange: (value) =>
                              FocusScope.of(context).nextFocus(),
                        ),
                        TextInput(
                          width: 79,
                          controller: controller.thirdController,
                          style: const TextStyle(
                              fontSize: 20, fontWeight: FontWeight.bold),
                          height: 76,
                          maxLength: 1,
                          counterText: '',
                          onChange: (value) =>
                              FocusScope.of(context).nextFocus(),
                          textAlign: TextAlign.center,
                          radius: BorderRadius.circular(25),
                        ),
                        TextInput(
                          width: 79,
                          controller: controller.fourthController,
                          height: 76,
                          style: const TextStyle(
                              fontSize: 20, fontWeight: FontWeight.bold),
                          textInputAction: TextInputAction.done,
                          textAlign: TextAlign.center,
                          radius: BorderRadius.circular(25),
                          maxLength: 1,
                          counterText: '',
                          onChange: (value) => FocusScope.of(context).unfocus(),
                        ),
                      ],
                    ),
                    if (controller.codeError.isNotEmpty)
                      Center(
                        child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: TextWidget(
                                text: controller.codeError, color: Colors.red)),
                      ),
                    const SizedBox(
                      height: 35,
                    ),
                    FullWidthButton(
                        text: 'Verify code'.tr,
                        loading: controller.loading,
                        onPressed: () => controller.checkCodeValidity()),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextWidget(
                          text: "Didn't get the code?".tr,
                          color: Colors.grey,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                        TextButton(
                            onPressed: () => controller.requestCode(),
                            child: controller.sendingCode
                                ? const SizedBox(
                                    width: 10,
                                    height: 10,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  )
                                : TextWidget(
                                    text: 'Resend code'.tr,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ))
                      ],
                    )
                  ],
                ),
              ),
            ),
          )),
    );
  }
}
