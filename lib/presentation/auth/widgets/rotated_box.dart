import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';

class RotatedBoxWidget extends StatelessWidget {
  final String text;
  final bool active;
  const RotatedBoxWidget({super.key, required this.active, required this.text});

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: const AlwaysStoppedAnimation(45 / 360),
      child: Container(
        height: 20,
        width: 20,
        decoration: BoxDecoration(
          color: active ? Theme.of(context).primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(5),
          border: Border.all(color: Theme.of(context).primaryColor),
        ),
        child: Center(
          child: RotationTransition(
            turns: const AlwaysStoppedAnimation(-45 / 360),
            child: TextWidget(
              text: text,
              color: active ? Colors.white : Theme.of(context).primaryColor,
              fontSize: 10,
            ),
          ),
        ),
      ),
    );
  }
}
