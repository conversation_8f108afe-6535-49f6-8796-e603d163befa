import 'package:family_management/presentation/auth/widgets/rotated_box.dart';
import 'package:flutter/widgets.dart';

class StepperWidget extends StatelessWidget {
  final int activeStep;
  const StepperWidget({super.key, required this.activeStep});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        RotatedBoxWidget(text: '1', active: activeStep >= 0),
        Image.asset('assets/images/register_vector.png'),
        RotatedBoxWidget(
          text: '2',
          active: activeStep >= 1,
        ),
        Image.asset('assets/images/register_vector.png'),
        RotatedBoxWidget(
          text: '3',
          active: activeStep >= 2,
        ),
      ],
    );
  }
}
