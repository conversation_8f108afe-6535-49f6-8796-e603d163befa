import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/shopping_and_meal_controller.dart';
import 'package:family_management/domain/enums/shopping_list_type.dart';
import 'package:family_management/presentation/shopping_and_meal/widgets/checklist_view.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class MealView extends GetView<ShoppingAndMealController> {
  const MealView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isShoppingList =
          controller.selectedShoppingList.type == ShoppingListType.shoppingList;
      final color = isShoppingList ? primarySwatch : pink;

      return Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          title: TextWidget(
            text: isShoppingList ? 'Shopping List'.tr : 'Meal Plan'.tr,
            fontWeight: FontWeight.bold,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              // Update the main lists before going back
              controller
                  .refreshShoppingListInLists(controller.selectedShoppingList);
              Get.back();
            },
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section with name and date
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            isShoppingList
                                ? Icons.shopping_cart
                                : Icons.restaurant,
                            color: color,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: TextWidget(
                              text: controller.selectedShoppingList.name ?? '',
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Icon(
                            Icons.calendar_today,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 8),
                          TextWidget(
                            text: controller.selectedShoppingList.updatedAt !=
                                    null
                                ? intl.DateFormat('MMMM d, yyyy').format(
                                    controller.selectedShoppingList.updatedAt!)
                                : intl.DateFormat('MMMM d, yyyy').format(
                                    controller.selectedShoppingList.createdAt!),
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ],
                      ),
                      if (controller.selectedShoppingList.createdBy !=
                          null) ...[
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.person,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 8),
                            TextWidget(
                              text:
                                  '${'Created by'.tr}: ${controller.selectedShoppingList.createdBy?.name ?? ''}',
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Checklist section
              ChecklistView(
                shoppingList: controller.selectedShoppingList,
                onItemChecked: controller.check,
                isItemChecked: controller.checked,
                accentColor: color,
                emptyMessage: isShoppingList
                    ? 'No items in this shopping list'.tr
                    : 'No ingredients in this meal plan'.tr,
              ),

              // No action button needed
            ],
          ),
        ),
      );
    });
  }
}
