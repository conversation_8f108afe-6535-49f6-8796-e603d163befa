import 'package:family_management/domain/controllers/shopping_and_meal_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../widget/full_width_button.dart';

class ConfirmAddMealView extends GetView<ShoppingAndMealController> {
  const ConfirmAddMealView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              title: 'Add Meal'.tr,
              withBackButton: true,
              withDrawer: false),
          body: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 30),
                  TextWidget(
                    text: 'Breakfast: Greek Yogurt Parfait'.tr,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  const SizedBox(height: 30),
                  //_buildIngredientChecklist(context),
                  const SizedBox(
                    height: 30,
                  ),
                  FullWidthButton(
                    text: 'Confirm'.tr,
                    onPressed: () => {},
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

// _buildIngredientChecklist(BuildContext context) {
//   return Column(
//     children: controller.ingredients.map((ingredient) {
//       return Obx(() => Row(
//             children: [
//               Checkbox(
//                 value: ingredient["isChecked"].value,
//                 onChanged: (bool? value) {
//                   ingredient["isChecked"].value = value!;
//                 },
//                 checkColor: Theme.of(context).primaryColor,
//               ),
//               TextWidget(
//                 text: ingredient["text"],
//                 fontSize: 14,
//                 color: Colors.black,
//               ),
//             ],
//           ));
//     }).toList(),
//   );
// }
}
