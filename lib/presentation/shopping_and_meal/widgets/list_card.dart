import 'package:family_management/domain/enums/shopping_list_type.dart';
import 'package:family_management/domain/models/shopping_list.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../config/themes.dart';

class ListCard extends StatelessWidget {
  final ShoppingList item;
  const ListCard({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    final isShoppingList = item.type == ShoppingListType.shoppingList;
    final completedItems =
        item.items.where((item) => item.isPurchased == true).length;
    final totalItems = item.items.length;
    final progress = totalItems > 0 ? completedItems / totalItems : 0.0;

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isShoppingList
              ? primarySwatch.withValues(alpha: 77) // ~0.3 opacity
              : pink.withValues(alpha: 77), // ~0.3 opacity
          width: 1,
        ),
      ),
      child: Container(
        width: isShoppingList ? MediaQuery.of(context).size.width / 2.5 : null,
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: TextWidget(
                    text: item.name ?? '',
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    color: isShoppingList ? primarySwatch : pink,
                  ),
                ),
                Icon(
                  isShoppingList ? Icons.shopping_cart : Icons.restaurant,
                  size: 18,
                  color: isShoppingList ? primarySwatch : pink,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 14,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                TextWidget(
                  text: DateFormat('MMM d, yyyy').format(item.createdAt!),
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: isShoppingList ? primarySwatch : pink,
                  ),
                  child: TextWidget(
                    text: '$totalItems ${'items'.tr}',
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
                if (totalItems > 0)
                  TextWidget(
                    text: '$completedItems/$totalItems',
                    color: Colors.grey[700],
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
              ],
            ),
            if (totalItems > 0) ...[
              const SizedBox(height: 8),
              ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.grey[200],
                  color: isShoppingList ? primarySwatch : pink,
                  minHeight: 6,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
