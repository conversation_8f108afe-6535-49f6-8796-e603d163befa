import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/models/shopping_list_item.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';

class ChecklistItem extends StatelessWidget {
  final ShoppingListItem item;
  final bool isChecked;
  final Function(bool?) onChanged;
  final Color? accentColor;

  const ChecklistItem({
    Key? key,
    required this.item,
    required this.isChecked,
    required this.onChanged,
    this.accentColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final color = accentColor ?? primarySwatch;

    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isChecked
              ? color.withValues(alpha: 77)
              : Colors.grey.withValues(alpha: 30),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () => onChanged(!isChecked),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          child: Row(
            children: [
              // Using a Material widget to add ripple effect to the checkbox
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(4),
                  onTap: () => onChanged(!isChecked),
                  child: Checkbox(
                    value: isChecked,
                    onChanged: onChanged,
                    activeColor: color,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                      text: item.name ?? '',
                      fontSize: 16,
                      fontWeight:
                          isChecked ? FontWeight.normal : FontWeight.bold,
                      decoration: isChecked ? TextDecoration.lineThrough : null,
                      color: isChecked ? Colors.grey : Colors.black87,
                    ),
                    if (item.quantity != null && item.unit != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: TextWidget(
                          text: '${item.quantity} ${item.unit}',
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                  ],
                ),
              ),
              if (isChecked)
                Container(
                  margin: const EdgeInsets.only(right: 12),
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 50),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check,
                    color: color,
                    size: 16,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
