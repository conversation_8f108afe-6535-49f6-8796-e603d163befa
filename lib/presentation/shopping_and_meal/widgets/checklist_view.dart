import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/models/shopping_list.dart';
import 'package:family_management/presentation/shopping_and_meal/widgets/checklist_item.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class ChecklistView extends StatefulWidget {
  final ShoppingList shoppingList;
  final Function(int) onItemChecked;
  final Function(int) isItemChecked;
  final Color? accentColor;
  final String? emptyMessage;

  const ChecklistView({
    Key? key,
    required this.shoppingList,
    required this.onItemChecked,
    required this.isItemChecked,
    this.accentColor,
    this.emptyMessage,
  }) : super(key: key);

  @override
  State<ChecklistView> createState() => _ChecklistViewState();
}

class _ChecklistViewState extends State<ChecklistView> {
  @override
  Widget build(BuildContext context) {
    final color = widget.accentColor ?? primarySwatch;
    final completedItems = widget.shoppingList.items
        .where((item) => widget.isItemChecked(item.id!) == true)
        .length;
    final totalItems = widget.shoppingList.items.length;
    final progress = totalItems > 0 ? completedItems / totalItems : 0.0;

    if (widget.shoppingList.items.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 30),
          child: Column(
            children: [
              Icon(
                Icons.list_alt,
                size: 60,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              TextWidget(
                text: widget.emptyMessage ?? 'No items in this list'.tr,
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Progress section
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 15),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextWidget(
                    text: 'Progress'.tr,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: TextWidget(
                      text: '$completedItems/$totalItems',
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              LinearPercentIndicator(
                percent: progress,
                lineHeight: 10,
                progressColor: color,
                backgroundColor: Colors.grey[200],
                barRadius: const Radius.circular(5),
                animation: true,
                animateFromLastPercent: true,
                padding: EdgeInsets.zero,
              ),
              const SizedBox(height: 8),
              TextWidget(
                text: progress == 1.0
                    ? 'All items completed!'.tr
                    : '${(progress * 100).toInt()}% ${'completed'.tr}',
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // Items list
        TextWidget(
          text: 'Items'.tr,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
        const SizedBox(height: 12),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: widget.shoppingList.items.length,
          itemBuilder: (context, index) {
            final item = widget.shoppingList.items[index];
            final isChecked = widget.isItemChecked(item.id!);

            return ChecklistItem(
              item: item,
              isChecked: isChecked,
              onChanged: (_) {
                widget.onItemChecked(item.id!);
                setState(() {}); // Force rebuild after item is checked
              },
              accentColor: color,
            );
          },
        ),
      ],
    );
  }
}
