import 'package:family_management/domain/controllers/shopping_and_meal_controller.dart';
import 'package:family_management/presentation/shopping_and_meal/widgets/list_card.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../config/app_routes.dart';
import '../../config/themes.dart';
import '../widget/loading_indicator.dart';
import '../widget/text_widget.dart';

class ShoppingListView extends GetView<ShoppingAndMealController> {
  const ShoppingListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Directionality(
          textDirection: controller.appService.direction,
          child: Scaffold(
            appBar: mainAppBar(
                context: context,
                withDrawer: false,
                withBackButton: true,
                title: 'Lists'.tr),
            body: RefreshIndicator(
              onRefresh: () async {
                await controller.getShoppingLists();
                await controller.getMeals();
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 80),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionHeader(
                      context,
                      title: 'Shopping Lists'.tr,
                      icon: Icons.shopping_cart,
                      color: primarySwatch,
                    ),
                    const SizedBox(height: 12),
                    controller.loading
                        ? const Center(
                            child: Padding(
                              padding: EdgeInsets.symmetric(vertical: 30),
                              child: LoadingIndicator(),
                            ),
                          )
                        : _buildShoppingLists(context),
                    const SizedBox(height: 24),
                    _buildSectionHeader(
                      context,
                      title: 'Planned Meals'.tr,
                      icon: Icons.restaurant,
                      color: pink,
                    ),
                    const SizedBox(height: 12),
                    controller.loadingPlanned
                        ? const Center(
                            child: Padding(
                              padding: EdgeInsets.symmetric(vertical: 30),
                              child: LoadingIndicator(),
                            ),
                          )
                        : _buildPlannedMeals(context),
                  ],
                ),
              ),
            ),
            floatingActionButton: FloatingActionButton(
              heroTag: "shopping_list_fab",
              onPressed: () => Get.toNamed(AppRoutes.addShoppingList),
              elevation: 4,
              backgroundColor: primarySwatch,
              child: const Icon(
                Icons.add,
                color: Colors.white,
              ),
            ),
          ),
        ));
  }

  Widget _buildSectionHeader(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 8),
        TextWidget(
          text: title,
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ],
    );
  }

  Widget _buildShoppingLists(BuildContext context) {
    if (controller.shoppingLists.isEmpty) {
      return _buildEmptyState(
        icon: Icons.shopping_cart_outlined,
        message: 'No shopping lists yet'.tr,
        hint: 'Create a shopping list by tapping the + button'.tr,
      );
    }

    return SizedBox(
      height: 160,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: controller.shoppingLists.length,
        itemBuilder: (context, index) {
          final shoppingList = controller.shoppingLists[index];
          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: GestureDetector(
              onTap: () async {
                controller.selectedShoppingList = shoppingList;
                await Get.toNamed(AppRoutes.meal, arguments: shoppingList);
                // Refresh lists when returning from meal view
                controller.getShoppingLists();
                controller.getMeals();
              },
              child: ListCard(item: shoppingList),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPlannedMeals(BuildContext context) {
    if (controller.plannedMeals.isEmpty) {
      return _buildEmptyState(
        icon: Icons.restaurant_outlined,
        message: 'No planned meals yet'.tr,
        hint: 'Plan a meal by tapping the + button'.tr,
      );
    }

    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1,
      ),
      itemCount: controller.plannedMeals.length,
      itemBuilder: (context, index) {
        final item = controller.plannedMeals[index];
        return GestureDetector(
          onTap: () async {
            controller.selectedShoppingList = item;
            await Get.toNamed(AppRoutes.meal, arguments: item);
            // Refresh lists when returning from meal view
            controller.getShoppingLists();
            controller.getMeals();
          },
          child: ListCard(item: item),
        );
      },
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String message,
    required String hint,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30),
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 60,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          TextWidget(
            text: message,
            fontSize: 16,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 8),
          TextWidget(
            text: hint,
            fontSize: 14,
            color: Colors.grey[500],
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
