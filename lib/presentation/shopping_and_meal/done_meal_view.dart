import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/task_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../widget/text_widget.dart';

class DoneMealView extends GetView<TaskController> {
  const DoneMealView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
          () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              title: 'Shopping and Meal'.tr,
              withBackButton: true,
              withDrawer: false),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset('assets/svgs/done_meal.svg'),
                const SizedBox(height: 30),
                TextWidget(
                  text: 'Congratulations! '.tr,
                  fontWeight: FontWeight.bold,
                  fontSize: 24,
                  color: pink,
                ),
                const SizedBox(height: 10),
                TextWidget(
                  text: 'You have successfully completed the recipe.'.tr,
                  fontSize: 14,
                  color: Theme.of(context).primaryColor,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
