import 'package:family_management/presentation/shopping_and_meal/polls_view.dart';
import 'package:family_management/presentation/shopping_and_meal/shopping_list_view.dart';
import 'package:family_management/presentation/widget/back_button.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../domain/controllers/shopping_and_meal_controller.dart';

class ShoppingAndMealView extends GetView<ShoppingAndMealController> {
  const ShoppingAndMealView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            leading: Padding(
              padding: const EdgeInsets.all(10),
              child: CustomBackButton(),
            ),
            title: TextWidget(
              text: controller.activeIndex == 0
                  ? 'Shopping and Meal '.tr
                  : 'The Polls'.tr,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
            centerTitle: true,
            bottom: TabBar(
              controller: controller.tabController,
              tabs: [
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset('assets/svgs/shopping.svg',
                          colorFilter: ColorFilter.mode(
                            controller.activeIndex == 0
                                ? Theme.of(context).primaryColor
                                : Colors.grey,
                            BlendMode.srcIn,
                          )),
                      const SizedBox(
                        width: 5,
                      ),
                      TextWidget(
                        text: 'Shopping list'.tr,
                        color: controller.activeIndex == 0
                            ? Theme.of(context).primaryColor
                            : Colors.grey,
                      )
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        'assets/svgs/polls.svg',
                        colorFilter: ColorFilter.mode(
                          controller.activeIndex == 1
                              ? Theme.of(context).primaryColor
                              : Colors.grey,
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      TextWidget(
                        text: 'The Polls'.tr,
                        color: controller.activeIndex == 1
                            ? Theme.of(context).primaryColor
                            : Colors.grey,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          body: TabBarView(
            controller: controller.tabController,
            children: const [
              ShoppingListView(),
              PollsView(),
            ],
          ),
        ),
      ),
    );
  }
}
