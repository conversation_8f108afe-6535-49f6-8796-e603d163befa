import 'package:family_management/domain/controllers/add_poll_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/multi_select.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

class AddPollView extends GetView<AddPollController> {
  const AddPollView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              title: 'Add Poll'.tr,
              withBackButton: true,
              withDrawer: false),
          body: SingleChildScrollView(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  const SizedBox(height: 30),
                  TextInput(
                    prefixIcon: const Icon(Icons.add),
                    controller: controller.nameController,
                    hint: 'Enter the title'.tr,
                    error: controller.nameError,
                  ),
                  const SizedBox(height: 20),
                  ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      scrollDirection: Axis.vertical,
                      itemCount: controller.items.length,
                      itemBuilder: (context, index) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 5.0),
                            child: TextInput(
                              controller: controller.items[index],
                              hint: 'Enter option'.tr,
                            ),
                          )),
                  const SizedBox(height: 10),
                  GestureDetector(
                    onTap: () => controller.addOption(),
                    child: Row(
                      children: [
                        const Icon(Icons.add),
                        TextWidget(text: 'Add option'.tr)
                      ],
                    ),
                  ),
                  if (controller.itemsError.isNotEmpty) ...[
                    const SizedBox(height: 10),
                    TextWidget(
                      text: controller.itemsError,
                      color: Colors.red,
                      fontSize: 12,
                    )
                  ],

                  const SizedBox(height: 20),

                  // Assignees Section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.people_outline,
                              color: Theme.of(context).primaryColor,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            TextWidget(
                              text: 'Assign to Family Members'.tr,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[800],
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        TextWidget(
                          text:
                              'Select family members who can vote on this poll (required)'
                                  .tr,
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(height: 16),
                        Obx(() => controller.loadingMembers
                            ? const Center(
                                child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator(),
                                ),
                              )
                            : MultiSelect(
                                hint: 'Select assignees (required)'.tr,
                                controller: controller.multiSelectController,
                                items: controller.members
                                    .map((member) => DropdownItem<String>(
                                        value: member.id.toString(),
                                        label: member.name ?? ''))
                                    .toList(),
                              )),

                        // Show assignees error
                        if (controller.assigneesError.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          TextWidget(
                            text: controller.assigneesError,
                            color: Colors.red,
                            fontSize: 12,
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: 20),

                  FullWidthButton(
                    text: 'Submit'.tr,
                    onPressed: () => controller.submit(),
                    loading: controller.loading,
                  ),
                ],
              ),
            ),
          ),
        )));
  }
}
