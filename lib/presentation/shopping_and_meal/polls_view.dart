import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/polls_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/percent_indicator.dart';

import '../../config/themes.dart';
import '../widget/text_widget.dart';

class PollsView extends GetView<PollsController> {
  const PollsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              withDrawer: false,
              withBackButton: true,
              title: 'Polls'.tr),
          floatingActionButton: FloatingActionButton(
            onPressed: () => Get.toNamed(AppRoutes.addPoll),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(100)),
            backgroundColor: Colors.white,
            child: Icon(
              Icons.add,
              color: Theme.of(context).primaryColor,
            ),
          ),
          body: controller.loading
              ? const Center(
                  child: LoadingIndicator(),
                )
              : RefreshIndicator(
                  onRefresh: () => controller.getPolls(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: controller.polls.isEmpty
                        ? _buildEmptyState()
                        : _buildPollsList(context),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.poll_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          TextWidget(
            text: 'No polls found'.tr,
            fontSize: 18,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 8),
          TextWidget(
            text: 'Create a new poll by tapping the + button'.tr,
            fontSize: 14,
            color: Colors.grey[500],
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPollsList(BuildContext context) {
    return ListView.builder(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.only(top: 16, bottom: 80),
      itemCount: controller.polls.length,
      itemBuilder: (context, index) {
        final poll = controller.polls[index];
        return _buildPollCard(context, poll, index);
      },
    );
  }

  Widget _buildPollCard(BuildContext context, poll, int pollIndex) {
    // Find the winning option (highest votes)
    int highestVotes = 0;
    int? winningItemId;

    for (final item in poll.items) {
      if ((item.totalVotes ?? 0) > highestVotes) {
        highestVotes = item.totalVotes ?? 0;
        winningItemId = item.id;
      }
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: TextWidget(
                    text: poll.name ?? '',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
                if (poll.userId == controller.appService.user.id)
                  IconButton(
                    icon: const Icon(Icons.delete_outline, color: Colors.red),
                    onPressed: () => _showDeleteDialog(context, poll.id!),
                    tooltip: 'Delete Poll'.tr,
                  ),
              ],
            ),
            const SizedBox(height: 16),
            ...poll.items
                .map((item) => _buildPollItem(
                      context,
                      poll,
                      item,
                      isWinning: item.id == winningItemId && highestVotes > 0,
                    ))
                .toList(),
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerRight,
              child: TextWidget(
                text: '${poll.getTotalVotes()} ${'Total Votes'.tr}',
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPollItem(BuildContext context, poll, item,
      {bool isWinning = false}) {
    final isVoted = controller.voted(poll.id, item.id);
    final percent = controller.getItemPercent(poll.id!, item.id!);

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => controller.vote(poll.id!, item.id!),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: isVoted ? lightPink2 : Colors.grey[100],
            border:
                isWinning ? Border.all(color: primarySwatch, width: 2) : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    height: 24,
                    width: 24,
                    decoration: BoxDecoration(
                      color: isVoted ? primarySwatch : Colors.white,
                      border: Border.all(
                        width: 2,
                        color: isVoted ? primarySwatch : Colors.grey[400]!,
                      ),
                      shape: BoxShape.circle,
                    ),
                    child: isVoted
                        ? const Icon(
                            Icons.check,
                            size: 16,
                            color: Colors.white,
                          )
                        : null,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextWidget(
                      text: item.name ?? '',
                      fontSize: 16,
                      fontWeight: isVoted ? FontWeight.bold : FontWeight.normal,
                      color: Colors.black87,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ),
                  TextWidget(
                    text: '${item.totalVotes ?? 0}',
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: isVoted ? primarySwatch : Colors.grey[600],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearPercentIndicator(
                percent: (percent < 0 || percent > 1) ? 0 : percent,
                lineHeight: 8,
                progressColor: isVoted ? primarySwatch : pink,
                backgroundColor: Colors.grey[200],
                animation: true,
                animateFromLastPercent: true,
                animationDuration: 300,
                barRadius: const Radius.circular(4),
                padding: EdgeInsets.zero,
              ),
              if (isWinning && percent > 0)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.emoji_events,
                        size: 14,
                        color: Colors.amber[700],
                      ),
                      const SizedBox(width: 4),
                      TextWidget(
                        text: 'Leading'.tr,
                        fontSize: 12,
                        color: Colors.amber[700],
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, int pollId) {
    showDialog(
      context: context,
      builder: (context) => Obx(
        () => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text("Delete Poll".tr),
          content: Text(
            "Are you sure you want to delete this poll?".tr,
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text(
                "Cancel".tr,
                style: const TextStyle(color: primarySwatch),
              ),
            ),
            ElevatedButton(
              onPressed: () => controller.deletePoll(pollId),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: controller.deleting
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Text("Delete".tr),
            ),
          ],
        ),
      ),
    );
  }
}
