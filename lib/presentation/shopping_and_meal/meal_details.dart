import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/meal_details_controller.dart';
import 'package:family_management/domain/enums/shopping_list_type.dart';
import 'package:family_management/presentation/shopping_and_meal/widgets/checklist_view.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:family_management/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MealDetails extends GetView<MealDetailsController> {
  const MealDetails({super.key});

  @override
  Widget build(BuildContext context) {
    final isShoppingList =
        controller.shoppingList.type == ShoppingListType.shoppingList;
    final color = isShoppingList ? primarySwatch : pink;

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: TextWidget(
          text: isShoppingList ? 'Shopping List Details'.tr : 'Meal Details'.tr,
          fontWeight: FontWeight.bold,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Update the main lists before going back
            if (controller.mainController != null && controller.hasChanges) {
              controller.mainController!
                  .refreshShoppingListInLists(controller.shoppingList);
            }
            Get.back();
          },
        ),
      ),
      body: GetBuilder<MealDetailsController>(
        builder: (controller) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header section with name and date
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              isShoppingList
                                  ? Icons.shopping_cart
                                  : Icons.restaurant,
                              color: color,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: TextWidget(
                                text: controller.shoppingList.name ?? '',
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Icon(
                              Icons.calendar_today,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 8),
                            TextWidget(
                              text: Utils.formatDate(
                                  controller.shoppingList.createdAt!),
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ],
                        ),
                        if (controller.shoppingList.createdBy != null) ...[
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                Icons.person,
                                size: 16,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 8),
                              TextWidget(
                                text:
                                    '${'Created by'.tr}: ${controller.shoppingList.createdBy?.name ?? ''}',
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // Checklist section
                ChecklistView(
                  shoppingList: controller.shoppingList,
                  onItemChecked: controller.check,
                  isItemChecked: controller.checked,
                  accentColor: color,
                  emptyMessage: isShoppingList
                      ? 'No items in this shopping list'.tr
                      : 'No ingredients in this meal plan'.tr,
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
