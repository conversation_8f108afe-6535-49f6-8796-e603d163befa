import 'package:family_management/domain/controllers/add_shopping_and_meal_controller.dart';
import 'package:family_management/domain/enums/shopping_list_type.dart';
import 'package:family_management/domain/models/suggested_item.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/custom_dropdown.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:family_management/presentation/widget/text_input_with_suggestions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../widget/full_width_button.dart';
import '../widget/text_input.dart';

class AddShoppingListView extends GetView<AddShoppingAndMealController> {
  const AddShoppingListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              title: 'Add Shopping List'.tr,
              withBackButton: true,
              withDrawer: false),
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 30),
                  Row(
                    children: [
                      SvgPicture.asset(
                        'assets/svgs/create_meal.svg',
                      ),
                      const SizedBox(width: 10),
                      Text(
                        'Create Shopping List: '.tr,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: TextInput(
                      controller: controller.nameController,
                      hint: '+ Add shopping list name'.tr,
                      minLines: 1,
                      maxLines: 1,
                      prefixIcon: SvgPicture.asset(
                        'assets/svgs/create_meal.svg',
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  CustomDropdown(
                      value: controller.type,
                      onChanged: (value) => controller.type = value!,
                      items: [
                        DropdownMenuItem(
                          value:
                              ShoppingListType.shoppingList.value().toString(),
                          child: TextWidget(
                            text: ShoppingListType.shoppingList.toString().tr,
                          ),
                        ),
                        DropdownMenuItem(
                          value: ShoppingListType.mealList.value().toString(),
                          child: TextWidget(
                            text: ShoppingListType.mealList.toString().tr,
                          ),
                        ),
                      ]),
                  const SizedBox(height: 30),
                  TextWidget(
                    text: 'Shopping List Items'.tr,
                    fontWeight: FontWeight.bold,
                  ),
                  const SizedBox(height: 10),
                  Column(
                    children: List.generate(
                      controller.itemsController.length,
                      (index) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          children: [
                            Expanded(
                              child: Obx(() =>
                                  TextInputWithSuggestions<SuggestedItem>(
                                    controller:
                                        controller.itemsController[index],
                                    hint: 'Enter item name'.tr,
                                    minLines: 1,
                                    maxLines: 1,
                                    suggestions: controller.filteredSuggestions,
                                    showSuggestions: controller
                                        .shouldShowSuggestionsForIndex(index),
                                    suggestionBuilder: (item) =>
                                        item.name ?? '',
                                    onSuggestionSelected: (item) => controller
                                        .selectSuggestion(item, index),
                                    onChange: (value) =>
                                        controller.onItemChanged(value, index),
                                    onSuggestionsDismissed: () =>
                                        controller.hideSuggestions(),
                                    suggestionIcon: Icons.shopping_cart,
                                    customSuggestionBuilder: (item) =>
                                        Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 12),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.shopping_cart,
                                            size: 16,
                                            color: Colors.grey[600],
                                          ),
                                          const SizedBox(width: 12),
                                          Expanded(
                                            child: Text(
                                              item.name ?? '',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Get.theme.primaryColor,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Icon(
                                            Icons.arrow_forward_ios,
                                            size: 12,
                                            color: Colors.grey[400],
                                          ),
                                        ],
                                      ),
                                    ),
                                  )),
                            ),
                            IconButton(
                              icon: const Icon(Icons.remove_circle,
                                  color: Colors.red),
                              onPressed: () =>
                                  controller.removeShoppingItem(index),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: TextButton.icon(
                      icon: const Icon(Icons.add),
                      label: TextWidget(text: 'Add Item'.tr),
                      onPressed: () => controller.addShoppingItem(),
                    ),
                  ),
                  const SizedBox(height: 30),
                  FullWidthButton(
                    loading: controller.loading,
                    text: 'Confirm'.tr,
                    onPressed: () {
                      controller.createShoppingList();
                      //Get.toNamed(AppRoutes.doneMeal);
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
