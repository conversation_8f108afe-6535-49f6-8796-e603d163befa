import 'package:family_management/domain/controllers/shopping_and_meal_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RestaurantDescriptionView extends GetView<ShoppingAndMealController> {
  const RestaurantDescriptionView({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
        textDirection: TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              title: 'Name restaurant'.tr,
              withBackButton: true,
              withDrawer: false),
          body: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 30),
                    Container(
                      width: 400,
                      height: 200,
                      clipBehavior: Clip.antiAliasWithSaveLayer,
                      decoration:
                          const BoxDecoration(shape: BoxShape.rectangle),
                      child: Image.asset(
                        'assets/images/restaurant.png',
                        fit: BoxFit.contain,
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextWidget(
                      text: 'Restaurant name'.tr,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                    const SizedBox(height: 5),
                    TextWidget(
                      text:
                          'LoremspamLoremspamLoremspamLoremspamLoremserrr pamvLoremspamLoremspam'
                              .tr,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        const Icon(
                          Icons.location_pin,
                        ),
                        const SizedBox(width: 10),
                        TextWidget(
                          text: 'Syria , Damascus Damascus'.tr,
                          fontSize: 12,
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        const Icon(
                          Icons.wifi_calling_3_outlined,
                        ),
                        const SizedBox(width: 10),
                        TextWidget(
                          text: '0944658287'.tr,
                          fontSize: 12,
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        const Icon(
                          CupertinoIcons.clock,
                        ),
                        const SizedBox(width: 10),
                        TextWidget(
                          text: 'open time :'.tr,
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 10),
                        TextWidget(
                          text: '12 : 12 pm'.tr,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ],
                    ),
                    const SizedBox(height: 40),
                    TextWidget(
                      text: 'restaurant offers a variety of services'.tr,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    const SizedBox(height: 20),
                    Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              height: 14,
                              width: 14,
                              decoration: BoxDecoration(
                                color: Colors.grey,
                                border: Border.all(
                                    width: 1,
                                    color: Theme.of(context).primaryColor),
                                shape: BoxShape.rectangle,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            const SizedBox(width: 10),
                            TextWidget(
                              text: 'LoremspamLoremspamLoremspamLoremspa'.tr,
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              height: 14,
                              width: 14,
                              decoration: BoxDecoration(
                                color: Colors.grey,
                                border: Border.all(
                                    width: 1,
                                    color: Theme.of(context).primaryColor),
                                shape: BoxShape.rectangle,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            const SizedBox(width: 10),
                            TextWidget(
                              text: 'LoremspamLoremspamLoremspamLoremspa'.tr,
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              height: 14,
                              width: 14,
                              decoration: BoxDecoration(
                                color: Colors.grey,
                                border: Border.all(
                                    width: 1,
                                    color: Theme.of(context).primaryColor),
                                shape: BoxShape.rectangle,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            const SizedBox(width: 10),
                            TextWidget(
                              text: 'LoremspamLoremspamLoremspamLoremspa'.tr,
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              height: 14,
                              width: 14,
                              decoration: BoxDecoration(
                                color: Colors.grey,
                                border: Border.all(
                                    width: 1,
                                    color: Theme.of(context).primaryColor),
                                shape: BoxShape.rectangle,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            const SizedBox(width: 10),
                            TextWidget(
                              text: 'LoremspamLoremspamLoremspamLoremspa'.tr,
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ],
                        ),
                      ],
                    )
                  ],
                ),
              )),
        ));
  }
}
