import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/add_task_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/assignees_widget.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ConfirmAddTaskView extends GetView<AddTaskController> {
  const ConfirmAddTaskView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              title: 'Add Task'.tr,
              withBackButton: true,
              withDrawer: false),
          body: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 30),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      TextWidget(
                        text: controller.titleController.text.isEmpty
                            ? 'No title'
                            : controller.titleController.text,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: pink,
                      ),
                    ],
                  ),
                  const SizedBox(height: 30),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(CupertinoIcons.clock),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: 'Due Date : '.tr,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: controller.dueDate.isNotEmpty
                            ? controller.dueDate
                            : 'Not Set',
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      )
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(CupertinoIcons.person),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: 'Responsible Person :'.tr,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: controller.user.name!,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      )
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(CupertinoIcons.person_add),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: 'Priority people :'.tr,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 5),
                      SizedBox(
                          width: MediaQuery.of(context).size.width - 200,
                          height: 40,
                          child: AssigneesWidget(
                              users: controller.selectedAssignees))
                    ],
                  ),
                  const SizedBox(height: 30),
                  TextWidget(
                    text: 'The task description :'.tr,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 10),
                  TextWidget(
                    text: controller.descriptionController.text.isNotEmpty
                        ? controller.descriptionController.text
                        : 'No description provided',
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                  const SizedBox(height: 30),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      TextWidget(
                        text: 'The task type :'.tr,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 5),
                      TextWidget(
                        text: controller.selectedTaskType.value == 1
                            ? 'One-time event'.tr
                            : 'Recurring event'.tr,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ],
                  ),
                  if (controller.selectedTaskType.value == 2) ...[
                    const SizedBox(height: 30),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const Icon(CupertinoIcons.calendar),
                        const SizedBox(width: 5),
                        TextWidget(
                          text: 'Task repetition :'.tr,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 5),
                        TextWidget(
                          text: controller.selectedRepetition.value.isNotEmpty
                              ? controller.selectedRepetition.value
                              : 'No repetition set',
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ],
                    ),
                    const SizedBox(height: 30),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const Icon(CupertinoIcons.clock),
                        const SizedBox(width: 5),
                        TextWidget(
                          text: 'Repeat interval :'.tr,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 5),
                        TextWidget(
                          text:
                              '${controller.recurringIntervalController.text} ',
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ],
                    ),
                  ],
                  const SizedBox(height: 30),
                  controller.loading
                      ? const Center(
                          child: CircularProgressIndicator(),
                        )
                      : Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              GestureDetector(
                                onTap: () => Get.back(),
                                child: Container(
                                  width: 150,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(20),
                                    color: Colors.red,
                                  ),
                                  alignment: Alignment.center,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 15,
                                  ),
                                  child: TextWidget(
                                    text: 'Cancel'.tr,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 20),
                              GestureDetector(
                                onTap: () => controller.storeOrUpdate(),
                                child: Container(
                                  width: 150,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(20),
                                    color: Theme.of(context).primaryColor,
                                  ),
                                  alignment: Alignment.center,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 15,
                                  ),
                                  child: TextWidget(
                                    text: 'Confirm'.tr,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
