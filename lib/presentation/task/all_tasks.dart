import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/all_tasks_controller.dart';
import 'package:family_management/presentation/chat/widgets/search_box.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/task_card.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AllTasksView extends GetView<AllTasksController> {
  const AllTasksView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Directionality(
          textDirection: controller.appService.locale == const Locale('ar')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Scaffold(
            appBar: mainAppBar(
                context: context,
                withBackButton: true,
                withDrawer: false,
                title: 'Tasks'.tr),
            body: controller.loading
                ? const Center(child: CircularProgressIndicator())
                : Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: [
                        SearchBox(
                          initialValue: controller.searchQuery,
                          onChanged: controller.onSearchChanged,
                        ),
                        const SizedBox(height: 20),
                        Expanded(
                          child: controller.tasks.isEmpty
                              ? _buildEmptyState(context)
                              : ListView.builder(
                                  shrinkWrap: true,
                                  scrollDirection: Axis.vertical,
                                  itemCount: controller.tasks.length,
                                  itemBuilder: (context, index) {
                                    final item = controller.tasks[index];
                                    return Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        TaskCard(
                                          task: item,
                                          onPressed: (_) => Get.toNamed(
                                              AppRoutes.taskDetails,
                                              arguments: item),
                                        ),
                                        const SizedBox(
                                          height: 10,
                                        )
                                      ],
                                    );
                                  }),
                        )
                      ],
                    ),
                  ),
            floatingActionButton: FloatingActionButton(
              backgroundColor: Theme.of(context).primaryColor,
              elevation: 4,
              onPressed: () => Get.toNamed(AppRoutes.addTask),
              child: const Icon(
                Icons.add,
                color: Colors.white,
              ),
            ),
          ),
        ));
  }

  Widget _buildEmptyState(BuildContext context) {
    final isSearching = controller.searchQuery.isNotEmpty;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isSearching ? Icons.search_off : Icons.assignment_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            TextWidget(
              text: isSearching ? 'No tasks found'.tr : 'No tasks available'.tr,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextWidget(
              text: isSearching
                  ? 'Try adjusting your search terms'.tr
                  : 'Create a new task by tapping the + button'.tr,
              fontSize: 14,
              color: Colors.grey[600],
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
