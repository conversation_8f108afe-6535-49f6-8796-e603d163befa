import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/task_controller.dart';
import 'package:family_management/presentation/widget/improved_task_card.dart';
import 'package:family_management/presentation/widget/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../config/app_routes.dart';
import '../widget/text_widget.dart';

class TaskView extends GetView<TaskController> {
  final bool mine;

  const TaskView({super.key, required this.mine});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TaskController>(
      builder: (controller) => Directionality(
        textDirection: controller.appService.direction,
        child: Scaffold(
          body: RefreshIndicator(
            onRefresh: () => controller.getTasks(),
            child: controller.loading
                ? const Center(child: LoadingIndicator())
                : controller.tasks.isEmpty
                    ? _buildEmptyState(context)
                    : Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: ListView.builder(
                          controller: controller.scrollController,
                          itemCount: controller.tasks.length +
                              2, // +2 for header and loading indicator
                          itemBuilder: (context, index) {
                            if (index == 0) {
                              // Header section
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 20),
                                  Container(
                                    padding:
                                        const EdgeInsets.symmetric(vertical: 8),
                                    child: Row(
                                      children: [
                                        Icon(
                                          mine
                                              ? Icons.person_outline
                                              : Icons.people_outline,
                                          color: primarySwatch,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 8),
                                        TextWidget(
                                          text: mine
                                              ? 'My Tasks'.tr
                                              : 'All Tasks'.tr,
                                          color: primarySwatch,
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        const Spacer(),
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 4),
                                          decoration: BoxDecoration(
                                            color: primarySwatch.withValues(
                                                alpha: 30),
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: TextWidget(
                                            text: '${controller.tasks.length}',
                                            fontSize: 14,
                                            fontWeight: FontWeight.bold,
                                            color: primarySwatch,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                ],
                              );
                            } else if (index == controller.tasks.length + 1) {
                              // Loading indicator at the bottom
                              return Obx(() => controller.isLoadingMore
                                  ? const Padding(
                                      padding: EdgeInsets.all(16.0),
                                      child: Center(child: LoadingIndicator()),
                                    )
                                  : const SizedBox(height: 80));
                            } else {
                              // Task items
                              final taskIndex = index - 1;
                              final task = controller.tasks[taskIndex];
                              return ImprovedTaskCard(
                                task: task,
                                showAssignees: !mine,
                                currentUserId: controller.user.id,
                                onPressed: (task) {
                                  controller.selectedTask = task;
                                  Get.toNamed(AppRoutes.taskDetails,
                                      arguments: task);
                                },
                              );
                            }
                          },
                        ),
                      ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              mine ? Icons.assignment_ind_outlined : Icons.assignment_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            TextWidget(
              text: mine ? 'No tasks for you'.tr : 'No tasks available'.tr,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextWidget(
              text: mine
                  ? 'Tasks created by you or assigned to you will appear here'
                      .tr
                  : 'Create a new task by tapping the + button'.tr,
              fontSize: 14,
              color: Colors.grey[600],
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
