import 'dart:developer';

import 'package:family_management/config/app_routes.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/task_details_controller.dart';
import 'package:family_management/domain/enums/task_type.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/assignees_widget.dart';
import 'package:family_management/presentation/widget/custom_dropdown.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;
import 'package:percent_indicator/linear_percent_indicator.dart';

class TaskDetailsView extends GetView<TaskDetailsController> {
  const TaskDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
          textDirection: TextDirection.ltr,
          child: Scaffold(
            appBar: mainAppBar(
                context: context,
                title: 'Task details'.tr,
                withBackButton: true,
                withDrawer: false),
            body: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 30),
                      SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: TextWidget(
                          text: '${controller.task.title}',
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: pink,
                        ),
                      ),
                      const SizedBox(height: 30),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              const Icon(CupertinoIcons.clock),
                              const SizedBox(width: 5),
                              TextWidget(
                                text: 'Due Date : '.tr,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                              const SizedBox(width: 5),
                              TextWidget(
                                text: controller.task.dueDate != null
                                    ? intl.DateFormat('d / M / y')
                                        .format(controller.task.dueDate!)
                                    : 'N/A',
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              )
                            ],
                          ),
                          controller.subTasks.isNotEmpty
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        TextWidget(
                                          text: 'Progress'.tr,
                                          color: pink,
                                          fontSize: 12,
                                        ),
                                        const SizedBox(width: 20),
                                        TextWidget(
                                          text:
                                              '${controller.subTasks.where((e) => e.status == 'done').length}/${controller.subTasks.length}',
                                          fontSize: 12,
                                        )
                                      ],
                                    ),
                                    LinearPercentIndicator(
                                      barRadius: const Radius.circular(10),
                                      width: 100,
                                      backgroundColor: Colors.grey[200],
                                      progressColor: pink,
                                      percent: controller.taskProgress(),
                                    )
                                  ],
                                )
                              : const SizedBox()
                        ],
                      ),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          const Icon(CupertinoIcons.person),
                          const SizedBox(width: 5),
                          TextWidget(
                            text: 'Created by:'.tr,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 5),
                          TextWidget(
                            text: controller.task.createdBy?.name ?? '',
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          )
                        ],
                      ),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Icon(CupertinoIcons.person_add),
                          const SizedBox(width: 5),
                          TextWidget(
                            text: 'Assignees:'.tr,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 5),
                          SizedBox(
                            height: 40,
                            width: MediaQuery.of(context).size.width - 200,
                            child: AssigneesWidget(
                                users: controller.task.assignees ?? []),
                          ),
                        ],
                      ),
                      const SizedBox(height: 30),
                      TextWidget(
                        text: 'The task description :'.tr,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 10),
                      TextWidget(
                        text: controller.task.description ?? "",
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                      const SizedBox(height: 30),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          TextWidget(
                            text: 'The task type:'.tr,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 5),
                          TextWidget(
                            text: controller.task.type.toString(),
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ],
                      ),
                      const SizedBox(height: 30),
                      if (controller.task.type == TaskType.recurring)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            const Icon(CupertinoIcons.calendar),
                            const SizedBox(width: 5),
                            TextWidget(
                              text: 'Task repetition:'.tr,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                            const SizedBox(width: 5),
                            TextWidget(
                              text: controller.task.getRecurringTime(),
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ],
                        ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TextWidget(
                            text: 'Subtasks'.tr,
                            fontSize: 14,
                            color: pink,
                          ),
                          IconButton(
                              onPressed: () =>
                                  controller.showSubtaskInput = true,
                              icon: const Icon(Icons.add))
                        ],
                      ),
                      if (controller.showSubtaskInput)
                        TextInput(
                          prefixIcon: const SizedBox(),
                          controller: controller.subTaskController,
                          height: 50,
                          hint: 'Subtask title'.tr,
                          textInputAction: TextInputAction.done,
                          error: controller.subTaskError,
                          onSubmitted: (value) => controller.addSubTask(),
                          suffixIcon: controller.adding
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ))
                              : IconButton(
                                  onPressed: () => controller.addSubTask(),
                                  icon: const Icon(Icons.send)),
                        ),
                      controller.subTasks.isEmpty
                          ? Center(
                              child: TextWidget(
                                text: 'No subtasks'.tr,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            )
                          : _subTasks(context),
                      if (controller.canEdit()) ...[
                        const SizedBox(height: 30),
                        FullWidthButton(
                            text: 'Edit task'.tr,
                            onPressed: () => {
                                  Get.toNamed(AppRoutes.addTask,
                                      arguments: controller.task),
                                })
                      ]
                    ],
                  ),
                )),
          )),
    );
  }

  _subTasks(BuildContext context) {
    return ListView.builder(
        shrinkWrap: true,
        scrollDirection: Axis.vertical,
        itemCount: controller.subTasks.length,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          final item = controller.subTasks[index];
          log(item.status ?? '');
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                height: 10,
              ),
              Container(
                width: MediaQuery.of(context).size.width - 50,
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 1, color: pink)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width / 2,
                      child: TextWidget(
                        text: item.content ?? '',
                        fontSize: 12,
                      ),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          height: 40,
                          width: 85,
                          child: CustomDropdown(
                              border: const UnderlineInputBorder(
                                  borderSide: BorderSide.none),
                              onChanged: (value) =>
                                  controller.updateStatus(item.id!, value),
                              value: item.status,
                              items: [
                                DropdownMenuItem(
                                    value: 'todo',
                                    child: Text(
                                      'To Do'.tr,
                                      style: const TextStyle(fontSize: 10),
                                    )),
                                DropdownMenuItem(
                                    value: 'in_progress',
                                    child: Text(
                                      'In progress'.tr,
                                      style: const TextStyle(fontSize: 10),
                                    )),
                                DropdownMenuItem(
                                    value: 'done',
                                    child: Text(
                                      'Done'.tr,
                                      style: const TextStyle(fontSize: 10),
                                    )),
                              ]),
                        ),
                        const SizedBox(width: 5),
                        item.deleting
                            ? const SizedBox(
                                height: 25,
                                width: 25,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              )
                            : GestureDetector(
                                onTap: () => controller.deleteSubtask(item.id!),
                                child: const Icon(
                                  FontAwesomeIcons.trashCan,
                                  color: Colors.red,
                                  size: 17,
                                )),
                      ],
                    )
                  ],
                ),
              ),
            ],
          );
        });
  }
}
