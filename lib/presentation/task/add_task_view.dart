import 'package:family_management/domain/controllers/add_task_controller.dart';
import 'package:family_management/domain/enums/recurring_pattern.dart';
import 'package:family_management/domain/models/ferquent_task.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/custom_dropdown.dart';
import 'package:family_management/presentation/widget/date_picker.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/multi_select.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:family_management/presentation/widget/text_input_with_suggestions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;
import 'package:multi_dropdown/multi_dropdown.dart';

import '../widget/text_widget.dart';

class AddTaskView extends GetView<AddTaskController> {
  const AddTaskView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              title: 'Add Task'.tr,
              withBackButton: true,
              withDrawer: false),
          body: controller.loading
              ? const Center(
                  child: CircularProgressIndicator(),
                )
              : Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 30),
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child:
                              Obx(() => TextInputWithSuggestions<FerquentTask>(
                                    controller: controller.titleController,
                                    hint: 'Add task name'.tr,
                                    error: controller.titleError,
                                    suggestions: controller.filteredSuggestions,
                                    showSuggestions:
                                        controller.showSuggestions.value,
                                    suggestionBuilder: (task) =>
                                        task.title ?? '',
                                    onSuggestionSelected: (task) =>
                                        controller.selectSuggestion(task),
                                    onChange: (value) =>
                                        controller.onTitleChanged(value),
                                    onSuggestionsDismissed: () =>
                                        controller.hideSuggestions(),
                                  )),
                        ),
                        _buildDatePicker(context),
                        _buildTextInput(controller.descriptionController,
                            'Type the note here'.tr,
                            minLines: 4, maxLines: 10),
                        _buildPriorityDropdown(),
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: MultiSelect(
                              hint: 'Assignees'.tr,
                              controller: controller.multiSelectController,
                              items: controller.members
                                  .map((e) => DropdownItem<String>(
                                      value: e.id.toString(),
                                      label: e.name ?? ''))
                                  .toList()),
                        ),
                        _buildTaskTypeDropdown(),
                        if (controller.selectedTaskType.value == 2) ...[
                          _buildRepetitionDropdown(),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: TextInput(
                              controller:
                                  controller.recurringIntervalController,
                              hint: 'Recurring Interval'.tr,
                            ),
                          )
                        ],
                        const SizedBox(height: 30),
                        FullWidthButton(
                          text: 'Save'.tr,
                          onPressed: () => controller.storeOrUpdate(),
                          loading: controller.adding,
                        )
                      ],
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildTextInput(TextEditingController controller, String hint,
      {IconData? icon, int minLines = 1, int maxLines = 1}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: TextInput(
        controller: controller,
        hint: hint,
        minLines: minLines,
        maxLines: maxLines,
        prefixIcon: icon != null
            ? IconButton(
                onPressed: null,
                icon: Icon(icon, color: Get.theme.primaryColor),
              )
            : const SizedBox.shrink(),
      ),
    );
  }

  Widget _buildDatePicker(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: DatePicker(
        label: 'Add Due Date'.tr,
        value: controller.dueDate,
        onChange: (value) =>
            controller.dueDate = intl.DateFormat('y-M-d').format(value!),
        prefixIcon: IconButton(
          onPressed: null,
          icon: Icon(CupertinoIcons.clock, color: Get.theme.primaryColor),
        ),
      ),
    );
  }

  Widget _buildTaskTypeDropdown() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: CustomDropdown(
        value: controller.selectedTaskType.value.toString(),
        items: [
          DropdownMenuItem(value: '1', child: Text('One-time'.tr)),
          DropdownMenuItem(value: '2', child: Text('Recurring'.tr)),
        ],
        hint: 'Select task type'.tr,
        onChanged: (value) =>
            controller.selectedTaskType.value = int.parse(value as String),
      ),
    );
  }

  Widget _buildRepetitionDropdown() {
    final items = RecurringPattern.values
        .map((e) => DropdownMenuItem<String>(
              value: e.name,
              child: TextWidget(text: e.name.capitalizeFirst ?? ''),
            ))
        .toList();

    final selectedValue = controller.selectedRepetition.value;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: CustomDropdown(
        items: items,
        hint: '+ Add Task repetition'.tr,
        value: items.any((item) => item.value == selectedValue)
            ? selectedValue
            : null,
        onChanged: (value) => controller.selectedRepetition.value = value ?? '',
      ),
    );
  }

  Widget _buildPriorityDropdown() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: CustomDropdown(
        items: [
          DropdownMenuItem(value: 'low', child: Text('Low'.tr)),
          DropdownMenuItem(value: 'medium', child: Text('Medium'.tr)),
          DropdownMenuItem(value: 'high', child: Text('High'.tr)),
        ],
        hint: 'Task Priority'.tr,
        value: controller.selectedPriority.value.isEmpty
            ? null
            : controller.selectedPriority.value,
        onChanged: (value) => controller.selectedPriority.value = value ?? '',
      ),
    );
  }
}
