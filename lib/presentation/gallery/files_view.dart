import 'package:family_management/domain/controllers/files_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/loading_indicator.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class FilesView extends GetView<FilesController> {
  const FilesView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Directionality(
          textDirection: controller.appService.direction,
          child: Scaffold(
            appBar: mainAppBar(
              context: context,
              withDrawer: false,
              withBackButton: true,
              title: controller.folder.name,
            ),
            body: controller.loading
                ? const Center(
                    child: LoadingIndicator(),
                  )
                : controller.media.isEmpty
                    ? Center(
                        child: TextWidget(text: 'No files found'.tr),
                      )
                    : Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 20),
                        child: Column(
                          children: [
                            GridView.builder(
                                shrinkWrap: true,
                                scrollDirection: Axis.vertical,
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                        crossAxisCount: 4,
                                        crossAxisSpacing: 5,
                                        mainAxisSpacing: 5),
                                itemCount: controller.media.length,
                                itemBuilder: (context, index) {
                                  final item = controller.media[index];
                                  return GestureDetector(
                                    onTap: () => controller.download(index),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(
                                          FontAwesomeIcons.file,
                                          size: 35,
                                        ),
                                        const SizedBox(height: 5),
                                        TextWidget(
                                          text: item.fileName ?? '',
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        if (item.downloading)
                                          LinearPercentIndicator(
                                            percent: controller.progress,
                                            progressColor:
                                                Theme.of(context).primaryColor,
                                            barRadius:
                                                const Radius.circular(10),
                                            animation: true,
                                            animateFromLastPercent: true,
                                          )
                                      ],
                                    ),
                                  );
                                })
                          ],
                        ),
                      ),
            floatingActionButton: FloatingActionButton(
              heroTag: "files_fab",
              onPressed: () => controller.pickFromFiles(),
              backgroundColor: Theme.of(context).scaffoldBackgroundColor,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(50)),
              child: controller.uploading
                  ? controller.progress == 1
                      ? const CircularProgressIndicator()
                      : Center(
                          child: CircularPercentIndicator(
                            radius: 20,
                            percent: controller.progress,
                            progressColor: Theme.of(context).primaryColor,
                          ),
                        )
                  : Icon(
                      FontAwesomeIcons.fileCirclePlus,
                      color: Theme.of(context).primaryColor,
                    ),
            ),
          ),
        ));
  }
}
