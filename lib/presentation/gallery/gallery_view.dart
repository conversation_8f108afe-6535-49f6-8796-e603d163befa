import 'package:family_management/domain/controllers/gallery_controller.dart';
import 'package:family_management/presentation/chat/widgets/search_box.dart';
import 'package:family_management/presentation/gallery/album_view.dart';
import 'package:family_management/presentation/gallery/all_media_view.dart';
import 'package:family_management/presentation/gallery/folder_view.dart';
import 'package:family_management/presentation/widget/back_button.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class GalleryView extends GetView<GalleryController> {
  const GalleryView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
            appBar: AppBar(
              automaticallyImplyLeading: false,
              leading: Padding(
                padding: const EdgeInsets.all(10),
                child: CustomBackButton(),
              ),
              title: TextWidget(
                text: 'Gallery'.tr,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
              centerTitle: true,
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(120),
                child: Column(
                  children: [
                    const SizedBox(height: 10),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: SearchBox(),
                    ),
                    const SizedBox(height: 10),
                    TabBar(
                      controller: controller.tabController,
                      tabs: [
                        Tab(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                'assets/svgs/gallery_tab.svg',
                                colorFilter: ColorFilter.mode(
                                  controller.activeIndex == 0
                                      ? Theme.of(context).primaryColor
                                      : Colors.grey,
                                  BlendMode.srcIn,
                                ),
                              ),
                              const SizedBox(width: 5),
                              TextWidget(
                                text: 'The Gallery'.tr,
                                color: controller.activeIndex == 0
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey,
                              ),
                            ],
                          ),
                        ),
                        Tab(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                'assets/svgs/folder_tab.svg',
                                colorFilter: ColorFilter.mode(
                                  controller.activeIndex == 1
                                      ? Theme.of(context).primaryColor
                                      : Colors.grey,
                                  BlendMode.srcIn,
                                ),
                              ),
                              const SizedBox(width: 5),
                              TextWidget(
                                text: 'The Folders'.tr,
                                color: controller.activeIndex == 1
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            body: TabBarView(
              controller: controller.tabController,
              children: [
                DefaultTabController(
                  length: 2,
                  child: SingleChildScrollView(
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom),
                    child: Column(
                      children: [
                        const SizedBox(height: 10),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.all(4),
                          child: TabBar(
                            controller: controller.innerTabController,
                            indicatorColor: Colors.transparent,
                            indicatorSize: TabBarIndicatorSize.tab,
                            labelColor: Colors.white,
                            unselectedLabelColor: Colors.black,
                            dividerColor: Colors.transparent,
                            tabs: [
                              _buildInnerTab(
                                  context,
                                  controller.innerActiveIndex == 0,
                                  'All media'.tr),
                              _buildInnerTab(
                                  context,
                                  controller.innerActiveIndex == 1,
                                  'Albums'.tr),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: MediaQuery.of(context).size.height - 300,
                          child: TabBarView(
                            controller: controller.innerTabController,
                            children: const [
                              AllMediaView(),
                              AlbumView(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const FolderView(),
              ],
            )),
      ),
    );
  }

  _buildInnerTab(BuildContext context, bool isActive, String text) {
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isActive ? Theme.of(context).primaryColor : Colors.grey,
      ),
      child: Text(text),
    );
  }
}
