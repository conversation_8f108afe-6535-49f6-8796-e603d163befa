import 'package:family_management/domain/controllers/gallery_controller.dart';
import 'package:family_management/presentation/gallery/widgets/media_list.dart';
import 'package:family_management/presentation/gallery/widgets/upload_media_dialog.dart';
import 'package:family_management/presentation/widget/loading_indicator.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class AllMediaView extends GetView<GalleryController> {
  const AllMediaView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        body: controller.loading
            ? const Center(child: LoadingIndicator())
            : controller.media.isEmpty
                ? Center(
                    child: TextWidget(
                      text: 'No media found'.tr,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 20),
                    child: <PERSON><PERSON>iew(
                      controller: controller.scrollController,
                      children: [
                        MediaList(media: controller.media),
                        // Loading indicator at the bottom
                        Obx(() => controller.isLoadingMore
                            ? const Padding(
                                padding: EdgeInsets.all(16.0),
                                child:
                                    Center(child: CircularProgressIndicator()),
                              )
                            : const SizedBox.shrink()),
                      ],
                    ),
                  ),
        floatingActionButton: FloatingActionButton(
          heroTag: "all_media_fab",
          onPressed: () => Get.dialog(const UploadMediaDialog()),
          backgroundColor: Theme.of(context).primaryColor,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
          child: SvgPicture.asset(
            'assets/svgs/add_picture.svg',
            colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
          ),
        ),
      ),
    );
  }
}
