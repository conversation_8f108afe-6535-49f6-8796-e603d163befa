import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../domain/controllers/gallery_controller.dart';

class DeleteSimilarPhotosView extends GetView<GalleryController> {
  const DeleteSimilarPhotosView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: mainAppBar(
        context: context,
        withDrawer: false,
        withBackButton: true,
        title: 'Similar Photos'.tr,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 30),
            Center(
              child: Container(
                height: 250,
                width: 250,
                decoration: BoxDecoration(
                  border: Border.all(
                    width: 10,
                    color: Theme.of(context).primaryColor,
                  ),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: TextWidget(
                    text: '45 %'.tr,
                    fontSize: 50,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 30),
            Center(
              child: TextWidget(
                text:
                    'LoremspamLoremspamLoremspamLoremspamL orems pamvLoremspamLoremspam'
                        .tr,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 60),
            FullWidthButton(
              text: 'Go to home'.tr,
              onPressed: () => {},
            ),
          ],
        ),
      ),
    );
  }
}
