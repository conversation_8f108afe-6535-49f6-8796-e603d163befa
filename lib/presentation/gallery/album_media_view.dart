import 'package:family_management/domain/controllers/album_controller.dart';
import 'package:family_management/presentation/gallery/widgets/media_list.dart';
import 'package:family_management/presentation/gallery/widgets/media_picker.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/loading_indicator.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class AlbumMediaView extends GetView<AlbumController> {
  const AlbumMediaView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          floatingActionButton: FloatingActionButton(
            heroTag: "album_media_fab",
            onPressed: () => Get.dialog(const MediaPicker()),
            backgroundColor: Colors.white,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
            child: SvgPicture.asset(
              'assets/svgs/add_picture.svg',
            ),
          ),
          appBar: mainAppBar(
              context: context, withDrawer: false, withBackButton: true),
          body: controller.loadingMedia
              ? const Center(
                  child: LoadingIndicator(),
                )
              : controller.media.isEmpty
                  ? Center(
                      child: TextWidget(
                      text: 'No media found'.tr,
                      fontWeight: FontWeight.bold,
                    ))
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: MediaList(media: controller.media),
                    ),
        )));
  }
}
