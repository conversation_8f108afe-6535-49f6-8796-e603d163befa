import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/upload_media_controller.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class UploadMediaDialog extends StatelessWidget {
  const UploadMediaDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(UploadMediaController());

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextWidget(
                    text: 'Upload Image'.tr,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Album Selection Section
              TextWidget(
                text: 'Select Album'.tr,
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),

              const SizedBox(height: 12),

              // Album Dropdown with Search
              Obx(() => Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        // Search Field
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          child: TextField(
                            controller: controller.searchController,
                            onChanged: controller.onSearchChanged,
                            decoration: InputDecoration(
                              hintText: 'Search albums...'.tr,
                              border: InputBorder.none,
                              prefixIcon: const Icon(Icons.search, size: 20),
                              suffixIcon: controller
                                      .searchController.text.isNotEmpty
                                  ? IconButton(
                                      onPressed: controller.clearSearch,
                                      icon: const Icon(Icons.clear, size: 20),
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                    )
                                  : null,
                            ),
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),

                        // Divider
                        if (controller.filteredAlbums.isNotEmpty)
                          Divider(height: 1, color: Colors.grey[300]),

                        // Albums List
                        if (controller.loadingAlbums)
                          const Padding(
                            padding: EdgeInsets.all(16),
                            child: Center(
                              child: CupertinoActivityIndicator(),
                            ),
                          )
                        else if (controller.filteredAlbums.isEmpty)
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Center(
                              child: TextWidget(
                                text:
                                    controller.searchController.text.isNotEmpty
                                        ? 'No albums found'.tr
                                        : 'No albums available'.tr,
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          )
                        else
                          Container(
                            constraints: const BoxConstraints(maxHeight: 200),
                            child: ListView.builder(
                              shrinkWrap: true,
                              itemCount: controller.filteredAlbums.length,
                              itemBuilder: (context, index) {
                                final album = controller.filteredAlbums[index];
                                final isSelected =
                                    controller.selectedAlbum?.id == album.id;

                                return InkWell(
                                  onTap: () => controller.selectAlbum(album),
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? primarySwatch.withAlpha(30)
                                          : Colors.transparent,
                                    ),
                                    child: Row(
                                      children: [
                                        // Album Icon
                                        Container(
                                          width: 32,
                                          height: 32,
                                          decoration: BoxDecoration(
                                            color: primarySwatch.withAlpha(30),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Icon(
                                            Icons.photo_album,
                                            size: 18,
                                            color: primarySwatch,
                                          ),
                                        ),

                                        const SizedBox(width: 12),

                                        // Album Name
                                        Expanded(
                                          child: TextWidget(
                                            text: album.name ?? '',
                                            fontSize: 14,
                                            fontWeight: isSelected
                                                ? FontWeight.w600
                                                : FontWeight.normal,
                                            color: isSelected
                                                ? primarySwatch
                                                : Colors.grey[800],
                                          ),
                                        ),

                                        // Selection Indicator
                                        if (isSelected)
                                          Icon(
                                            Icons.check_circle,
                                            size: 20,
                                            color: primarySwatch,
                                          ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                      ],
                    ),
                  )),

              const SizedBox(height: 24),

              // Upload Button
              Obx(() => SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: controller.selectedAlbum != null &&
                              !controller.uploading
                          ? controller.showImagePicker
                          : null,
                      icon: controller.uploading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : SvgPicture.asset(
                              'assets/svgs/add_picture.svg',
                              width: 20,
                              height: 20,
                              colorFilter: const ColorFilter.mode(
                                Colors.white,
                                BlendMode.srcIn,
                              ),
                            ),
                      label: TextWidget(
                        text: controller.uploading
                            ? 'Uploading...'.tr
                            : 'Upload Image'.tr,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: controller.selectedAlbum != null
                            ? primarySwatch
                            : Colors.grey[400],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  )),
            ],
          ),
        ),
      ),
    );
  }
}
