import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:family_management/domain/controllers/album_controller.dart';
import 'package:family_management/domain/models/media.dart';
import 'package:family_management/presentation/gallery/widgets/image_preview.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class MediaList extends GetView<AlbumController> {
  final List<Media> media;
  const MediaList({super.key, required this.media});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (controller.enableDeleteMedia)
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                    onPressed: () => controller.cancelDelete(),
                    child: TextWidget(text: 'cancel'.tr)),
                controller.deletingMedia
                    ? const SizedBox(
                        width: 10,
                        height: 10,
                        child: CircularProgressIndicator(
                          color: Colors.red,
                          strokeWidth: 1,
                        ),
                      )
                    : TextButton(
                        onPressed: () => controller.deleteMedia(),
                        child: TextWidget(
                          text: 'delete'.tr,
                          color: Colors.red,
                        )),
              ],
            ),
          const SizedBox(height: 10),
          GetBuilder<AlbumController>(builder: (controller) {
            return SizedBox(
              child: GridView.builder(
                shrinkWrap: true,
                scrollDirection: Axis.vertical,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio: 0.9,
                    crossAxisSpacing: 3,
                    mainAxisSpacing: 3),
                itemCount: media.length,
                itemBuilder: (context, index) {
                  final item = media[index];
                  return item.filePath != null
                      ? GestureDetector(
                          onTap: () {
                            if (controller.progress < 1.0) {
                              return; // Don't open if still uploading
                            }
                            Get.to(
                              () => ImagePreview(
                                media: item,
                                allMedia: media,
                                initialIndex: index,
                              ),
                              transition: Transition.fadeIn,
                            );
                          },
                          child: Stack(
                            children: [
                              Hero(
                                tag: 'media_${item.uuid ?? index}',
                                child: Image.file(File(item.filePath!),
                                    fit: BoxFit.cover),
                              ),
                              Container(
                                alignment: Alignment.bottomCenter,
                                decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 0.5)),
                                child: LinearPercentIndicator(
                                  percent: controller.progress,
                                  backgroundColor: Colors.grey,
                                  progressBorderColor: Colors.white,
                                  barRadius: const Radius.circular(10),
                                  animation: true,
                                  animateToInitialPercent: false,
                                ),
                              )
                            ],
                          ),
                        )
                      : GestureDetector(
                          onLongPress: () {
                            controller.enableDeleteMedia = true;
                            controller.select(item.id!);
                          },
                          onTap: () {
                            if (!controller.enableDeleteMedia) {
                              Get.to(
                                () => ImagePreview(
                                  media: item,
                                  allMedia: media,
                                  initialIndex: index,
                                ),
                                transition: Transition.fadeIn,
                              );
                            } else {
                              controller.select(item.id!);
                            }
                          },
                          child: Stack(
                            children: [
                              Positioned(
                                top: 0,
                                bottom: 0,
                                right: 0,
                                left: 0,
                                child: Hero(
                                  tag: 'media_${item.id}',
                                  child: Container(
                                      clipBehavior: Clip.antiAliasWithSaveLayer,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: CachedNetworkImage(
                                        imageUrl: item.originalUrl!,
                                        fit: BoxFit.cover,
                                      )),
                                ),
                              ),
                              if (controller.enableDeleteMedia)
                                Positioned(
                                    child: Checkbox(
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(50),
                                            side: BorderSide.none),
                                        fillColor: WidgetStateProperty.all(
                                            Colors.white),
                                        checkColor:
                                            Theme.of(context).primaryColor,
                                        side: BorderSide.none,
                                        value: controller.deleteIds
                                            .contains(item.id!),
                                        onChanged: (value) =>
                                            controller.select(item.id!)))
                            ],
                          ),
                        );
                },
              ),
            );
          }),
        ],
      ),
    );
  }
}
