import 'package:cached_network_image/cached_network_image.dart';
import 'package:family_management/domain/models/media.dart';
import 'package:family_management/presentation/widget/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ImagePreview extends StatefulWidget {
  final Media media;
  final List<Media> allMedia;
  final int initialIndex;

  const ImagePreview({
    Key? key,
    required this.media,
    required this.allMedia,
    required this.initialIndex,
  }) : super(key: key);

  @override
  State<ImagePreview> createState() => _ImagePreviewState();
}

class _ImagePreviewState extends State<ImagePreview> {
  late PageController _pageController;
  late int _currentIndex;
  bool _isZoomed = false;
  final TransformationController _transformationController =
      TransformationController();

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    _transformationController.dispose();
    super.dispose();
  }

  void _resetZoom() {
    _transformationController.value = Matrix4.identity();
    setState(() {
      _isZoomed = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Get.back(),
        ),
        // title: TextWidget(
        //   text: '${_currentIndex + 1}/${widget.allMedia.length}',
        //   color: Colors.white,
        // ),
        // actions: [
        //   IconButton(
        //     icon: const Icon(Icons.share, color: Colors.white),
        //     onPressed: () {
        //       // Implement share functionality
        //     },
        //   ),
        //   IconButton(
        //     icon: const Icon(Icons.download_outlined, color: Colors.white),
        //     onPressed: () {
        //       // Implement download functionality
        //     },
        //   ),
        // ],
      ),
      body: PageView.builder(
        controller: _pageController,
        itemCount: widget.allMedia.length,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
            _resetZoom();
          });
        },
        itemBuilder: (context, index) {
          final media = widget.allMedia[index];
          return GestureDetector(
            onTap: () {
              if (_isZoomed) {
                _resetZoom();
              }
            },
            child: Center(
              child: InteractiveViewer(
                transformationController: _transformationController,
                minScale: 0.5,
                maxScale: 4.0,
                onInteractionEnd: (details) {
                  setState(() {
                    _isZoomed =
                        _transformationController.value != Matrix4.identity();
                  });
                },
                child: Hero(
                  tag: 'media_${media.id}',
                  child: CachedNetworkImage(
                    imageUrl: media.originalUrl ?? '',
                    fit: BoxFit.contain,
                    placeholder: (context, url) => const Center(
                      child: LoadingIndicator(),
                    ),
                    errorWidget: (context, url, error) => const Center(
                      child: Icon(Icons.error, color: Colors.white),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
