import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/folder_controller.dart';
import 'package:family_management/presentation/widget/loading_indicator.dart';
import 'package:family_management/presentation/widget/multi_select.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

import '../widget/full_width_button.dart';
import '../widget/text_input.dart';

class FolderView extends GetView<FolderController> {
  const FolderView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.direction,
        child: Scaffold(
          body: controller.loading
              ? const Center(child: LoadingIndicator())
              : controller.folders.isEmpty
                  ? Center(
                      child: TextWidget(
                        text: 'No folders found'.tr,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 20),
                      child: GridView.builder(
                        shrinkWrap: true,
                        scrollDirection: Axis.vertical,
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 4,
                          childAspectRatio: 1,
                          crossAxisSpacing: 5,
                          mainAxisSpacing: 5,
                        ),
                        itemCount: controller.folders.length,
                        itemBuilder: (context, index) {
                          final item = controller.folders[index];
                          return GestureDetector(
                            onTap: () =>
                                Get.toNamed(AppRoutes.files, arguments: item),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  height: 35,
                                  width: 35,
                                  'assets/svgs/folder_tab.svg',
                                ),
                                const SizedBox(height: 10),
                                TextWidget(
                                  text: item.name ?? '',
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                )
                              ],
                            ),
                          );
                        },
                      ),
                    ),
          floatingActionButton: FloatingActionButton(
            heroTag: "folder_fab",
            onPressed: () => _showPopup(context),
            backgroundColor: Colors.white,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
            child: Icon(
              Icons.create_new_folder_outlined,
              size: 30,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      ),
    );
  }

  void _showPopup(BuildContext context) {
    Get.dialog(Obx(
      () => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SvgPicture.asset(
                  'assets/svgs/gallery_tab.svg',
                  colorFilter: ColorFilter.mode(
                    Theme.of(context).primaryColor,
                    BlendMode.srcIn,
                  ),
                  width: 24,
                  height: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextWidget(
                    text: 'Create new folder :'.tr,
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: TextInput(
                hint: 'Enter name folder'.tr,
                controller: controller.nameController,
                error: controller.nameError,
              ),
            ),

            const SizedBox(height: 16),

            // Assignees Section
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.people_outline,
                        color: Theme.of(context).primaryColor,
                        size: 18,
                      ),
                      const SizedBox(width: 6),
                      TextWidget(
                        text: 'Assign to Family Members'.tr,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  TextWidget(
                    text:
                        'Select family members who can access this folder (required)'
                            .tr,
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(height: 12),
                  Obx(() => controller.loadingMembers
                      ? const Center(
                          child: Padding(
                            padding: EdgeInsets.all(12),
                            child: SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ),
                        )
                      : MultiSelect(
                          hint: 'Select assignees (required)'.tr,
                          controller: controller.multiSelectController,
                          items: controller.members
                              .map((member) => DropdownItem<String>(
                                  value: member.id.toString(),
                                  label: member.name ?? ''))
                              .toList(),
                        )),

                  // Show assignees error
                  if (controller.assigneesError.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    TextWidget(
                      text: controller.assigneesError,
                      color: Colors.red,
                      fontSize: 12,
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 16),

            FullWidthButton(
              text: 'Create folder'.tr,
              onPressed: () => controller.create(),
              loading: controller.creating,
            ),
          ],
        ),
      ),
    ));
  }
}
