import 'package:cached_network_image/cached_network_image.dart';
import 'package:family_management/config/app_routes.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../domain/controllers/gallery_controller.dart';

class SimilarPhotosView extends GetView<GalleryController> {
  const SimilarPhotosView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.direction,
        child: Scaffold(
          appBar: mainAppBar(
            context: context,
            withDrawer: false,
            withBackButton: true,
            title: 'Delete Photos'.tr,
          ),
          body: controller.loading
              ? const Center(
                  child: LoadingIndicator(),
                )
              : Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 30),
                      Text(
                        controller.media.length.toString() + 'photos'.tr,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: 20),
                      SizedBox(
                        child: GridView.builder(
                          shrinkWrap: true,
                          scrollDirection: Axis.vertical,
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 3,
                            mainAxisSpacing: 3,
                            childAspectRatio: 1,
                          ),
                          itemCount: controller.media.length,
                          itemBuilder: (context, index) {
                            final item = controller.media[index];
                            return Container(
                              clipBehavior: Clip.antiAliasWithSaveLayer,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: CachedNetworkImage(
                                imageUrl: item.originalUrl!,
                                fit: BoxFit.cover,
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 20),
                      FullWidthButton(
                        text: 'Delete selected'.tr,
                        onPressed: () =>
                            Get.toNamed(AppRoutes.deleteSimilarPhoto),
                      ),
                    ],
                  ),
                ),
        ),
      ),
    );
  }
}
