import 'package:cached_network_image/cached_network_image.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/album_controller.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/loading_indicator.dart';
import 'package:family_management/presentation/widget/multi_select.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

import '../widget/text_input.dart';
import '../widget/text_widget.dart';

class AlbumView extends GetView<AlbumController> {
  const AlbumView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        resizeToAvoidBottomInset: true,
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: controller.loading
              ? const Center(child: LoadingIndicator())
              : controller.albums.isEmpty
                  ? const Center(child: Text('No Albums'))
                  : SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          GridView.builder(
                            scrollDirection: Axis.vertical,
                            physics: const NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 2,
                                    childAspectRatio: 1,
                                    crossAxisSpacing: 10),
                            itemCount: controller.albums.length,
                            itemBuilder: (context, index) {
                              final item = controller.albums[index];
                              return GestureDetector(
                                onTap: () => controller.initMedia(item.id!),
                                child: Column(
                                  children: [
                                    Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        Transform.rotate(
                                          angle: 25 * (3.1415926535 / 180),
                                          child: Container(
                                            height: 100,
                                            width: 75,
                                            decoration: BoxDecoration(
                                              border: Border.all(
                                                width: 1,
                                                color: Theme.of(context)
                                                    .primaryColor,
                                              ),
                                              color: lightBlue,
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                          ),
                                        ),
                                        Transform.rotate(
                                          angle: 330 * (3.1415926535 / 180),
                                          child: Container(
                                            height: 100,
                                            width: 75,
                                            decoration: BoxDecoration(
                                              border: Border.all(
                                                width: 1,
                                                color: Theme.of(context)
                                                    .primaryColor,
                                              ),
                                              color: lightBlue,
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                          ),
                                        ),
                                        Container(
                                          height: 120,
                                          width: 90,
                                          clipBehavior:
                                              Clip.antiAliasWithSaveLayer,
                                          decoration: BoxDecoration(
                                            color: Theme.of(context)
                                                .scaffoldBackgroundColor,
                                            border: Border.all(
                                              color: Theme.of(context)
                                                  .primaryColor,
                                              width: 1,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          child: SizedBox(
                                            width: 30,
                                            height: 30,
                                            child: item.thumbnail != null
                                                ? CachedNetworkImage(
                                                    imageUrl: item.thumbnail!,
                                                    fit: BoxFit.cover)
                                                : SvgPicture.asset(
                                                    'assets/svgs/album.svg',
                                                    width: 25,
                                                    height: 25,
                                                    fit: BoxFit.contain,
                                                  ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 10),
                                    TextWidget(
                                      text: item.name ?? '',
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    )
                                  ],
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
        ),
        floatingActionButton: FloatingActionButton(
          heroTag: "album_fab",
          onPressed: () => _showPopup(context),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
          backgroundColor: Colors.white,
          child: Icon(
            Icons.add,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ),
    );
  }

  void _showPopup(BuildContext context) {
    Get.dialog(Obx(
      () => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SvgPicture.asset(
                  'assets/svgs/gallery_tab.svg',
                  colorFilter: ColorFilter.mode(
                    Theme.of(context).primaryColor,
                    BlendMode.srcIn,
                  ),
                  width: 24,
                  height: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextWidget(
                    text: 'Create new album :'.tr,
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: TextInput(
                hint: 'Enter name album'.tr,
                controller: controller.nameController,
                error: controller.nameError,
              ),
            ),

            const SizedBox(height: 16),

            // Assignees Section
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.people_outline,
                        color: Theme.of(context).primaryColor,
                        size: 18,
                      ),
                      const SizedBox(width: 6),
                      TextWidget(
                        text: 'Assign to Family Members'.tr,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  TextWidget(
                    text:
                        'Select family members who can access this album (required)'
                            .tr,
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(height: 12),
                  Obx(() => controller.loadingMembers
                      ? const Center(
                          child: Padding(
                            padding: EdgeInsets.all(12),
                            child: SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ),
                        )
                      : MultiSelect(
                          hint: 'Select assignees (required)'.tr,
                          controller: controller.multiSelectController,
                          items: controller.members
                              .map((member) => DropdownItem<String>(
                                  value: member.id.toString(),
                                  label: member.name ?? ''))
                              .toList(),
                        )),

                  // Show assignees error
                  if (controller.assigneesError.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    TextWidget(
                      text: controller.assigneesError,
                      color: Colors.red,
                      fontSize: 12,
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 16),

            FullWidthButton(
              text: 'Create album'.tr,
              onPressed: () => controller.create(),
              loading: controller.creating,
            ),
          ],
        ),
      ),
    ));
  }
}
