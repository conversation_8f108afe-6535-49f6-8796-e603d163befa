import 'package:family_management/domain/controllers/editeprofile_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../widget/icon_widget.dart';
import '../widget/text_input.dart';

class EditprofileView extends GetView<EditeProFileController> {
  const EditprofileView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              withBackButton: true,
              withDrawer: false,
              title: 'Edit your info'.tr),
          body: Column(
            children: [
              const SizedBox(height: 60),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    children: [
                      TextInput(
                        hint: controller.user.name,
                        controller: controller.nameController,
                        prefixIcon: SvgPicture.asset(
                            'assets/svgs/profile_svgrepo.com.svg'),
                      ),
                      const SizedBox(height: 10),
                      TextInput(
                        hint: 'Enter new password'.tr,
                        controller: controller.passwordController,
                        prefixIcon: const IconWidget(icon: Icons.lock_outlined),
                        isPassword: true,
                        error: controller.passwordError,
                      ),
                      const SizedBox(height: 10),
                      TextInput(
                        hint: 'Confirm new password'.tr,
                        controller: controller.confirmPasswordController,
                        prefixIcon: const IconWidget(icon: Icons.lock_outlined),
                        isPassword: true,
                      ),
                      const SizedBox(height: 10),
                      TextInput(
                        hint: controller.user.bio ?? "Enter you bio here".tr,
                        controller: controller.bioController,
                        prefixIcon: SvgPicture.asset(
                            'assets/svgs/text-files_svgrepo.com.svg'),
                        minLines: 1,
                        maxLines: 5,
                      ),
                      const SizedBox(height: 30),
                      FullWidthButton(
                        text: 'Sumbit'.tr,
                        onPressed: () => controller.submit(),
                        loading: controller.loading,
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
