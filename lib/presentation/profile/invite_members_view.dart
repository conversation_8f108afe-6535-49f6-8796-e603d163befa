import 'package:family_management/domain/controllers/invite_members_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/full_width_button.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class InviteMembersView extends GetView<InviteMembersController> {
  const InviteMembersView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
            context: context,
            title: 'Invite Members'.tr,
            withBackButton: true,
            withDrawer: false,
          ),
          body: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  text: 'Enter email addresses to invite family members'.tr,
                  color: Colors.grey[700],
                  fontSize: 16,
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: TextInput(
                        controller: controller.emailController,
                        hint: 'Email address'.tr,
                        prefixIcon: const Icon(Icons.email_outlined),
                        textInputAction: TextInputAction.done,
                        onSubmitted: (_) => controller.addEmail(),
                      ),
                    ),
                    const SizedBox(width: 10),
                    ElevatedButton(
                      onPressed: controller.addEmail,
                      style: ElevatedButton.styleFrom(
                        shape: const CircleBorder(),
                        padding: const EdgeInsets.all(12),
                      ),
                      child: const Icon(Icons.add),
                    ),
                  ],
                ),
                if (controller.errorMessage.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: TextWidget(
                      text: controller.errorMessage.value,
                      color: Colors.red,
                      fontSize: 12,
                    ),
                  ),
                const SizedBox(height: 20),
                Obx(() => controller.emails.isEmpty
                    ? Center(
                        child: TextWidget(
                          text: 'No emails added yet'.tr,
                          color: Colors.grey,
                        ),
                      )
                    : Expanded(
                        child: _buildEmailsList(),
                      )),
                const SizedBox(height: 20),
                FullWidthButton(
                  text: 'Send Invitations'.tr,
                  onPressed: controller.inviteMembers,
                  loading: controller.isLoading.value,
                  disabled: !controller.canInvite,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmailsList() {
    return ListView.builder(
      itemCount: controller.emails.length,
      itemBuilder: (context, index) {
        final email = controller.emails[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: const CircleAvatar(
              child: Icon(Icons.email),
            ),
            title: TextWidget(
              text: email,
              color: Colors.black87,
              fontSize: 13,
            ),
            trailing: IconButton(
              icon: const Icon(Icons.close),
              onPressed: () => controller.removeEmail(email),
            ),
          ),
        );
      },
    );
  }
}
