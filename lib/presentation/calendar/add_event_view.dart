import 'package:family_management/domain/controllers/add_event_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/button.dart';
import 'package:family_management/presentation/widget/custom_dropdown.dart';
import 'package:family_management/presentation/widget/text_input.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class AddEventView extends GetView<AddEventController> {
  const AddEventView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
          textDirection: controller.appService.locale == const Locale('ar')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Scaffold(
            appBar: mainAppBar(
                context: context,
                title: 'Add event'.tr,
                withBackButton: true,
                withDrawer: false),
            body: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 30),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: TextInput(
                          hint: 'Event name'.tr,
                          controller: controller.nameController,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: TextInput(
                          controller: controller.descriptionController,
                          hint: 'Description'.tr,
                          minLines: 3,
                          maxLines: 10,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: TextInput(
                          controller: controller.locationController,
                          hint: 'Location'.tr,
                          minLines: 3,
                          maxLines: 10,
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      TextWidget(text: 'Scheduled at'.tr),
                      const SizedBox(
                        height: 5,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          GestureDetector(
                            onTap: () => controller.selectDate(context),
                            child: Container(
                              width: MediaQuery.of(context).size.width / 1.9,
                              padding: const EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 20),
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      width: 1,
                                      color: Theme.of(context).primaryColor),
                                  borderRadius: BorderRadius.circular(25)),
                              child: TextWidget(
                                text: controller.date.isNotEmpty
                                    ? _formatDisplayDate(controller.date)
                                    : 'Select Date'.tr,
                                maxLines: 1,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () => controller.selectTime(context),
                            child: Container(
                              width: MediaQuery.of(context).size.width / 3,
                              padding: const EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 20),
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      width: 1,
                                      color: Theme.of(context).primaryColor),
                                  borderRadius: BorderRadius.circular(25)),
                              child: TextWidget(
                                text: controller.time.isNotEmpty
                                    ? _formatDisplayTime(controller.time)
                                    : 'Select Time'.tr,
                                color: Colors.grey,
                              ),
                            ),
                          )
                        ],
                      ),
                      const SizedBox(height: 10),
                      TextWidget(text: 'Event type'.tr),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        child: CustomDropdown(
                          items: [
                            DropdownMenuItem(
                              value: 'one_time',
                              child: Text('One time'.tr),
                            ),
                            DropdownMenuItem(
                              value: 'recurring',
                              child: Text('Recurring'.tr),
                            ),
                          ],
                          hint: 'Event type'.tr,
                          value: controller.eventType,
                          onChanged: (String? value) {
                            if (value != null) {
                              controller.eventType = value;
                            }
                          },
                        ),
                      ),
                      const SizedBox(height: 10),
                      if (controller.eventType == 'recurring')
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(text: 'Recurring pattern'.tr),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: CustomDropdown(
                                  value: controller.recurringPattern,
                                  onChanged: (value) {
                                    if (value != null) {
                                      controller.recurringPattern = value;
                                    }
                                  },
                                  items: [
                                    DropdownMenuItem(
                                      value: 'daily',
                                      child: Text('Daily'.tr),
                                    ),
                                    DropdownMenuItem(
                                      value: 'weekly',
                                      child: Text('Weekly'.tr),
                                    ),
                                    DropdownMenuItem(
                                      value: 'monthly',
                                      child: Text('Monthly'.tr),
                                    ),
                                  ]),
                            )
                          ],
                        ),
                      const SizedBox(height: 10),
                      TextWidget(text: 'Remind users before'.tr),
                      const SizedBox(height: 5),
                      CustomDropdown(
                          value: controller.notifyTime,
                          onChanged: (value) {
                            if (value != null) {
                              controller.notifyTime = value;
                            }
                          },
                          items: [
                            DropdownMenuItem(
                              value: '15m',
                              child: Text('15 minutes'.tr),
                            ),
                            DropdownMenuItem(
                              value: '30m',
                              child: Text('30 minutes'.tr),
                            ),
                            DropdownMenuItem(
                              value: '1h',
                              child: Text('1 hour'.tr),
                            ),
                          ]),
                      const SizedBox(height: 10),
                      TextWidget(text: 'Occasion type'.tr),
                      const SizedBox(height: 5),
                      CustomDropdown(
                          value: controller.occasionId,
                          onChanged: (value) {
                            if (value != null) {
                              controller.occasionId = value;
                            }
                          },
                          items: controller.occasions
                              .map(
                                (element) => DropdownMenuItem(
                                  value: element.id.toString(),
                                  child: TextWidget(text: element.name ?? ''),
                                ),
                              )
                              .toList()),
                      const SizedBox(
                        height: 20,
                      ),
                      Button(
                        text: controller.event != null
                            ? 'Update'.tr
                            : 'Create'.tr,
                        onPressed: () => controller.storeOrUpdate(),
                        loading: controller.loading,
                        width: MediaQuery.of(context).size.width,
                        padding: const EdgeInsets.symmetric(vertical: 10),
                      )
                    ],
                  ),
                )),
          )),
    );
  }

  String _formatDisplayDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return intl.DateFormat('MMM dd, yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }

  String _formatDisplayTime(String timeString) {
    try {
      final parts = timeString.split(':');
      final hour = int.parse(parts[0]);
      final minute = int.parse(parts[1]);
      final time = TimeOfDay(hour: hour, minute: minute);
      final now = DateTime.now();
      final dateTime =
          DateTime(now.year, now.month, now.day, time.hour, time.minute);
      return intl.DateFormat('h:mm a').format(dateTime);
    } catch (e) {
      return timeString;
    }
  }
}
