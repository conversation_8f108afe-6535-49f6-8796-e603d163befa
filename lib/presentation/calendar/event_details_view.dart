import 'package:cached_network_image/cached_network_image.dart';
import 'package:family_management/config/app_routes.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/event_details_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' hide TextDirection;

class EventDetailsView extends GetView<EventDetailsController> {
  const EventDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          backgroundColor: const Color(0xFFF5F7FA),
          appBar: mainAppBar(
            context: context,
            title: "Event details".tr,
            withBackButton: true,
            withDrawer: false,
            actions: [
              IconButton(
                icon: const Icon(
                  FontAwesomeIcons.trash,
                  size: 18,
                  color: Colors.red,
                ),
                onPressed: () => _showDeleteDialog(context),
                tooltip: 'Delete event'.tr,
              ),
              IconButton(
                icon: Icon(
                  FontAwesomeIcons.penToSquare,
                  size: 18,
                  color: Theme.of(context).primaryColor,
                ),
                onPressed: () => Get.toNamed(
                  AppRoutes.addEvent,
                  arguments: controller.event,
                ),
                tooltip: 'Edit event'.tr,
              )
            ],
          ),
          body: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildEventHeader(context),
                _buildEventDetails(context),
                if (controller.event.description != null &&
                    controller.event.description!.isNotEmpty)
                  _buildEventDescription(context),
              ],
            ),
          ),
        )));
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Obx(
        () => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 10,
          backgroundColor: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with icon
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(30),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: SvgPicture.asset(
                      'assets/svgs/delet.svg',
                      height: 40,
                      colorFilter: const ColorFilter.mode(
                        Colors.red,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Title
                TextWidget(
                  text: "Delete Event".tr,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),

                const SizedBox(height: 16),

                // Message
                TextWidget(
                  text: "Are you sure you want to delete this event?".tr,
                  fontSize: 16,
                  color: Colors.grey[600],
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 24),

                // Buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Cancel button
                    OutlinedButton(
                      onPressed: () => Get.back(),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.grey[700],
                        side: BorderSide(color: Colors.grey[300]!),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                      child: TextWidget(
                        text: "Cancel".tr,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),

                    // Delete button
                    ElevatedButton(
                      onPressed: controller.deleting
                          ? null
                          : () => controller.deleteEvent(),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: Colors.red,
                        disabledBackgroundColor: Colors.red.withAlpha(150),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                      child: controller.deleting
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : TextWidget(
                              text: "Delete".tr,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEventHeader(BuildContext context) {
    final event = controller.event;
    final isPassed = event.isPassed;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            isPassed ? green.withAlpha(200) : pink.withAlpha(200),
            isPassed ? green.withAlpha(150) : pink.withAlpha(150),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status badge
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha(50),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isPassed
                          ? FontAwesomeIcons.check
                          : FontAwesomeIcons.clock,
                      size: 12,
                      color: Colors.white,
                    ),
                    const SizedBox(width: 6),
                    TextWidget(
                      text: isPassed ? 'Completed'.tr : 'Upcoming'.tr,
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Event name
              TextWidget(
                text: event.name ?? '',
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),

              const SizedBox(height: 8),

              // Event type
              Row(
                children: [
                  Icon(
                    event.type.toString().contains('recurring')
                        ? FontAwesomeIcons.repeat
                        : FontAwesomeIcons.calendarDay,
                    size: 14,
                    color: Colors.white.withAlpha(200),
                  ),
                  const SizedBox(width: 8),
                  TextWidget(
                    text: event.type.toString().contains('recurring')
                        ? 'Recurring Event'.tr
                        : 'One-time Event'.tr,
                    fontSize: 14,
                    color: Colors.white.withAlpha(200),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Date and time
              Row(
                children: [
                  _buildHeaderInfoItem(
                    FontAwesomeIcons.calendar,
                    DateFormat('EEE, MMM d, yyyy').format(event.scheduledAt!),
                  ),
                  const SizedBox(width: 24),
                  _buildHeaderInfoItem(
                    FontAwesomeIcons.clock,
                    DateFormat('h:mm a').format(event.scheduledAt!),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderInfoItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: Colors.white,
        ),
        const SizedBox(width: 8),
        TextWidget(
          text: text,
          fontSize: 14,
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ],
    );
  }

  Widget _buildEventDetails(BuildContext context) {
    final event = controller.event;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            text: 'Event Details'.tr,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
          const SizedBox(height: 20),

          // Occasion
          if (event.occasion != null)
            _buildDetailItem(
              'Occasion'.tr,
              event.occasion?.name ?? '',
              FontAwesomeIcons.gift,
              pink,
              image: event.occasion?.image,
            ),

          // Location
          if (event.location != null && event.location!.isNotEmpty)
            _buildDetailItem(
              'Location'.tr,
              event.location!,
              FontAwesomeIcons.locationDot,
              Colors.blue,
            ),

          // Notification
          _buildDetailItem(
            'Notification'.tr,
            'Notify ${event.getNotifyTime()} before'.tr,
            FontAwesomeIcons.bell,
            Colors.orange,
          ),

          // Created by
          if (event.createdBy != null)
            _buildDetailItem(
              'Created by'.tr,
              event.createdBy?.name ?? '',
              FontAwesomeIcons.user,
              primarySwatch,
            ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(
    String title,
    String content,
    IconData icon,
    Color color, {
    String? image,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withAlpha(30),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Center(
              child: image != null
                  ? CachedNetworkImage(
                      width: 24,
                      height: 24,
                      imageUrl: image,
                      placeholder: (context, url) =>
                          const CircularProgressIndicator(),
                      errorWidget: (context, url, error) => Icon(
                        icon,
                        size: 20,
                        color: color,
                      ),
                    )
                  : Icon(
                      icon,
                      size: 20,
                      color: color,
                    ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  text: title,
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                const SizedBox(height: 4),
                TextWidget(
                  text: content,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[800],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventDescription(BuildContext context) {
    final description = controller.event.description;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            text: 'Description'.tr,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
          const SizedBox(height: 12),
          TextWidget(
            text: description ?? '',
            fontSize: 16,
            color: Colors.grey[700],
          ),
        ],
      ),
    );
  }
}
