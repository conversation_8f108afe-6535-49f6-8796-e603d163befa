import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/all_events_controller.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/event_card.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AllEventsView extends GetView<AllEventsController> {
  const AllEventsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Directionality(
          textDirection: controller.appService.locale == const Locale('ar')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: Scaffold(
            appBar: mainAppBar(
                context: context,
                withBackButton: true,
                withDrawer: false,
                title: 'Events'.tr),
            body: controller.loading
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        const SizedBox(
                          height: 20,
                        ),
                        Expanded(
                          child: ListView.builder(
                              shrinkWrap: true,
                              scrollDirection: Axis.vertical,
                              itemCount: controller.events.length,
                              itemBuilder: (context, index) {
                                final item = controller.events[index];
                                return EventCard(
                                  width: MediaQuery.of(context).size.width - 90,
                                  event: item,
                                  onPressed: (event) => Get.toNamed(
                                      AppRoutes.eventDetails,
                                      arguments: event),
                                );
                              }),
                        )
                      ],
                    ),
                  ),
          ),
        ));
  }
}
