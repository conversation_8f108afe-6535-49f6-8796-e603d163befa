import 'dart:ui';

import 'package:get/get.dart';
import 'package:family_management/lang/ar.dart';
import 'package:family_management/lang/en.dart';

class LocalizationService extends Translations {
  static get locale {
    String lan = 'ar';
    return getLocaleFromLanguage(lan);
  }

  static const fallbackLocale = Locale('en', 'US');

  static final langs = [
    'English',
    'العربية',
  ];

  static final locales = [
    const Locale('en', 'US'),
    const Locale('ar'),
  ];

  @override
  Map<String, Map<String, String>> get keys => {'en_US': enUS, 'ar_AR': arAR};

  static void changeLocale(String lang) {
    final locale = getLocaleFromLanguage(lang);
    Get.updateLocale(locale);
    //StorageController().lang = lang;
  }

  static Locale getLocaleFromLanguage(String lang) {
    for (int i = 0; i < langs.length; i++) {
      if (lang == langs[i]) return locales[i];
    }
    return Get.locale ?? const Locale('ar');
  }
}
