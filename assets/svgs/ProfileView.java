import 'package:cached_network_image/cached_network_image.dart';
import 'package:family_management/config/app_routes.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/profile_controller.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../widget/custom_app_bar.dart';

class ProfileView extends GetView<ProfileController> {
  const ProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: controller.appService.locale == const Locale('ar')
          ? TextDirection.rtl
          : TextDirection.ltr,
      child: Scaffold(
        body: Column(
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                CustomAppBar(
                  title: 'The profile'.tr,
                  titleColor: const Color(0xFF134982),
                ),
                Positioned(
                  bottom: -40,
                  left: 0,
                  right: 0,
                  child: Center(child: _buildProfileImage()),
                ),
              ],
            ),
            const SizedBox(height: 60),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Column(
                  children: [
                    const SizedBox(height: 20),
                    _buildInfoSection(),
                    const SizedBox(height: 20),
                    _buildInterestsSection(),
                    const SizedBox(height: 20),
                    _buildFamilySection(),
                  ],
                ),
              ),
            ),
          ],
        ),
        floatingActionButton: Stack(
          clipBehavior: Clip.none,
          children: [
            // زر إضافة الملف الشخصي
            GestureDetector(
              onTap: () => Get.toNamed(AppRoutes.addProfile),
              child: Container(
                height: 60,
                width: 60,
                decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: const [
                      BoxShadow(
                        blurRadius: 1,
                        color: Color(0xff808080),
                      )
                    ],
                    shape: BoxShape.circle,
                    border: Border.all(
                        width: 1, color: pink.withValues(alpha: 0.5))),
                child: const Center(
                  child: Icon(Icons.add),
                ),
              ),
            ),
            // زر تعديل الملف الشخصي
            Positioned(
              bottom: 80, // جعل الزر فوق زر الإضافة
              right: 0,
              child: GestureDetector(
                onTap: () => Get.toNamed(AppRoutes.editProfile),
                child: Container(
                  height: 60,
                  width: 60,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: const [
                        BoxShadow(
                          blurRadius: 1,
                          color: Color(0xff808080),
                        )
                      ],
                      shape: BoxShape.circle,
                      border: Border.all(
                          width: 1, color: pink.withValues(alpha: 0.5))),
                  child: Center(
                    child: SvgPicture.asset(
                      'assets/svgs/edit-2.svg',
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileImage() {
    return Stack(
      alignment: Alignment.bottomLeft,
      children: [
        CircleAvatar(
          radius: 40,
          backgroundColor: Colors.white,
          child: ClipOval(
            child: Image.asset(
              'assets/images/pic.png',
              width: 80,
              height: 80,
              fit: BoxFit.cover,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 30.0, bottom: 0.0),
          child: SvgPicture.asset(
            'assets/svgs/edit-2.svg',
            width: 24,
            height: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoSection() {
    return Column(
      children: [
        _getRow('assets/svgs/profile_svgrepo.com.svg', 'Full name :'.tr, controller.user.name),
        const SizedBox(height: 15),
        _getRow('assets/svgs/email.svg', 'Your email :'.tr, controller.user.email),
        const SizedBox(height: 15),
        _getRow('assets/svgs/lock-security-secure-password-protect_svgrepo.com.svg', 'Your Password :'.tr, '************'),
        const SizedBox(height: 15),
        _getRow('assets/svgs/text-files_svgrepo.com.svg', 'Your Bio :'.tr, controller.user.bio),
      ],
    );
  }

  Widget _buildInterestsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            SvgPicture.asset(
              'assets/svgs/text-files_svgrepo.com.svg',
              width: 24,
              height: 20,
            ),
            const SizedBox(width: 6),
            _sectionTitle('Interests and hobbies'.tr),
          ],
        ),
        const SizedBox(height: 10),
        _bulletPoint('Reading books'.tr),
        _bulletPoint('Traveling and hiking'.tr),
        _bulletPoint('Photography and arts'.tr),
      ],
    );
  }

  Widget _buildFamilySection() {
    List<String> imagePaths = [
      'assets/images/pict1.png',
      'assets/images/pict2.png',
      'assets/images/pict3.png',
      'assets/images/pict1.png',
      'assets/images/pict2.png',
      'assets/images/pict3.png',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            SvgPicture.asset(
              'assets/svgs/profile_svgrepo.com.svg',
              width: 24,
              height: 24,
            ),
            const SizedBox(width: 8),
            _sectionTitle('Our family'.tr),
          ],
        ),
        const SizedBox(height: 10),
        SizedBox(
          height: 50,
          child: Stack(
            clipBehavior: Clip.none,
            children: List.generate(
              imagePaths.length,
                  (index) => Positioned(
                left: index * 25,
                child: CircleAvatar(
                  radius: 18,
                  backgroundColor: Colors.white,
                  child: ClipOval(
                    child: Image.asset(
                      imagePaths[index],
                      width: 44,
                      height: 44,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _getRow(String iconPath, String title, String? subtitle) {
    return Row(
      children: [
        SvgPicture.asset(
          iconPath,
          width: 20,
          height: 20,
          color: const Color(0xFF134982),
        ),
        const SizedBox(width: 10),
        TextWidget(
          text: title,
          fontSize: 14,
          color: const Color(0xff808080),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: TextWidget(
            text: subtitle ?? '',
            fontSize: 12,
            color: const Color(0xFF134982),
          ),
        ),
      ],
    );
  }

  Widget _sectionTitle(String title) {
    return TextWidget(
      text: title,
      fontSize: 16,
      color: const Color(0xff808080),
    );
  }

  Widget _bulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(left: 15, bottom: 5),
      child: Row(
        children: [
          const Icon(Icons.circle, size: 6, color: Colors.pinkAccent),
          const SizedBox(width: 5),
          TextWidget(
            text: text,
            fontSize: 12,
            color: const Color(0xFF134982),
          ),
        ],
      ),
    );
  }
}
